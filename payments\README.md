# Payments Application

## Overview
The Payments application handles all payment-related functionality for the MFA Backend, including processing membership dues, event registrations, and donations. It integrates with PayPal for payment processing and provides a comprehensive system for tracking and managing financial transactions.

## Directory Structure
- **models/**: Database models for payments and transactions
- **views/**: API views for payment processing and management
- **migrations/**: Database migrations
- **serializers/**: Data serializers for payment-related API requests/responses

## Key Models

### Payment
Represents a payment transaction:
- Amount and currency
- Payment method (PayPal, check, cash)
- Status (pending, completed, failed)
- Associated member
- Payment date
- Invoice number
- Payment type (membership, event, donation)

### Invoice
Represents an invoice:
- Invoice number
- Items and amounts
- Due date
- Status (paid, unpaid, partial)
- Associated member

## Payment Processing Flow
1. Payment request is created
2. User is redirected to payment provider (PayPal)
3. After payment, user is redirected back to the application
4. Payment status is verified and updated
5. Receipt is generated and sent to the user

## API Endpoints

### Payment Management
- `/api/payments/`: List/create payments
- `/api/payments/:id/`: Retrieve/update payment details
- `/api/payments/process/`: Process a new payment
- `/api/payments/verify/`: Verify payment status
- `/api/payments/member/:id/`: Get payments for a specific member

### PayPal Integration
- `/api/payments/paypal/create/`: Create PayPal payment
- `/api/payments/paypal/execute/`: Execute PayPal payment
- `/api/payments/paypal/webhook/`: Handle PayPal webhooks

## Payment Reports
The application provides various reports for financial tracking:
- Payment summaries by date range
- Member payment history
- Department payment summaries
- Outstanding invoices

## Receipt Generation
Automatic receipt generation for completed payments with email delivery.

## Security Considerations
- All payment data is validated and sanitized
- Secure communication with payment providers
- Audit logging for all payment transactions
- Proper error handling for failed payments

## Webhook Handling
The application processes webhooks from payment providers to update payment statuses asynchronously.

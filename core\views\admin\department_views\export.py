from django.http import HttpResponse
from rest_framework import status
from django_filters import rest_framework as filters
import openpyxl
from openpyxl.styles import <PERSON>ont, Ali<PERSON>ment, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO
from django.db.models import Count

from common.utils import track_activity
from common.views import BaseAPIView, APIResponse
from common.permissions import IsStaffUser
from common.filters import DepartmentFilter
from core.models import Department


class DepartmentExportView(BaseAPIView):
    """
    Export departments to Excel with filtering and no pagination (admin only)
    """
    permission_classes = [IsStaffUser]
    filter_backends = (filters.DjangoFilterBackend,)
    pagination_class = None
    filterset_class = DepartmentFilter

    def get_queryset(self):
        """Get the queryset for departments with member count"""
        queryset = Department.objects.annotate(member_count=Count('members')).order_by('name')
        
        # Check for ids_list parameter
        ids_list = self.request.query_params.get('ids_list', None)
        if ids_list:
            # Split the comma-separated list and convert to integers
            try:
                ids = [int(id.strip()) for id in ids_list.split(',') if id.strip()]
                queryset = queryset.filter(id__in=ids)
            except ValueError:
                # If conversion fails, return empty queryset
                return Department.objects.none()
                
        return queryset

    def filter_queryset(self, queryset):
        """Apply filters to the queryset"""
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    @track_activity(description="Exported departments list")
    def get(self, request, *args, **kwargs):
        """
        Export departments to Excel with filters applied and no pagination
        
        Optional query parameters:
        - ids_list: Comma-separated list of department IDs to include in the export
        """
        # Get filtered queryset
        queryset = self.filter_queryset(self.get_queryset())
        
        try:
            # Create a new workbook and select the active worksheet
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Departments"

            # Define column headers
            headers = [
                'ID', 'Department Name', 'Member Count'
            ]

            # Add headers to the worksheet
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.value = header
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
                cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")

            # Add data to the worksheet
            for row_num, department in enumerate(queryset, 2):
                # ID
                worksheet.cell(row=row_num, column=1).value = department.id
                # Department Name
                worksheet.cell(row=row_num, column=2).value = department.name
                # Member Count
                worksheet.cell(row=row_num, column=3).value = department.member_count

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = (max_length + 2) if max_length < 50 else 50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Create a BytesIO object to save the workbook to
            excel_file = BytesIO()
            workbook.save(excel_file)
            excel_file.seek(0)

            # Create the HttpResponse with the Excel file
            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            # Set headers to force download
            response['Content-Disposition'] = 'attachment; filename=departments_export.xlsx'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition'
            # Disable caching to ensure fresh download each time
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

            return response
            
        except Exception as e:
            return APIResponse(
                message=f"Failed to generate export: {str(e)}",
                success=False,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 
"""
Utility functions for generating PDF documents
"""
import os
from datetime import datetime
from io import BytesIO
import tempfile

from django.template.loader import get_template
from django.conf import settings
from django.http import HttpResponse
from weasyprint import HTML, CSS


def generate_invoice_pdf(template_src, context_dict=None, filename="invoice.pdf"):
    """
    Generate PDF using WeasyPrint for high-quality rendering
    
    Args:
        template_src (str): Path to HTML template
        context_dict (dict): Context data for template rendering
        filename (str): Name of the PDF file
        
    Returns:
        HttpResponse: PDF response
    """
    if context_dict is None:
        context_dict = {}
        
    template = get_template(template_src)
    html_string = template.render(context_dict)
    
    # Create a temporary HTML file
    with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as temp_html:
        temp_html.write(html_string.encode('utf-8'))
        temp_html_path = temp_html.name
    
    # Generate PDF from HTML
    pdf_file = BytesIO()
    HTML(filename=temp_html_path).write_pdf(
        pdf_file,
        stylesheets=[CSS(string='@page { size: letter; margin: 1cm; }')]
    )
    
    # Clean up temporary file
    os.unlink(temp_html_path)
    
    # Create HTTP response with PDF
    response = HttpResponse(pdf_file.getvalue(), content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    return response

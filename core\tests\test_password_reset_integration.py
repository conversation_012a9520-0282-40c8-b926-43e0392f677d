"""
Integration tests for the password reset functionality.

This module contains comprehensive integration tests for the entire password reset flow,
testing how different components interact with each other, including:
- Serializers
- Views
- Models
- Email utilities
"""
import uuid
from datetime import timedelta
from unittest.mock import patch, MagicMock

from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core import mail
from rest_framework.test import APIClient
from rest_framework import status

from common.models import PasswordReset
from common.utils.email import send_password_reset_email, verify_password_reset_token
from core.serializers.auth.password_reset import (
    RequestPasswordResetSerializer,
    ValidateResetTokenSerializer,
    PasswordResetConfirmSerializer
)

User = get_user_model()


class PasswordResetEndToEndTests(TestCase):
    """End-to-end tests for the password reset flow."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.request_url = reverse('core:request-password-reset')
        self.validate_url = reverse('core:validate-reset-token')
        self.reset_url = reverse('core:reset-password')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

    @patch('common.utils.email.send_mail')  # Mock email sending
    def test_complete_password_reset_flow(self, mock_send_mail):
        """Test the complete password reset flow from request to confirmation."""
        # Initial check - no reset tokens exist
        self.assertEqual(PasswordReset.objects.count(), 0)

        # Step 1: Request password reset
        request_response = self.client.post(self.request_url, {'email': '<EMAIL>'})
        self.assertEqual(request_response.status_code, status.HTTP_200_OK)

        # Check that a reset token was created
        self.assertEqual(PasswordReset.objects.count(), 1)
        reset = PasswordReset.objects.first()
        self.assertEqual(reset.user, self.user)
        self.assertFalse(reset.used)

        # Check that an email would have been sent
        self.assertTrue(mock_send_mail.called)

        # Step 2: Validate the token
        validate_response = self.client.post(self.validate_url, {'token': reset.key})
        self.assertEqual(validate_response.status_code, status.HTTP_200_OK)
        self.assertEqual(validate_response.data['data']['email'], '<EMAIL>')

        # Check that token is still not used
        reset.refresh_from_db()
        self.assertFalse(reset.used)

        # Step 3: Reset the password
        reset_response = self.client.post(self.reset_url, {
            'token': reset.key,
            'new_password': 'NewSecurePassword123!',
            'confirm_password': 'NewSecurePassword123!'
        })
        self.assertEqual(reset_response.status_code, status.HTTP_200_OK)

        # Check that password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewSecurePassword123!'))

        # Check that token was marked as used
        reset.refresh_from_db()
        self.assertTrue(reset.used)

        # Step 4: Try to use the token again (should fail)
        reuse_response = self.client.post(self.reset_url, {
            'token': reset.key,
            'new_password': 'AnotherPassword123!',
            'confirm_password': 'AnotherPassword123!'
        })
        self.assertEqual(reuse_response.status_code, status.HTTP_400_BAD_REQUEST)

        # Password should not have changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewSecurePassword123!'))
        self.assertFalse(self.user.check_password('AnotherPassword123!'))


class PasswordResetSerializerIntegrationTests(TestCase):
    """Integration tests for the password reset serializers."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

    @patch('common.utils.email.send_mail')
    def test_serializer_chain(self, mock_send_mail):
        """Test all three serializers in sequence."""
        # Step 1: Request password reset
        request_serializer = RequestPasswordResetSerializer(data={'email': '<EMAIL>'})
        self.assertTrue(request_serializer.is_valid())
        request_serializer.save()

        # Check that a reset token was created
        reset = PasswordReset.objects.first()
        self.assertIsNotNone(reset)
        self.assertEqual(reset.user, self.user)
        self.assertFalse(reset.used)

        # Step 2: Validate the token
        validate_serializer = ValidateResetTokenSerializer(data={'token': reset.key})
        self.assertTrue(validate_serializer.is_valid())
        self.assertEqual(validate_serializer.context['user'], self.user)

        # Step 3: Reset the password
        confirm_serializer = PasswordResetConfirmSerializer(data={
            'token': reset.key,
            'new_password': 'NewSecurePassword123!',
            'confirm_password': 'NewSecurePassword123!'
        })
        self.assertTrue(confirm_serializer.is_valid())

        # Save the new password
        user = confirm_serializer.save()

        # Check that password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewSecurePassword123!'))

        # Check that token was marked as used
        reset.refresh_from_db()
        self.assertTrue(reset.used)


class PasswordResetUtilsIntegrationTests(TestCase):
    """Integration tests for the password reset utility functions."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

    @patch('common.utils.email.send_mail')
    def test_utils_integration(self, mock_send_mail):
        """Test the integration of utility functions."""
        # Create a reset token using the utility function
        reset = send_password_reset_email(self.user)

        # Check that the token was created
        self.assertIsNotNone(reset)
        self.assertEqual(reset.user, self.user)
        self.assertFalse(reset.used)

        # Verify the token using the utility function
        user, error = verify_password_reset_token(reset.key)

        # Check verification results
        self.assertEqual(user, self.user)
        self.assertIsNone(error)

        # Mark token as used
        reset.used = True
        reset.save()

        # Verify the token again, should fail
        user, error = verify_password_reset_token(reset.key)
        self.assertIsNone(user)
        self.assertEqual(error, "This password reset link has already been used")


class PasswordResetRaceConditionTests(TransactionTestCase):
    """Tests for race conditions in the password reset process."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.request_url = reverse('core:request-password-reset')
        self.reset_url = reverse('core:reset-password')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

    @patch('common.utils.email.send_mail')
    def test_multiple_concurrent_requests(self, mock_send_mail):
        """Test handling multiple reset requests in quick succession."""
        # Send multiple reset requests
        for _ in range(3):
            response = self.client.post(self.request_url, {'email': '<EMAIL>'})
            self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that only one active token exists
        active_tokens = PasswordReset.objects.filter(
            user=self.user,
            used=False,
            expires_at__gt=timezone.now()
        ).count()
        self.assertEqual(active_tokens, 1)

        # All but the latest token should be marked as used
        total_tokens = PasswordReset.objects.filter(user=self.user).count()
        used_tokens = PasswordReset.objects.filter(user=self.user, used=True).count()
        self.assertEqual(total_tokens, 3)
        self.assertEqual(used_tokens, 2)

    @patch('common.utils.email.send_mail')
    def test_concurrent_password_resets(self, mock_send_mail):
        """Test two password resets with the same token in quick succession."""
        # Request password reset
        self.client.post(self.request_url, {'email': '<EMAIL>'})

        # Get the token
        reset = PasswordReset.objects.get(user=self.user, used=False)

        # Simulate two concurrent password resets
        with patch('core.serializers.auth.password_reset.verify_password_reset_token') as mock_verify:
            # For the first call, return valid user
            # For the second call, return error (token already used)
            mock_verify.side_effect = [
                (self.user, None),  # First call - token is valid
                (None, "This password reset link has already been used")  # Second call - token is used
            ]

            # First reset (should succeed)
            response1 = self.client.post(self.reset_url, {
                'token': reset.key,
                'new_password': 'FirstPassword123!',
                'confirm_password': 'FirstPassword123!'
            })
            self.assertEqual(response1.status_code, status.HTTP_200_OK)

            # Second reset with same token (should fail)
            response2 = self.client.post(self.reset_url, {
                'token': reset.key,
                'new_password': 'SecondPassword123!',
                'confirm_password': 'SecondPassword123!'
            })
            self.assertEqual(response2.status_code, status.HTTP_400_BAD_REQUEST)

        # Check that only the first password was set
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('FirstPassword123!'))
        self.assertFalse(self.user.check_password('SecondPassword123!'))


class PasswordResetEmailTests(TestCase):
    """Tests for email functionality in the password reset process."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

    def test_password_reset_email_content(self):
        """Test the content of the password reset email."""
        # Clear the mail outbox
        mail.outbox = []

        # Send password reset email
        reset = send_password_reset_email(self.user)

        # Check that the email was sent
        self.assertEqual(len(mail.outbox), 1)

        # Get the email
        email = mail.outbox[0]

        # Check email properties
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertIn('Reset your password', email.subject)

        # Check that the email contains the reset URL
        self.assertIn(str(reset.key), email.body)

        # Check that the email contains the user's name
        self.assertIn('Test User', email.body)

        # Check expiration information
        self.assertIn('expire', email.body.lower())


class PasswordResetErrorHandlingIntegrationTests(TestCase):
    """Integration tests for error handling in the password reset process."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.request_url = reverse('core:request-password-reset')
        self.validate_url = reverse('core:validate-reset-token')
        self.reset_url = reverse('core:reset-password')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

        # Create tokens with different statuses
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        self.expired_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(minutes=1)
        )

        self.used_reset = PasswordReset.objects.create(
            user=self.user,
            used=True,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_handling_multiple_error_scenarios(self):
        """Test handling of various error scenarios."""
        test_cases = [
            # (scenario_name, url, data, expected_status)

            # Invalid request cases
            ('empty_request', self.request_url, {}, status.HTTP_400_BAD_REQUEST),
            ('invalid_email', self.request_url, {'email': 'not-an-email'}, status.HTTP_400_BAD_REQUEST),

            # Invalid token validation cases
            ('empty_token_validation', self.validate_url, {}, status.HTTP_400_BAD_REQUEST),
            ('invalid_token_format', self.validate_url, {'token': 'not-a-uuid'}, status.HTTP_400_BAD_REQUEST),
            ('expired_token_validation', self.validate_url, {'token': self.expired_reset.key}, status.HTTP_400_BAD_REQUEST),
            ('used_token_validation', self.validate_url, {'token': self.used_reset.key}, status.HTTP_400_BAD_REQUEST),

            # Invalid password reset cases
            ('empty_reset', self.reset_url, {}, status.HTTP_400_BAD_REQUEST),
            ('missing_new_password', self.reset_url, {'token': self.valid_reset.key, 'confirm_password': 'Password123!'}, status.HTTP_400_BAD_REQUEST),
            ('missing_confirm_password', self.reset_url, {'token': self.valid_reset.key, 'new_password': 'Password123!'}, status.HTTP_400_BAD_REQUEST),
            ('passwords_dont_match', self.reset_url, {
                'token': self.valid_reset.key,
                'new_password': 'Password123!',
                'confirm_password': 'DifferentPassword123!'
            }, status.HTTP_400_BAD_REQUEST),
            ('weak_password', self.reset_url, {
                'token': self.valid_reset.key,
                'new_password': 'password',
                'confirm_password': 'password'
            }, status.HTTP_400_BAD_REQUEST),
            ('expired_token_reset', self.reset_url, {
                'token': self.expired_reset.key,
                'new_password': 'Password123!',
                'confirm_password': 'Password123!'
            }, status.HTTP_400_BAD_REQUEST),
            ('used_token_reset', self.reset_url, {
                'token': self.used_reset.key,
                'new_password': 'Password123!',
                'confirm_password': 'Password123!'
            }, status.HTTP_400_BAD_REQUEST),
        ]

        for scenario, url, data, expected_status in test_cases:
            with self.subTest(scenario=scenario):
                response = self.client.post(url, data)
                self.assertEqual(response.status_code, expected_status)

                # For error cases, password should not have changed
                if url == self.reset_url and expected_status != status.HTTP_200_OK:
                    self.user.refresh_from_db()
                    self.assertTrue(self.user.check_password('oldpassword123'))
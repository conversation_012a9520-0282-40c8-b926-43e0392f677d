import csv
from datetime import datetime
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.utils import IntegrityError
from django.utils import timezone
from zoneinfo import ZoneInfo
from core.models import Member, Department

class Command(BaseCommand):
    help = 'Import members from CSV file'

    def add_arguments(self, parser):
        parser.add_argument('csv_file', type=str, help='Path to the CSV file')

    def validate_row(self, row, row_num):
        """Validate row data before processing"""
        errors = []
        # Add any other validations here if needed
        return errors

    def get_formatted_name(self, first_name, last_name):
        """Format name with fallback to Unknown if both are empty"""
        if not first_name and not last_name:
            return "Unknown"
        return f"{first_name or ''} {last_name or ''}".strip()

    def parse_date(self, date_str):
        """
        Parse date string in multiple formats and make it timezone-aware
        Handles:
        - MM-DD-YY 0:00
        - MM-DD-YYYY 12:00:00 AM
        - M-D-YYYY 12:00:00 AM
        - M-D-YY 0:00
        """
        if not date_str:
            return None
            
        date_str = date_str.strip()
        
        # List of possible date formats
        date_formats = [
            '%m-%d-%y %H:%M',           # 11-04-34 0:00
            '%m-%d-%Y %H:%M',           # 11-04-1934 0:00
            '%m-%d-%Y %I:%M:%S %p',     # 1-22-1990 12:00:00 AM
            '%m-%d-%y %I:%M:%S %p',     # 1-22-90 12:00:00 AM
            '%-m-%-d-%Y %I:%M:%S %p',   # Single digit month/day
            '%-m-%-d-%y %H:%M',         # Single digit month/day with 2-digit year
            '%m/%d/%Y %H:%M',           # Also handle possible forward slash format
            '%m/%d/%y %H:%M'
        ]
        
        # Try each format until one works
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                
                # Handle two-digit years
                if fmt.endswith('%y %H:%M') or fmt.endswith('%y %I:%M:%S %p'):
                    year = parsed_date.year
                    # If year is greater than current year's last two digits,
                    # assume it's from previous century
                    current_year = datetime.now().year % 100
                    if year > current_year:
                        parsed_date = parsed_date.replace(year=parsed_date.year + 1900)
                    else:
                        parsed_date = parsed_date.replace(year=parsed_date.year + 2000)
                
                # Make the date timezone-aware using US/Central timezone
                central_tz = ZoneInfo('America/Chicago')
                aware_date = timezone.make_aware(parsed_date, timezone=central_tz)
                return aware_date
            except ValueError:
                continue
                
        return None

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        all_member_data = []  # Store all member data before processing
        validation_errors = []
        
        # First pass: Read and validate all data
        self.stdout.write(self.style.WARNING("Reading and validating CSV data..."))
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row_num, row in enumerate(reader, start=1):
                    # Validate row
                    row_errors = self.validate_row(row, row_num)
                    if row_errors:
                        validation_errors.extend(row_errors)
                        continue
                        
                    # Prepare member data
                    member_data = {
                        'name': self.get_formatted_name(row.get('First Name', ''), row.get('Last Name', '')),
                        'dst': row.get('District', ''),
                        'county': row.get('county', ''),
                        'account': row.get('Account', ''),
                        'lead_status': row.get('Lead Status', '').lower(),
                        'address': ', '.join(filter(None, [row.get('Address 1', ''), row.get('Address 2', '')])),
                        'city': row.get('City', ''),
                        'st': row.get('State', 'MS'),
                        'zip_code': row.get('Zip', ''),
                        'home_phone': row.get('Phone', ''),
                        'role': row.get('Role', '').lower() if row.get('Role') else 'volunteer',
                        'gender': row.get('Gender', '').lower() if row.get('Gender') else 'prefer_not_to_say',
                    }
                    
                    # Handle DOB with new parser
                    if row.get('DOB'):
                        parsed_date = self.parse_date(row['DOB'])
                        if parsed_date:
                            member_data['dob'] = parsed_date
                        else:
                            self.stdout.write(self.style.WARNING(
                                f"Row {row_num}: Could not parse date format - {row['DOB']}, skipping DOB"
                            ))
                    
                    # Handle email
                    email = row.get('Email', '').strip()
                    if not email:
                        base_email = f"member_{row_num}@test.com"
                        counter = 1
                        temp_email = base_email
                        while any(d.get('email') == temp_email for d in all_member_data):
                            temp_email = f"member_{row_num}_{counter}@test.com"
                            counter += 1
                        email = temp_email
                    member_data['email'] = email
                    
                    all_member_data.append(member_data)
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error reading CSV file: {str(e)}"))
            return
        
        # If there are validation errors, show them and exit
        if validation_errors:
            self.stdout.write(self.style.ERROR("\nValidation errors found:"))
            for error in validation_errors:
                self.stdout.write(self.style.ERROR(error))
            self.stdout.write(self.style.ERROR("\nNo data was imported due to validation errors."))
            return
        
        # Second pass: Process all data in a single transaction
        self.stdout.write(self.style.WARNING("\nImporting data..."))
        created_count = 0
        updated_count = 0
        unknown_name_count = 0
        skipped_dob_count = 0
        
        try:
            with transaction.atomic():
                for member_data in all_member_data:
                    try:
                        if member_data['name'] == 'Unknown':
                            unknown_name_count += 1
                        if 'dob' not in member_data and row.get('DOB'):
                            skipped_dob_count += 1
                            
                        member, created = Member.objects.update_or_create(
                            email=member_data['email'],
                            defaults=member_data
                        )
                        
                        if created:
                            created_count += 1
                        else:
                            updated_count += 1
                            
                    except IntegrityError as e:
                        raise Exception(f"Database integrity error for email {member_data['email']}: {str(e)}")
                    except Exception as e:
                        raise Exception(f"Error processing member {member_data['email']}: {str(e)}")
                
                # If we got here, all records were processed successfully
                self.stdout.write(self.style.SUCCESS(
                    f'\nImport completed successfully:\n'
                    f'Total rows processed: {len(all_member_data)}\n'
                    f'Members created: {created_count}\n'
                    f'Members updated: {updated_count}\n'
                    f'Members with unknown names: {unknown_name_count}\n'
                    f'Dates that could not be parsed: {skipped_dob_count}'
                ))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(
                f'\nError during import - no data was imported:\n{str(e)}'
            ))
            return 
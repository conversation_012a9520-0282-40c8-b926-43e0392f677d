"""
Serializers for Event models.
"""
from rest_framework import serializers
from core.models import Event
from core.serializers.event_config_serializer import EventConfigSerializer


class EventSerializer(serializers.ModelSerializer):
    """
    Serializer for Event model - used for list views
    """
    total_registrations = serializers.IntegerField(read_only=True)
    is_late_registration = serializers.BooleanField(read_only=True)
    spots_remaining = serializers.IntegerField(read_only=True)
    is_at_capacity = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Event
        fields = [
            'id', 'event_name', 'event_date', 'event_end_date', 'event_location',
            'event_description', 'registration_fee_normal', 'registration_fee_late',
            'guest_fee', 'late_registration_date', 'max_participants',
            'total_registrations', 'spots_remaining', 'created_at', 'updated_at', 
            'is_active', 'is_late_registration', 'is_at_capacity', 'config'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class EventDetailSerializer(EventSerializer):
    """
    Serializer for Event model - used for detail views
    Includes more information than the list serializer
    """
    config_details = EventConfigSerializer(source='get_effective_config', read_only=True)
    
    class Meta(EventSerializer.Meta):
        fields = EventSerializer.Meta.fields + ['config_details']


class EventCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for Event model - used for create/update operations
    """
    class Meta:
        model = Event
        fields = [
            'event_name', 'event_date', 'event_end_date', 'event_location',
            'event_description', 'registration_fee_normal', 'registration_fee_late',
            'guest_fee', 'late_registration_date', 'max_participants', 'is_active',
            'config'
        ]
        
    def validate(self, data):
        """
        Validate that end date is after start date if both are provided
        """
        if data.get('event_date') and data.get('event_end_date'):
            if data['event_end_date'] < data['event_date']:
                raise serializers.ValidationError({
                    'event_end_date': 'End date must be after start date'
                })
                
        if data.get('event_date') and data.get('late_registration_date'):
            if data['late_registration_date'] > data['event_date']:
                raise serializers.ValidationError({
                    'late_registration_date': 'Late registration date must be before event date'
                })
                
        return data

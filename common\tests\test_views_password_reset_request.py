"""
Tests for the password reset request view in the common app.

This module contains comprehensive tests for the RequestPasswordResetView class.
"""
from unittest.mock import patch

from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

# Use the view from core not common
from core.views import RequestPasswordResetView

User = get_user_model()


class RequestPasswordResetViewTests(TestCase):
    """Test cases for the RequestPasswordResetView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )

        # URL for requesting password reset - include the namespace 'core:'
        self.url = reverse('core:request-password-reset')

    @patch('core.serializers.auth.password_reset.RequestPasswordResetSerializer.save')
    def test_request_password_reset_success(self, mock_save):
        """Test successful password reset request."""
        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('will receive password reset instructions', response.data['message'])

        # Check that save method was called
        mock_save.assert_called_once()

    def test_request_password_reset_nonexistent_user(self):
        """Test password reset request for nonexistent user."""
        # Make the request with a nonexistent email
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should still return 200 for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('will receive password reset instructions', response.data['message'])

    def test_request_password_reset_missing_email(self):
        """Test password reset request with missing email."""
        # Make the request without an email
        response = self.client.post(self.url, {})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid request', response.data['message'])

    def test_request_password_reset_empty_email(self):
        """Test password reset request with an empty email."""
        # Make the request with an empty email
        response = self.client.post(self.url, {'email': ''})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid request', response.data['message'])

    def test_request_password_reset_invalid_email_format(self):
        """Test password reset request with an invalid email format."""
        # Make the request with an invalid email format
        response = self.client.post(self.url, {'email': 'not-an-email'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid request', response.data['message'])

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_activity_tracking(self, mock_create_activity):
        """Test that user activity is tracked for password reset request."""
        # Make the request
        self.client.post(self.url, {'email': '<EMAIL>'})

        # Check if activity tracking was called
        # Note: This might not be called because the user might not be authenticated
        # in the request context during the test
        mock_create_activity.assert_not_called()

    def test_request_password_reset_method_not_allowed(self):
        """Test that only POST method is allowed for password reset request."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response - should return 405 Method Not Allowed
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_request_password_reset_with_uppercase_email(self):
        """Test password reset request with uppercase email."""
        # Make the request with uppercase email
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should still work with case-insensitive email matching
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('will receive password reset instructions', response.data['message'])

    def test_request_password_reset_with_whitespace_in_email(self):
        """Test password reset request with whitespace in email."""
        # Make the request with whitespace in email
        response = self.client.post(self.url, {'email': ' <EMAIL> '})

        # Check the response - should still work with trimmed email
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('will receive password reset instructions', response.data['message'])

    def test_request_password_reset_with_json_content_type(self):
        """Test password reset request with JSON content type."""
        # Make the request with JSON content type
        response = self.client.post(
            self.url,
            {'email': '<EMAIL>'},
            format='json'
        )

        # Check the response - should work with JSON content type
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('will receive password reset instructions', response.data['message'])

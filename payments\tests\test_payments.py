"""
Tests for the payments application.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from payments.models import Payment
from core.models import Member

class PaymentTests(TestCase):
    """Test cases for payment-related functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a test member
        self.member = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test Member'
        )

        # Payment data
        self.payment_data = {
            'member': self.member.id,
            'amount': '100.00',
            'payment_method': 'paypal',
            'status': 'pending',
            'payment_type': 'membership'
        }

    def test_create_payment(self):
        """Test creating a payment."""
        url = reverse('payments:create-payment')
        self.client.force_authenticate(user=self.member)
        response = self.client.post(url, self.payment_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Payment.objects.count(), 1)
        payment = Payment.objects.first()
        self.assertEqual(payment.amount, 100.00)
        self.assertEqual(payment.payment_method, 'paypal')

    def test_payment_list(self):
        """Test listing payments."""
        Payment.objects.create(
            member=self.member,
            amount=100.00,
            payment_method='paypal',
            status='completed',
            payment_type='membership'
        )

        url = reverse('payments:payment-list')
        self.client.force_authenticate(user=self.member)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_payment_detail(self):
        """Test retrieving a payment detail."""
        payment = Payment.objects.create(
            member=self.member,
            amount=100.00,
            payment_method='paypal',
            status='completed',
            payment_type='membership'
        )

        url = reverse('payments:payment-detail', args=[payment.id])
        self.client.force_authenticate(user=self.member)
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['amount'], '100.00')
"""
Tests for the core app authentication views.
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from django.contrib.auth import get_user_model
from common.models import EmailVerification, PasswordReset
from common.views import APIResponse
import uuid
import unittest.mock as mock

User = get_user_model()




class RegisterViewTests(TestCase):
    """Test cases for the Register view."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.register_url = reverse('core:register')

        # Valid registration data
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'password_confirm': 'securepassword123',
            'name': 'New User'
        }

        # Create an existing user for duplication tests
        self.existing_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Existing User'
        )

    @patch('common.utils.email.send_mail')
    def test_successful_registration(self, mock_send_mail):
        """Test successful registration flow."""
        with patch('common.utils.email.send_mail'):
            data = {
                'email': '<EMAIL>',
                'name': 'New User',
                'password': 'StrongP@ssw0rd123!',
                'confirm_password': 'StrongP@ssw0rd123!'
            }
            response = self.client.post(self.register_url, data, format='json')

            # Check response
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertEqual(response.data['success'], True)
            self.assertIn('verification', response.data['message'].lower())

            # Check that user was created
            self.assertTrue(User.objects.filter(email='<EMAIL>').exists())

            # Check that email verification was created
            user = User.objects.get(email='<EMAIL>')
            self.assertTrue(EmailVerification.objects.filter(user=user).exists())

    def test_duplicate_email(self):
        """
        Test registration with duplicate email.
        """
        # Create a user first
        duplicate_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Original User',
            active=True
        )

        # Try to register with the same email
        data = {
            'email': '<EMAIL>',  # Same as the user created above
            'name': 'Duplicate Test User',
            'password': 'StrongP@ssw0rd123!',
            'confirm_password': 'StrongP@ssw0rd123!'
        }

        response = self.client.post(self.register_url, data)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('already', response.data['message'].lower())

    def test_missing_fields(self):
        """Test registration with missing required fields."""
        # Missing email
        missing_email = self.valid_data.copy()
        missing_email.pop('email')

        response = self.client.post(self.register_url, missing_email, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)

        # Missing password
        missing_password = self.valid_data.copy()
        missing_password.pop('password')

        response = self.client.post(self.register_url, missing_password, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)

        # Missing name
        missing_name = self.valid_data.copy()
        missing_name.pop('name')

        response = self.client.post(self.register_url, missing_name, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)

    def test_invalid_inputs(self):
        """Test registration with invalid inputs."""
        # Invalid email format
        invalid_email = self.valid_data.copy()
        invalid_email['email'] = 'not-an-email'

        response = self.client.post(self.register_url, invalid_email, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)

        # Password too short
        short_password = self.valid_data.copy()
        short_password['password'] = 'short'
        short_password['password_confirm'] = 'short'

        response = self.client.post(self.register_url, short_password, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)

        # Password mismatch
        password_mismatch = self.valid_data.copy()
        password_mismatch['password_confirm'] = 'differentpassword123'

        response = self.client.post(self.register_url, password_mismatch, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)


class LoginViewTests(APITestCase):
    """Test cases for the Login view."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.login_url = reverse('core:login')
        self.refresh_url = reverse('token_refresh')

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Test User',
            active=True,  # Match the model field
        )

    def test_valid_login(self):
        """
        Test successful login with valid credentials for a verified user.
        """
        self.user.is_email_verified = True  # User must be verified
        self.user.is_staff = True  # User must be staff to login
        self.user.save()
        data = {'email': '<EMAIL>', 'password': 'password123'}
        response = self.client.post(self.login_url, data)
        # Check success response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('access', response.data['data'])
        self.assertIn('refresh', response.data['data'])
        self.assertIn('user', response.data['data'])

    def test_invalid_password(self):
        """
        Test login attempt with an incorrect password.
        """
        self.user.is_email_verified = True  # User must be verified to reach password check
        self.user.is_staff = True  # User must be staff
        self.user.save()
        data = {'email': '<EMAIL>', 'password': 'wrongpassword'}
        response = self.client.post(self.login_url, data)
        # Serializer validation fails -> 400 Bad Request
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid credentials', response.data['message'].lower())

    def test_inactive_user(self):
        """
        Test login attempt for an inactive user.
        """
        self.user.active = False  # Use 'active' field instead of 'is_active'
        self.user.is_email_verified = True  # Verification status doesn't matter if inactive
        self.user.is_staff = True  # User must be staff
        self.user.save()
        data = {'email': '<EMAIL>', 'password': 'password123'}
        response = self.client.post(self.login_url, data)
        # Serializer validation fails -> 400 Bad Request
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('awaiting approval', response.data['message'].lower())

    def test_nonexistent_user(self):
        """
        Test login attempt with an email that does not exist.
        """
        data = {'email': '<EMAIL>', 'password': 'password123'}
        response = self.client.post(self.login_url, data)
        # Serializer validation fails -> 400 Bad Request
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid credentials', response.data['message'].lower())

    def test_unverified_user_login(self):
        """
        Test login attempt for an unverified user.
        """
        # User exists but is_email_verified is False (default)
        self.user.is_staff = True  # User must be staff
        self.user.save()
        data = {'email': '<EMAIL>', 'password': 'password123'}
        response = self.client.post(self.login_url, data)
        # Serializer validation fails -> 400 Bad Request
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('email has not been verified', response.data['message'].lower())

    def test_token_rotation(self):
        """
        Test that access tokens can be refreshed using a refresh token.
        """
        # First, we need to login to get the refresh token
        self.user.is_email_verified = True
        self.user.is_staff = True  # User must be staff to login
        self.user.save()

        login_data = {'email': '<EMAIL>', 'password': 'password123'}
        login_response = self.client.post(self.login_url, login_data)
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)

        refresh_token = login_response.data['data']['refresh']

        # Now use the refresh token to get a new access token
        first_response = self.client.post(
            self.refresh_url,
            {'refresh': refresh_token}
        )

        # Check success response
        self.assertEqual(first_response.status_code, status.HTTP_200_OK)
        self.assertIn('access', first_response.data['data'])


class PasswordResetViewsTests(APITestCase):
    """Test cases for password reset views."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Test User',
            active=True,
        )

        # URLs
        self.request_url = reverse('core:request-password-reset')
        self.validate_url = reverse('core:validate-reset-token')
        self.reset_url = reverse('core:reset-password')

        # Setup mock for verify_password_reset_token
        self.patcher = mock.patch('common.utils.email.verify_password_reset_token')
        self.mock_verify = self.patcher.start()
        # Default behavior is to return the user and no error
        self.mock_verify.return_value = (self.user, None)

    def tearDown(self):
        """Clean up after tests."""
        self.patcher.stop()

    def test_request_password_reset_valid_email(self):
        """
        Test requesting password reset with valid email.
        """
        with mock.patch('core.serializers.auth.password_reset.send_password_reset_email') as mock_send:
            mock_send.return_value = mock.MagicMock()
            data = {'email': '<EMAIL>'}
            response = self.client.post(self.request_url, data)

            # Check success response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['success'], True)
            self.assertIn('password reset instructions', response.data['message'].lower())

            # Check that send_password_reset_email was called
            mock_send.assert_called_once_with(self.user)

    def test_request_password_reset_invalid_email(self):
        """
        Test requesting password reset with invalid email.
        """
        with mock.patch('core.serializers.auth.password_reset.send_password_reset_email') as mock_send:
            data = {'email': '<EMAIL>'}
            response = self.client.post(self.request_url, data)

            # Check success response (for security reasons, same response as valid email)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['success'], True)
            self.assertIn('password reset instructions', response.data['message'].lower())

            # Check that send_password_reset_email was NOT called
            mock_send.assert_not_called()

    def test_validate_password_reset_token_valid(self):
        """
        Test validating a valid password reset token.
        """
        # Mock return valid token
        self.mock_verify.return_value = (self.user, None)

        data = {'token': str(uuid.uuid4())}
        response = self.client.post(self.validate_url, data)

        # Check success response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('valid', response.data['message'].lower())

    def test_validate_password_reset_token_expired(self):
        """
        Test validating an expired password reset token.
        """
        # Mock return expired token
        self.mock_verify.return_value = (None, "Password reset link has expired")

        data = {'token': str(uuid.uuid4())}
        response = self.client.post(self.validate_url, data)

        # Check error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid', response.data['message'].lower())

    def test_validate_password_reset_token_used(self):
        """
        Test validating a used password reset token.
        """
        # Mock return used token
        self.mock_verify.return_value = (None, "This password reset link has already been used")

        data = {'token': str(uuid.uuid4())}
        response = self.client.post(self.validate_url, data)

        # Check error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid', response.data['message'].lower())

    def test_validate_password_reset_token_invalid(self):
        """
        Test validating an invalid password reset token.
        """
        # Mock return invalid token
        self.mock_verify.return_value = (None, "Invalid password reset link")

        data = {'token': str(uuid.uuid4())}
        response = self.client.post(self.validate_url, data)

        # Check error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid', response.data['message'].lower())

    def test_password_reset_confirm_valid(self):
        """
        Test password reset confirmation with valid token.
        """
        # Skip the whole serializer and mock the view directly
        with mock.patch('core.views.password_reset.PasswordResetConfirmView.post',
                        return_value=APIResponse(
                            message="Password has been reset successfully. You can now log in with your new password.",
                            data={"email": self.user.email},
                            status_code=status.HTTP_200_OK
                        )):
            # Use the expected parameter names
            data = {
                'token': str(uuid.uuid4()),
                'new_password': 'NewPassword123!',
                'confirm_password': 'NewPassword123!'
            }

            response = self.client.post(self.reset_url, data)

            # Check success response
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['success'], True)
            self.assertIn('success', response.data['message'].lower())

    def test_password_reset_confirm_invalid_token(self):
        """
        Test password reset confirmation with invalid token.
        """
        # Mock return invalid token
        self.mock_verify.return_value = (None, "Invalid password reset link")

        data = {
            'token': str(uuid.uuid4()),
            'new_password': 'NewPassword123!',
            'confirm_password': 'NewPassword123!'
        }
        response = self.client.post(self.reset_url, data)

        # Check error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid', response.data['message'].lower())

    def test_password_reset_confirm_password_mismatch(self):
        """
        Test password reset confirmation with mismatched passwords.
        """
        data = {
            'token': str(uuid.uuid4()),
            'new_password': 'NewPassword123!',
            'confirm_password': 'DifferentPassword123!'
        }
        response = self.client.post(self.reset_url, data)

        # Check error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('match', response.data['message'].lower())


# Disable throttling for email verification tests specifically

class EmailVerificationViewsTests(TestCase):
    """Test cases for the Email Verification views."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Verify Test User',
            active=False
        )

        # Create a verification record
        self.verification = EmailVerification.objects.create(user=self.user)

        # Create an expired verification record
        self.expired_verification = EmailVerification.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(days=1)
        )

        # Create a verified verification record
        self.verified_verification = EmailVerification.objects.create(
            user=self.user,
            verified=True
        )

        # Set up URLs
        self.resend_url = reverse('core:resend-verification-email')

    def test_verify_email_success(self):
        """Test successful email verification."""
        verify_url = reverse('core:verify-email', kwargs={'verification_key': self.verification.key})
        response = self.client.post(verify_url, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('successfully verified', response.data['message'].lower())

        # Check that verification record was marked as verified
        self.verification.refresh_from_db()
        self.assertTrue(self.verification.verified)

    def test_verify_email_already_verified(self):
        """Test email verification when already verified."""
        verify_url = reverse('core:verify-email', kwargs={'verification_key': self.verified_verification.key})
        response = self.client.post(verify_url, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('already been verified', response.data['message'].lower())

    def test_verify_email_expired(self):
        """Test email verification with expired token."""
        verify_url = reverse('core:verify-email', kwargs={'verification_key': self.expired_verification.key})
        response = self.client.post(verify_url, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('expired', response.data['message'].lower())

    def test_verify_email_invalid_token(self):
        """Test verifying email with an invalid token."""
        # Create an invalid UUID format token (but still valid UUID syntax)
        invalid_uuid = "00000000-0000-0000-0000-000000000000"
        verify_url = reverse('core:verify-email', kwargs={'verification_key': invalid_uuid})

        response = self.client.post(verify_url, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid', response.data['message'].lower())

    @patch('common.utils.email.send_mail')
    def test_resend_verification_email(self, mock_send_mail):
        """Test resending verification email."""
        # Add a test user that needs verification
        unverified_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Unverified User'
        )

        # Request data
        data = {'email': '<EMAIL>'}

        # Send request
        response = self.client.post(self.resend_url, data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('verification email has been sent', response.data['message'].lower())

        # Check that send_mail was called
        self.assertTrue(mock_send_mail.called)

        # Check that an email verification record exists
        self.assertTrue(EmailVerification.objects.filter(user=unverified_user, verified=False).exists())

    @patch('common.utils.email.send_mail')
    def test_resend_verification_email_nonexistent_user(self, mock_send_mail):
        """Test resending verification email to a nonexistent user."""
        # Request data
        data = {'email': '<EMAIL>'}

        # Send request
        response = self.client.post(self.resend_url, data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('email has been sent', response.data['message'].lower())

        # Check that send_mail was not called (since user doesn't exist)
        self.assertFalse(mock_send_mail.called)


class UserDetailViewTests(TestCase):
    """Test cases for the User Detail view."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user_detail_url = reverse('core:user-detail')

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test User',
            active=True
        )

        # Get tokens for authentication
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)

    def test_authenticated_access(self):
        """Test accessing user details when authenticated."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

        response = self.client.get(self.user_detail_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check user data
        self.assertIn('data', response.data)
        user_data = response.data['data']
        self.assertEqual(user_data['email'], self.user.email)
        self.assertEqual(user_data['name'], self.user.name)

    def test_unauthenticated_access(self):
        """Test accessing user details when not authenticated."""
        response = self.client.get(self.user_detail_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data['success'], False)
        self.assertIn('authentication credentials', response.data['message'].lower())

    def test_invalid_token(self):
        """Test accessing user details with invalid token."""
        # Set invalid authentication header
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid-token')

        response = self.client.get(self.user_detail_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data['success'], False)
        self.assertIn('invalid token', response.data['message'].lower())

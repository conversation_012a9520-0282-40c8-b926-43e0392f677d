# Generated by Django 5.1.7 on 2025-03-17 16:09

import django.db.models.deletion
import payments.models.validators
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0002_eventregistration_historicaleventregistration'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceDueDate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('due_date', models.DateField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='HistoricalInvoiceDueDate',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('due_date', models.DateField()),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('updated', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical invoice due date',
                'verbose_name_plural': 'historical invoice due dates',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalPayment',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('invoice_number', models.CharField(blank=True, help_text='Format: YYDDD-XXX (year+julian_date-sequence)', max_length=100, null=True, validators=[payments.models.validators.validate_invoice_format])),
                ('po_number', models.CharField(blank=True, max_length=100, null=True)),
                ('paid_year', models.IntegerField(default=2025)),
                ('paid_next_year', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_link', models.URLField(blank=True, null=True)),
                ('payment_id', models.CharField(blank=True, max_length=365, null=True)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('date', models.DateTimeField(blank=True, editable=False)),
                ('updated', models.DateTimeField(blank=True, editable=False)),
                ('status', models.CharField(choices=[('success', 'Success'), ('pending', 'Pending'), ('invoice', 'Invoice'), ('refund', 'Refund'), ('failed', 'Failed'), ('adjustment', 'Adjustment')], default='pending', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('payment_type', models.CharField(blank=True, choices=[('prepaid', 'Prepaid'), ('collect', 'Collect'), ('checks', 'Checks'), ('cash', 'Cash'), ('online', 'Online'), ('money_order', 'Money Order')], default='', max_length=20, null=True)),
                ('draft', models.BooleanField(default=True)),
                ('billing_address', models.CharField(blank=True, default='', max_length=255)),
                ('transaction_id', models.CharField(blank=True, max_length=255, null=True)),
                ('paypal_response', models.JSONField(blank=True, null=True)),
                ('due_date', models.DateField()),
                ('payment_for', models.CharField(choices=[('membership', 'Membership'), ('event', 'Event Registration')], default='membership', max_length=20)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('event_registration', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='core.eventregistration')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('payer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical payment',
                'verbose_name_plural': 'historical payments',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('invoice_number', models.CharField(blank=True, help_text='Format: YYDDD-XXX (year+julian_date-sequence)', max_length=100, null=True, validators=[payments.models.validators.validate_invoice_format])),
                ('po_number', models.CharField(blank=True, max_length=100, null=True)),
                ('paid_year', models.IntegerField(default=2025)),
                ('paid_next_year', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_link', models.URLField(blank=True, null=True)),
                ('payment_id', models.CharField(blank=True, max_length=365, null=True)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('success', 'Success'), ('pending', 'Pending'), ('invoice', 'Invoice'), ('refund', 'Refund'), ('failed', 'Failed'), ('adjustment', 'Adjustment')], default='pending', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('payment_type', models.CharField(blank=True, choices=[('prepaid', 'Prepaid'), ('collect', 'Collect'), ('checks', 'Checks'), ('cash', 'Cash'), ('online', 'Online'), ('money_order', 'Money Order')], default='', max_length=20, null=True)),
                ('draft', models.BooleanField(default=True)),
                ('billing_address', models.CharField(blank=True, default='', max_length=255)),
                ('transaction_id', models.CharField(blank=True, max_length=255, null=True)),
                ('paypal_response', models.JSONField(blank=True, null=True)),
                ('due_date', models.DateField()),
                ('payment_for', models.CharField(choices=[('membership', 'Membership'), ('event', 'Event Registration')], default='membership', max_length=20)),
                ('covered_members', models.ManyToManyField(related_name='covered_payments', to=settings.AUTH_USER_MODEL)),
                ('event_registration', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='core.eventregistration')),
                ('payer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payer_payments', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

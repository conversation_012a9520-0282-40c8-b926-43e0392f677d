"""
Tests for advanced pagination functionality.

This module contains comprehensive tests for complex pagination scenarios
including pagination with filtered and ordered results.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.request import Request
from rest_framework.test import APIRequestFactory
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.utils import timezone
from datetime import timedelta

from django.contrib.auth import get_user_model
from core.models import Department, Member
from common.pagination import StandardPagination
from common.filters.member_filters import DynamicFieldsMemberFilter

User = get_user_model()


class AdvancedPaginationTests(TestCase):
    """Test cases for advanced pagination scenarios."""

    def setUp(self):
        """Set up test data."""
        # Create departments
        self.dept1 = Department.objects.create(
            name='Department 1',
            department_city='City 1',
            department_state='MS'
        )

        self.dept2 = Department.objects.create(
            name='Department 2',
            department_city='City 2',
            department_state='AL'
        )

        # Create a large set of users for pagination testing (55 users)
        self.users = []
        for i in range(55):
            # Alternate between departments
            department = self.dept1 if i % 2 == 0 else self.dept2

            # Alternate between active and inactive
            active = True if i % 3 != 0 else False
            membership_active = True if i % 4 != 0 else False

            # Alternate between different membership classes
            membership_class = Member.MembershipStatus.MEMBER if i % 3 == 0 else \
                               Member.MembershipStatus.ASSOCIATE_MEMBER if i % 3 == 1 else \
                               Member.MembershipStatus.HONORARY_MEMBER

            # Alternate between roles
            role = Member.Role.VOLUNTEER if i % 2 == 0 else Member.Role.CAREER

            # Create the user
            user = User.objects.create_user(
                email=f'user{i}@example.com',
                password='password123',
                name=f'User {i}',
                department=department,
                membership_class=membership_class,
                role=role,
                gender=Member.Gender.MALE if i % 2 == 0 else Member.Gender.FEMALE,
                active=active,
                membership_active=membership_active,
                orig_join_date=timezone.now() - timedelta(days=i*30)  # Each user joins 30 days apart
            )
            self.users.append(user)

        # Create an admin user for API tests
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass',
            name='Admin User',
            is_staff=True,
            is_superuser=True
        )

        self.client = APIClient()
        self.client.force_authenticate(user=self.admin_user)

        # Set up factory for manual pagination tests
        self.factory = APIRequestFactory()
        self.paginator = StandardPagination()

    def test_pagination_with_filtered_results(self):
        """Test pagination with filtered query results."""
        # Get response with filters
        response = self.client.get(reverse('core:admin-member-list'), {
            'department__department_state': 'MS',
            'active': 'yes',
            'page_size': 10
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # We should have about half of the users in department 1
        # and 2/3 of them active, so around 55 / 2 * 2/3 ≈ 18-19 users
        filtered_count = response.data['data']['count']
        self.assertTrue(15 <= filtered_count <= 20)

        # First page should have 10 results
        self.assertEqual(len(response.data['data']['results']), 10)

        # Follow next page link
        next_url = response.data['data']['next']
        self.assertIsNotNone(next_url)

        # Extract the path and query params from the URL
        path_and_query = next_url.split('http://testserver')[1]

        response2 = self.client.get(path_and_query)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)

        # We should be on page 2 with the remaining filtered results
        second_page_count = len(response2.data['data']['results'])
        self.assertTrue(5 <= second_page_count <= 10)

        # Total results across pages should equal the count
        self.assertEqual(
            len(response.data['data']['results']) + len(response2.data['data']['results']),
            filtered_count
        )

    def test_pagination_with_ordering(self):
        """Test pagination with ordered results."""
        # Get ordered results
        response = self.client.get(reverse('core:admin-member-list'), {
            'ordering': '-orig_join_date',  # Newest members first
            'page_size': 15
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # We should have 56 users total (55 + admin)
        self.assertEqual(response.data['data']['count'], 56)

        # First page should have 15 results
        results = response.data['data']['results']
        self.assertEqual(len(results), 15)

        # Verify ordering - the admin user should be first (most recent)
        self.assertEqual(results[0]['email'], '<EMAIL>')

        # Second user should be user0 (most recently created test user)
        self.assertEqual(results[1]['email'], '<EMAIL>')

        # Test reverse ordering
        response = self.client.get(reverse('core:admin-member-list'), {
            'ordering': 'orig_join_date',  # Oldest members first
            'page_size': 15
        })

        results = response.data['data']['results']

        # The oldest user (user54) should be first
        self.assertEqual(results[0]['email'], '<EMAIL>')

    def test_pagination_with_search_and_filter_combined(self):
        """Test pagination with both search and filter parameters."""
        # Create some users with a specific search term
        for i in range(3):
            User.objects.create_user(
                email=f'searchable{i}@example.com',
                password='password123',
                name=f'Searchable User {i}',
                department=self.dept1,
                membership_class=Member.MembershipStatus.MEMBER,
                role=Member.Role.VOLUNTEER,
                active=True
            )

        # Search for users with 'Searchable' in their name, in Department 1
        response = self.client.get(reverse('core:admin-member-list'), {
            'search': 'Searchable',
            'department': self.dept1.id,
            'page_size': 2
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # We should find 3 searchable users
        self.assertEqual(response.data['data']['count'], 3)

        # First page should have 2 results
        self.assertEqual(len(response.data['data']['results']), 2)

        # Follow next page link
        next_url = response.data['data']['next']
        self.assertIsNotNone(next_url)

        # Extract the path and query params from the URL
        path_and_query = next_url.split('http://testserver')[1]

        response2 = self.client.get(path_and_query)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)

        # Second page should have the remaining 1 result
        self.assertEqual(len(response2.data['data']['results']), 1)

    def test_pagination_page_size_edge_cases(self):
        """Test pagination with edge case page sizes."""
        # Edge case: page_size=1
        response = self.client.get(reverse('core:admin-member-list'), {
            'page_size': 1
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 1)
        self.assertEqual(response.data['data']['count'], 56)  # 55 + admin

        # Edge case: page_size=0 (should use default)
        response = self.client.get(reverse('core:admin-member-list'), {
            'page_size': 0
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), StandardPagination.page_size)

        # Edge case: page_size=1000 (should be limited to max_page_size)
        response = self.client.get(reverse('core:admin-member-list'), {
            'page_size': 1000
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should either be limited to max_page_size or return all 56 users
        result_count = len(response.data['data']['results'])
        self.assertTrue(
            result_count == StandardPagination.max_page_size or
            result_count == 56
        )

    def test_pagination_programmatic(self):
        """Test pagination programmatically using the paginator class."""
        # Create a request and paginate manually
        request = self.factory.get('/')
        queryset = User.objects.all().order_by('-id')

        # Paginate the results
        paginated_queryset = self.paginator.paginate_queryset(queryset, Request(request))

        # Should have standard page size results
        self.assertEqual(len(paginated_queryset), StandardPagination.page_size)

        # Get the response
        response = self.paginator.get_paginated_response(paginated_queryset)
        response_data = response.data

        # Verify structure
        self.assertIn('count', response_data)
        self.assertIn('next', response_data)
        self.assertIn('previous', response_data)
        self.assertIn('results', response_data)

        # Count should match total
        self.assertEqual(response_data['count'], 56)  # 55 + admin

        # Next should be present, previous should not
        self.assertIsNotNone(response_data['next'])
        self.assertIsNone(response_data['previous'])

    def test_pagination_filtered_programmatic(self):
        """Test programmatic pagination with filtered results."""
        # Create a filter
        queryset = User.objects.all()
        filter_params = {
            'active': 'yes',
            'department': self.dept1.id
        }

        # Apply the filter
        filterset = DynamicFieldsMemberFilter(filter_params, queryset)
        filtered_queryset = filterset.qs

        # Create a request
        request = self.factory.get('/', {'page': 1, 'page_size': 10})
        request = Request(request)

        # Paginate the filtered queryset
        paginated_queryset = self.paginator.paginate_queryset(filtered_queryset, request)

        # Get the response
        response = self.paginator.get_paginated_response(paginated_queryset)
        response_data = response.data

        # We should have filtered results
        self.assertTrue(response_data['count'] < 56)

        # Should have 10 results per page
        self.assertEqual(len(response_data['results']), 10)

    def test_last_page_behavior(self):
        """Test behavior when navigating to the last page."""
        # Calculate total pages
        total_users = User.objects.count()
        page_size = 10
        total_pages = (total_users + page_size - 1) // page_size  # Ceiling division

        # Go to the last page
        response = self.client.get(reverse('core:admin-member-list'), {
            'page': total_pages,
            'page_size': page_size
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Last page should have between 1 and page_size results
        results_count = len(response.data['data']['results'])
        self.assertTrue(1 <= results_count <= page_size)

        # Previous link should be present, next should not
        self.assertIsNotNone(response.data['data']['previous'])
        self.assertIsNone(response.data['data']['next'])

        # Test going beyond the last page
        response = self.client.get(reverse('core:admin-member-list'), {
            'page': total_pages + 1,
            'page_size': page_size
        })

        # Should get a 404 error for non-existent page
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_django_paginator_compatibility(self):
        """Test compatibility with Django's built-in Paginator."""
        queryset = User.objects.all().order_by('-id')

        # Use Django's paginator
        paginator = Paginator(queryset, StandardPagination.page_size)

        # Get the first page
        page1 = paginator.page(1)

        # Should have standard page size results
        self.assertEqual(len(page1.object_list), StandardPagination.page_size)

        # Paginate the same queryset with DRF paginator
        request = self.factory.get('/')
        drf_paginated = self.paginator.paginate_queryset(queryset, Request(request))

        # Both should have the same number of results
        self.assertEqual(len(page1.object_list), len(drf_paginated))

        # Results should be the same
        for django_obj, drf_obj in zip(page1.object_list, drf_paginated):
            self.assertEqual(django_obj.id, drf_obj.id)

    def test_pagination_with_empty_page(self):
        """Test handling of empty pages."""
        # Create a filtered queryset that returns no results
        response = self.client.get(reverse('core:admin-member-list'), {
            'name': 'NonExistentUser',
            'page': 1
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should have zero results
        self.assertEqual(response.data['data']['count'], 0)
        self.assertEqual(len(response.data['data']['results']), 0)

        # No next or previous links
        self.assertIsNone(response.data['data']['next'])
        self.assertIsNone(response.data['data']['previous'])

    def test_pagination_url_preservation(self):
        """Test that pagination preserves all query parameters in URLs."""
        # Request with multiple query parameters
        response = self.client.get(reverse('core:admin-member-list'), {
            'active': 'yes',
            'department': self.dept1.id,
            'ordering': '-name',
            'search': 'User',
            'page_size': 10
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Next page link should preserve all parameters
        next_url = response.data['data']['next']
        self.assertIsNotNone(next_url)

        # Check that all parameters are preserved
        self.assertIn('active=yes', next_url)
        self.assertIn(f'department={self.dept1.id}', next_url)
        self.assertIn('ordering=-name', next_url)
        self.assertIn('search=User', next_url)
        self.assertIn('page_size=10', next_url)
        self.assertIn('page=2', next_url)

        # Navigate to next page
        path_and_query = next_url.split('http://testserver')[1]
        response2 = self.client.get(path_and_query)

        self.assertEqual(response2.status_code, status.HTTP_200_OK)

        # Previous page link should also preserve all parameters
        prev_url = response2.data['data']['previous']
        self.assertIsNotNone(prev_url)

        # Check that all parameters are preserved
        self.assertIn('active=yes', prev_url)
        self.assertIn(f'department={self.dept1.id}', prev_url)
        self.assertIn('ordering=-name', prev_url)
        self.assertIn('search=User', prev_url)
        self.assertIn('page_size=10', prev_url)
        self.assertIn('page=1', prev_url)

    def tearDown(self):
        """Clean up after tests."""
        # No specific cleanup needed
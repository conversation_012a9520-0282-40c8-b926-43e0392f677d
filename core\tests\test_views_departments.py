"""
Tests for the core app department views.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from django.contrib.auth import get_user_model
from core.models import Department, Member

User = get_user_model()


class DepartmentViewsTests(TestCase):
    """Test cases for the Department views."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create URLs
        self.departments_list_url = reverse('core:admin-department-list')

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Create a regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            active=True
        )

        # Get tokens for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)
        self.regular_token = str(RefreshToken.for_user(self.regular_user).access_token)

        # Create departments
        self.department1 = Department.objects.create(
            name='Department 1',
            department_city='City 1',
            department_state='MS'
        )

        self.department2 = Department.objects.create(
            name='Department 2',
            department_city='City 2',
            department_state='MS'
        )

        # Create some members in departments
        self.member1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Department Member 1',
            department=self.department1,
            active=True
        )

        self.member2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Department Member 2',
            department=self.department1,
            active=True
        )

        self.member3 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Department Member 3',
            department=self.department2,
            active=True
        )

    def test_list_departments(self):
        """Test listing all departments."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        response = self.client.get(self.departments_list_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check that the response contains paginated data
        self.assertIn('results', response.data['data'])
        self.assertIn('count', response.data['data'])
        self.assertIn('next', response.data['data'])
        self.assertIn('previous', response.data['data'])

        # Check that all departments are returned
        # Note: There might be more departments in the database than just the ones we created
        department_names = [dept['name'] for dept in response.data['data']['results']]
        self.assertIn('Department 1', department_names)
        self.assertIn('Department 2', department_names)

    def test_create_department(self):
        """Test creating a new department."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        new_department_data = {
            'name': 'New Department',
            'department_city': 'New City',
            'department_state': 'MS',
            'department_address1': '123 Main St',
            'department_zip_code': '12345'
        }

        create_url = reverse('core:admin-department-create')
        response = self.client.post(create_url, new_department_data, format='json')

        # Check response
        if not self.staff_user.is_staff:
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        else:
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            self.assertEqual(response.data['success'], True)

            # Check that department was created
            self.assertTrue(Department.objects.filter(name='New Department').exists())
            new_dept = Department.objects.get(name='New Department')
            self.assertEqual(new_dept.department_city, 'New City')
            self.assertEqual(new_dept.department_address1, '123 Main St')

    def test_create_duplicate_department(self):
        """Test creating a department with a duplicate name."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        duplicate_department_data = {
            'name': 'Department 1',  # Already exists
            'department_city': 'Different City',
            'department_state': 'MS'
        }

        create_url = reverse('core:admin-department-create')
        response = self.client.post(create_url, duplicate_department_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('already exists', response.data['message'].lower())

    def test_get_department_detail(self):
        """Test retrieving a specific department."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        detail_url = reverse('core:admin-department-detail', kwargs={'pk': self.department1.pk})
        response = self.client.get(detail_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check department data
        self.assertEqual(response.data['data']['name'], 'Department 1')
        self.assertEqual(response.data['data']['department_city'], 'City 1')
        self.assertEqual(response.data['data']['department_state'], 'MS')

    def test_update_department(self):
        """Test updating a department."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        update_url = reverse('core:admin-department-update', kwargs={'pk': self.department1.pk})
        update_data = {
            'name': 'Updated Department 1',
            'department_city': 'Updated City',
            'department_address1': 'New Address'
        }

        response = self.client.patch(update_url, update_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check that department was updated
        self.department1.refresh_from_db()
        self.assertEqual(self.department1.name, 'Updated Department 1')
        self.assertEqual(self.department1.department_city, 'Updated City')
        self.assertEqual(self.department1.department_address1, 'New Address')

        # State should remain unchanged
        self.assertEqual(self.department1.department_state, 'MS')

    def test_delete_department(self):
        """Test deleting a department."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Create a department to delete
        dept_to_delete = Department.objects.create(
            name='Department to Delete',
            department_city='Delete City',
            department_state='MS'
        )

        delete_url = reverse('core:admin-department-delete', kwargs={'pk': dept_to_delete.pk})
        response = self.client.delete(delete_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Check that department was deleted
        self.assertFalse(Department.objects.filter(name='Department to Delete').exists())

    def test_get_department_members(self):
        """Test getting members of a department."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        members_url = reverse('core:admin-department-members', kwargs={'pk': self.department1.pk})
        response = self.client.get(members_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('message', response.data)
        self.assertIn(f"Members in department '{self.department1.name}' retrieved successfully", response.data['message'])

        # Check that the response contains paginated data
        self.assertIn('data', response.data)
        self.assertIn('members', response.data['data'])
        self.assertIn('results', response.data['data']['members'])
        self.assertIn('count', response.data['data']['members'])
        self.assertIn('next', response.data['data']['members'])
        self.assertIn('previous', response.data['data']['members'])

        # Check that correct members are returned
        member_emails = [member['email'] for member in response.data['data']['members']['results']]
        self.assertIn('<EMAIL>', member_emails)
        self.assertIn('<EMAIL>', member_emails)

        # Verify member count
        self.assertEqual(response.data['data']['members']['count'], 2)

    def test_unauthorized_access(self):
        """Test accessing department endpoints with a regular user."""
        # Set authentication header for regular user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')

        # Try to list departments
        response = self.client.get(self.departments_list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Try to create a department
        new_department_data = {
            'name': 'Unauthorized Department',
            'department_city': 'Unauthorized City',
            'department_state': 'MS'
        }
        response = self.client.post(self.departments_list_url, new_department_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Try to get department details
        detail_url = reverse('core:admin-department-detail', kwargs={'pk': self.department1.pk})
        response = self.client.get(detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_access(self):
        """Test accessing department endpoints without authentication."""
        # Remove authentication header
        self.client.credentials()

        # Try to list departments
        response = self.client.get(self.departments_list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Try to create a department
        new_department_data = {
            'name': 'Unauthenticated Department',
            'department_city': 'Unauthenticated City',
            'department_state': 'MS'
        }
        response = self.client.post(self.departments_list_url, new_department_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_search_departments(self):
        """Test searching departments."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Search by name
        response = self.client.get(f'{self.departments_list_url}?search=Department 1')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that the search results include Department 1
        department_names = [dept['name'] for dept in response.data['data']['results']]
        self.assertIn('Department 1', department_names)

        # Search by city
        response = self.client.get(f'{self.departments_list_url}?search=City 2')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that the search results include Department 2
        department_names = [dept['name'] for dept in response.data['data']['results']]
        self.assertIn('Department 2', department_names)

    def test_public_department_endpoints(self):
        """Test public department endpoints."""
        # Authenticate as a regular user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')

        # Public department list
        public_list_url = reverse('core:public-department-list')
        response = self.client.get(public_list_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Verify if response is paginated
        if 'results' in response.data['data']:
            # Paginated response
            self.assertTrue(len(response.data['data']['results']) > 0)
        else:
            # Non-paginated response
            self.assertTrue(len(response.data['data']) > 0)

        # Public department detail
        public_detail_url = reverse('core:public-department-detail', kwargs={'pk': self.department1.pk})
        response = self.client.get(public_detail_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data']['name'], 'Department 1')


class DepartmentViewTests(APITestCase):
    """Test cases for the Department views using APITestCase."""

    def setUp(self):
        """Set up test data."""
        # Create URLs
        self.list_url = reverse('core:admin-department-list')

        # Create a staff user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Authenticate the user
        self.client.force_authenticate(user=self.user)

    def test_list_departments(self):
        """
        Test retrieving a list of departments.
        """
        Department.objects.create(name='HR')
        Department.objects.create(name='Finance')
        # Add the initial 'Executive' department if it's created by migrations/setup
        # Assuming 'Executive' is created by default, total = 3
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        # Verify paginated response
        self.assertIn('results', response.data['data'])
        self.assertIn('count', response.data['data'])
        
        # Adjust count based on actual initial departments + created ones
        initial_departments_count = Department.objects.count() # Get count before creating test ones
        Department.objects.create(name='HR Test List')
        Department.objects.create(name='Finance Test List')
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Total count = initial + 2 created in this test
        self.assertEqual(response.data['data']['count'], initial_departments_count + 2)

    def test_create_department(self):
        """
        Test creating a new department (requires staff/admin privileges).
        """
        data = {'name': 'Engineering'}
        create_url = reverse('core:admin-department-create')
        response = self.client.post(create_url, data)

        # If the test user is staff, expect 201. If not, expect 403.
        if not self.user.is_staff:
             self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        else:
             self.assertEqual(response.status_code, status.HTTP_201_CREATED)
             self.assertEqual(response.data['success'], True)
             self.assertEqual(response.data['data']['name'], 'Engineering')
             self.assertTrue(Department.objects.filter(name='Engineering').exists())

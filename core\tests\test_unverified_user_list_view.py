"""
Test cases for the UnverifiedUserListView and VerifiedButInactiveUserListView.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from datetime import timedelta

from core.models import Member, Department
from common.models import EmailVerification


class UnverifiedUserListViewTests(TestCase):
    """Test cases for the UnverifiedUserListView."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a department
        self.department = Department.objects.create(name="Test Department")

        # Create an admin user for authentication
        self.admin_user = Member.objects.create(
            email="<EMAIL>",
            name="Admin User",
            is_staff=True,
            is_superuser=True,
            active=True
        )

        # Create an unverified user
        self.unverified_user = Member.objects.create(
            email="<EMAIL>",
            name="Unverified User",
            department=self.department,
            active=False
        )

        # Create a verified but inactive user
        self.verified_inactive_user = Member.objects.create(
            email="<EMAIL>",
            name="Verified Inactive User",
            department=self.department,
            active=False
        )

        # Create email verification for verified user
        self.verification = EmailVerification.objects.create(
            user=self.verified_inactive_user,
            verified=True,
            expires_at=timezone.now() + timedelta(days=1)
        )

        # Authenticate as admin
        self.client.force_authenticate(user=self.admin_user)

    def test_unverified_user_list_with_results(self):
        """Test unverified user list view returns correct data when users exist."""
        response = self.client.get(reverse('core:unverified-users'))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Unverified users retrieved successfully")

        # Check that results contain our unverified user
        self.assertEqual(response.data['data']['count'], 1)
        self.assertEqual(len(response.data['data']['results']), 1)
        self.assertEqual(response.data['data']['results'][0]['email'], self.unverified_user.email)

    def test_unverified_user_list_with_no_results(self):
        """Test unverified user list view returns empty array when no users found."""
        # Delete the unverified user
        self.unverified_user.delete()

        response = self.client.get(reverse('core:unverified-users'))

        # Print response data for debugging
        print(f"Response status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"Response data: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "No unverified users found")

        # Check that results is an empty array
        self.assertEqual(response.data['data']['count'], 0)
        self.assertEqual(len(response.data['data']['results']), 0)
        self.assertEqual(response.data['data']['results'], [])

    def test_verified_inactive_user_list_with_results(self):
        """Test verified but inactive user list view returns correct data when users exist."""
        response = self.client.get(reverse('core:verified-inactive-users'))

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Verified but inactive users retrieved successfully")

        # Check that results contain our verified but inactive user
        self.assertEqual(response.data['data']['count'], 1)
        self.assertEqual(len(response.data['data']['results']), 1)
        self.assertEqual(response.data['data']['results'][0]['email'], self.verified_inactive_user.email)

    def test_verified_inactive_user_list_with_no_results(self):
        """Test verified but inactive user list view returns empty array when no users found."""
        # Delete the verified but inactive user
        self.verified_inactive_user.delete()

        response = self.client.get(reverse('core:verified-inactive-users'))

        # Print response data for debugging
        print(f"Response status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"Response data: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "No verified but inactive users found")

        # Check that results is an empty array
        self.assertEqual(response.data['data']['count'], 0)
        self.assertEqual(len(response.data['data']['results']), 0)
        self.assertEqual(response.data['data']['results'], [])

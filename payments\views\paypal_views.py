"""
Views for PayPal payment processing.
Complete PayPal payment flow with initiate, success, and cancel endpoints.
"""
import logging
from django.utils import timezone
from django.http import HttpResponseRedirect
from rest_framework.permissions import IsAuthenticated
from rest_framework import status

from common.views import BaseAPIView, APIResponse
from payments.models import Payment
from payments.services import PayPalService, PaymentService
from payments.services.paypal_service import PayPalAPIError
from payments.config import SuccessMessages, ErrorMessages

logger = logging.getLogger(__name__)



class PayPalPaymentFlowView(BaseAPIView):
    """Complete PayPal payment flow for frontend integration"""
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def post(self, request, *args, **kwargs):
        """
        Initiate PayPal payment flow
        Expected request data:
        {
            "payment_id": 123
        }
        """
        payment_id = request.data.get('payment_id')

        if not payment_id:
            return APIResponse(
                message="Payment ID is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            payment = Payment.objects.get(pk=payment_id)
        except Payment.DoesNotExist:
            return APIResponse(
                message=ErrorMessages.PAYMENT_NOT_FOUND,
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Check if payment is already completed
        if payment.status == Payment.PaymentStatus.SUCCESS:
            return APIResponse(
                message=ErrorMessages.PAYMENT_ALREADY_PAID,
                data={
                    "payment_id": payment.pk,
                    "status": payment.status,
                    "payment_date": payment.payment_date,
                    "transaction_id": payment.transaction_id
                },
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Generate return and cancel URLs for PayPal callbacks
        base_url = request.build_absolute_uri('/')
        return_url = f"{base_url}api/payments/paypal/success/?payment_id={payment_id}"
        cancel_url = f"{base_url}api/payments/paypal/cancel/?payment_id={payment_id}"

        try:
            # Generate description using service
            description = PaymentService.get_payment_description(payment)

            # Create PayPal order using service
            reference_id = str(payment.pk)
            paypal_order = self.paypal_service.create_order(
                amount=float(payment.total_amount or payment.amount),
                description=description,
                reference_id=reference_id,
                return_url=return_url,
                cancel_url=cancel_url
            )

            # Update payment with PayPal information
            payment.payment_type = Payment.PaymentType.PAYPAL
            payment.paypal_order_id = paypal_order.get('id')
            payment.paypal_response = paypal_order
            payment.save()

            # Get the approval URL
            approval_url = next((link['href'] for link in paypal_order.get('links', [])
                               if link['rel'] == 'approve'), None)

            if not approval_url:
                return APIResponse(
                    message="Failed to get PayPal approval URL",
                    data=None,
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            return APIResponse(
                message=SuccessMessages.PAYPAL_ORDER_CREATED,
                data={
                    "payment_id": payment.pk,
                    "amount": float(payment.total_amount or payment.amount),
                    "approval_url": approval_url,
                    "order_id": paypal_order.get('id'),
                    "description": description
                },
                status_code=status.HTTP_201_CREATED
            )

        except PayPalAPIError as e:
            logger.error(f"PayPal API error for payment {payment.pk}: {str(e)}")
            return APIResponse(
                message=f"PayPal error: {str(e)}",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error initiating PayPal payment {payment.pk}: {str(e)}")
            return APIResponse(
                message="Failed to create PayPal order",
                data=None,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PayPalSuccessView(BaseAPIView):
    """Handle successful PayPal payment callback"""
    permission_classes = []  # PayPal callbacks don't include authentication

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def get(self, request, *args, **kwargs):
        """Handle PayPal success callback"""
        payment_id = request.GET.get('payment_id')
        paypal_order_id = request.GET.get('token')  # PayPal sends order ID as 'token'
        payer_id = request.GET.get('PayerID')

        if not payment_id or not paypal_order_id:
            logger.error(f"Missing parameters in PayPal success callback: payment_id={payment_id}, token={paypal_order_id}")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-error?error=missing_parameters")

        try:
            payment = Payment.objects.get(pk=payment_id)
        except Payment.DoesNotExist:
            logger.error(f"Payment not found in success callback: {payment_id}")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-error?error=payment_not_found")

        # Check if payment is already completed to prevent double processing
        if payment.status == Payment.PaymentStatus.SUCCESS:
            logger.info(f"Payment {payment_id} already completed, redirecting to success page")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-success?payment_id={payment_id}&transaction_id={payment.transaction_id}")

        try:
            # Capture the payment using PayPal service
            capture_result = self.paypal_service.capture_payment(paypal_order_id)

            # Update payment record
            payment.payment_date = timezone.now().date()
            payment.paypal_response = capture_result
            payment.paypal_order_id = paypal_order_id

            # Extract PayPal transaction details
            if 'purchase_units' in capture_result:
                for unit in capture_result.get('purchase_units', []):
                    if 'payments' in unit and 'captures' in unit['payments']:
                        for capture in unit['payments']['captures']:
                            payment.transaction_id = capture.get('id')
                            payment.paypal_payment_status = capture.get('status')

                            # Get fee details if available
                            if 'seller_receivable_breakdown' in capture:
                                breakdown = capture['seller_receivable_breakdown']
                                if 'paypal_fee' in breakdown:
                                    payment.paypal_fee = breakdown['paypal_fee']['value']

            # Update payment status using service (handles related payments)
            PaymentService.update_payment_status(payment, Payment.PaymentStatus.SUCCESS)

            # If this is an event registration payment, update the event registration status
            if payment.payment_for == Payment.PaymentFor.EVENT and payment.event_registration:
                event_registration = payment.event_registration
                event_registration.payment_status = 'COMPLETED'
                event_registration.save()

            logger.info(f"PayPal payment successful: payment_id={payment_id}, transaction_id={payment.transaction_id}")

            # Redirect to frontend success page
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-success?payment_id={payment_id}&transaction_id={payment.transaction_id}")

        except PayPalAPIError as e:
            logger.error(f"PayPal API error in success callback for payment {payment_id}: {str(e)}")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-error?error=paypal_api_error")
        except Exception as e:
            logger.error(f"Unexpected error in PayPal success callback for payment {payment_id}: {str(e)}")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-error?error=processing_error")

    def _get_frontend_url(self, request=None):
        """Get frontend URL from settings or config"""
        from django.conf import settings
        from payments.config import PaymentConfig

        # Try to get from Django settings first, then fallback to config
        frontend_url = getattr(settings, 'FRONTEND_BASE_URL', PaymentConfig.FRONTEND_BASE_URL)

        return frontend_url.rstrip('/')


class PayPalCancelView(BaseAPIView):
    """Handle cancelled PayPal payment callback"""
    permission_classes = []  # PayPal callbacks don't include authentication

    def get(self, request, *args, **kwargs):
        """Handle PayPal cancel callback"""
        payment_id = request.GET.get('payment_id')
        paypal_order_id = request.GET.get('token')  # PayPal sends order ID as 'token'

        if not payment_id:
            logger.warning(f"Missing payment_id in PayPal cancel callback")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-cancelled")

        try:
            payment = Payment.objects.get(pk=payment_id)

            # Update payment status to cancelled
            PaymentService.update_payment_status(payment, Payment.PaymentStatus.CANCELLED)

            logger.info(f"PayPal payment cancelled: payment_id={payment_id}, order_id={paypal_order_id}")

            # Redirect to frontend cancel page
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-cancelled?payment_id={payment_id}")

        except Payment.DoesNotExist:
            logger.error(f"Payment not found in cancel callback: {payment_id}")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-cancelled?error=payment_not_found")
        except Exception as e:
            logger.error(f"Error in PayPal cancel callback for payment {payment_id}: {str(e)}")
            frontend_url = self._get_frontend_url(request)
            return HttpResponseRedirect(f"{frontend_url}/payment-cancelled?error=processing_error")

    def _get_frontend_url(self, request=None):
        """Get frontend URL from settings or config"""
        from django.conf import settings
        from payments.config import PaymentConfig

        # Try to get from Django settings first, then fallback to config
        frontend_url = getattr(settings, 'FRONTEND_BASE_URL', PaymentConfig.FRONTEND_BASE_URL)

        return frontend_url.rstrip('/')


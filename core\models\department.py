from django.db import models
from simple_history.models import HistoricalRecords


class Department(models.Model):
    name = models.Char<PERSON>ield(max_length=255, unique=True)

    # Department address fields
    department_address1 = models.CharField(max_length=255, blank=True, default="")
    department_address2 = models.Char<PERSON><PERSON>(max_length=255, blank=True, default="")
    department_city = models.CharField(max_length=255, blank=True, default="")
    department_district = models.CharField(max_length=255, blank=True, default="")
    department_county = models.CharField(max_length=255, blank=True, default="")
    department_state = models.CharField(max_length=2, default="MS")
    department_zip_code = models.CharField(max_length=10, blank=True, default="")

    # Billing address fields
    billing_address1 = models.CharField(max_length=255, blank=True, default="")
    billing_address2 = models.CharField(max_length=255, blank=True, default="")
    billing_city = models.Char<PERSON>ield(max_length=255, blank=True, default="")
    billing_district = models.CharField(max_length=255, blank=True, default="")
    billing_county = models.Char<PERSON>ield(max_length=255, blank=True, default="")
    billing_state = models.CharField(max_length=2, default="MS")
    billing_zip_code = models.CharField(max_length=10, blank=True, default="")

    history = HistoricalRecords()

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'
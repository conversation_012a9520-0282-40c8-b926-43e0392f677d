from django.db import models
from django.utils import timezone
from simple_history.models import HistoricalRecords
from django.core.exceptions import ValidationError

from .user import Member
from .event import Event


def validate_invoice_format(value):
    """Custom validator to ensure invoice number format is YYDDD-XXX with integers"""
    error_message = 'Invoice number must be in format YYDDD-XXX where Y=year digit, D=day of year digit, X=sequence number digit'

    if value:  # Allow empty values since field is nullable
        try:
            # Split the value and check format
            prefix, counter = value.split('-')

            # Ensure both parts are valid integers
            if not (prefix.isdigit() and counter.isdigit()):
                raise ValidationError(error_message)

            # Ensure correct lengths (5 digits before hyphen, 3 after)
            if not (len(prefix) == 5 and len(counter) == 3):
                raise ValidationError(error_message)

        except (ValueError, ValidationError):
            raise ValidationError(error_message)


class EventRegistration(models.Model):
    REGISTRATION_TYPES = [
        ('NORMAL', 'Normal Registration'),
        ('LATE', 'Late Registration'),
    ]

    PAYMENT_STATUS = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed')
    ]

    # Event reference
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='registrations', null=True, blank=True)

    # Main registrant details
    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='event_registrations', null=True, blank=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    title = models.CharField(max_length=100)
    fire_department = models.CharField(max_length=200)
    address = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=50)
    zipcode = models.CharField(max_length=10)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    invoice_number = models.CharField(max_length=100, blank=True, null=True, validators=[validate_invoice_format])

    # Registration details
    registration_type = models.CharField(max_length=20, choices=REGISTRATION_TYPES)
    number_of_participants = models.PositiveIntegerField(default=1)
    number_of_guests = models.PositiveIntegerField(default=0)
    registration_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, null=True)

    # Payment details - these will be moved to the Payment model
    base_amount = models.DecimalField(max_digits=10, decimal_places=2)
    guest_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS, default='PENDING')

    # Additional participants
    extra_participants = models.JSONField(default=dict, blank=True)

    def clean(self):
        """Validate model fields"""
        super().clean()

        # Validate extra_participants is a dictionary
        if not isinstance(self.extra_participants, dict):
            raise ValidationError({'extra_participants': 'Extra participants must be a dictionary.'})

    # Group registration support
    group_registration = models.BooleanField(default=False)
    group_members = models.ManyToManyField(Member, related_name='group_event_registrations', blank=True)

    history = HistoricalRecords()

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.registration_type}"

    class Meta:
        ordering = ['-registration_date']
        # Add uniqueness constraint for event and member
        unique_together = ['event', 'member']

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def get_full_address(self):
        return f"{self.address}, {self.city}, {self.state} {self.zipcode}"

    def calculate_total_amount(self):
        """Recalculate total amount based on participants and guests"""
        if self.event:
            # Use event-specific rates
            if self.event.is_late_registration:
                base_rate = float(self.event.registration_fee_late)
            else:
                base_rate = float(self.event.registration_fee_normal)
            guest_rate = float(self.event.guest_fee)
        else:
            # Fallback to default rates if no event is specified
            fallback_date = timezone.datetime(2024, 12, 15, tzinfo=timezone.get_current_timezone())
            base_rate = 115 if timezone.now() > fallback_date else 100
            guest_rate = 25

        self.base_amount = self.number_of_participants * base_rate
        self.guest_amount = self.number_of_guests * guest_rate
        self.total_amount = self.base_amount + self.guest_amount
        return self.total_amount

    def can_be_modified(self):
        """Check if registration can be modified"""
        return self.payment_status != 'COMPLETED'

    def save(self, *args, **kwargs):
        """Override save method to ensure amounts are never null"""
        # Ensure base_amount is never null
        if self.base_amount is None:
            self.base_amount = 0

        # Ensure guest_amount is never null
        if self.guest_amount is None:
            self.guest_amount = 0

        # Ensure total_amount is never null
        if self.total_amount is None:
            self.total_amount = self.base_amount + self.guest_amount

        # Call the original save method
        super().save(*args, **kwargs)
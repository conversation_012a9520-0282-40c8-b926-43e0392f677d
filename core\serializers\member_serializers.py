from rest_framework import serializers
from core.models import Member, Department


class DepartmentsSerializer(serializers.ModelSerializer):
    """
    Serializer for Department model
    """
    member_count = serializers.IntegerField(read_only=True, required=False)

    class Meta:
        model = Department
        fields = [
            'id', 'name',
            # Department address fields
            'department_address1', 'department_address2', 'department_city', 'department_district',
            'department_county', 'department_state', 'department_zip_code',
            # Billing address fields
            'billing_address1', 'billing_address2', 'billing_city','billing_district',
            'billing_county', 'billing_state', 'billing_zip_code',
            # Other fields
             'member_count'
        ]


class MembershipRosterAdminSerializer(serializers.ModelSerializer):
    """
    Serializer for Member model with admin-level access
    """
    department_name = serializers.SerializerMethodField()
    membership_class_display = serializers.CharField(source='get_membership_class_display', read_only=True)

    # Payment status fields (calculated in the view)
    has_paid_this_year = serializers.Bo<PERSON>anField(read_only=True, required=False, default=False)
    has_paid_last_year = serializers.Bo<PERSON>anField(read_only=True, required=False, default=False)
    has_paid_in_last_five_years = serializers.BooleanField(read_only=True, required=False, default=False)
    has_paid_between_three_and_five_years_ago = serializers.BooleanField(read_only=True, required=False, default=False)

    def get_department_name(self, obj):
        """Return department name or empty string if department is None"""
        if obj.department:
            return obj.department.name
        return ""

    class Meta:
        model = Member
        fields = [
            'id', 'name', 'mi', 'dst', 'title', 'email', 'address', 'city', 'st',
            'zip_code', 'home_phone', 'business_phone', 'department', 'department_name',
            'membership_class', 'membership_class_display', 'executive_board',
            'committee_member', 'committee', 'new_member', 'lifetime', 'paid_next_year',
            'lapel_pin', 'is_deceased', 'active', 'membership_active', 'orig_join_date', 'notes', 'picture',
            'has_paid_this_year', 'has_paid_last_year', 'has_paid_in_last_five_years',
            'has_paid_between_three_and_five_years_ago', 'role', 'gender'
        ]


class MemberIdSerializer(serializers.Serializer):
    """
    Serializer for member IDs (used in bulk operations)
    """
    member_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=True
    )


class MergeMembersSerializer(serializers.Serializer):
    """
    Serializer for merging members

    The first member in the list will be considered the primary member (to keep)
    and the second member will be considered the secondary member (to be deleted after merge).

    Primary member's fields are retained. If primary member has empty fields,
    those will be filled with data from the secondary member.
    """
    members_list = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        min_length=2,
        max_length=2
    )

    def validate_members_list(self, value):
        """
        Validate that exactly 2 members are provided and they are different.
        """
        if len(value) != 2:
            raise serializers.ValidationError("Exactly 2 members must be provided for merging.")

        if value[0] == value[1]:
            raise serializers.ValidationError("Cannot merge a member with itself.")

        return value 
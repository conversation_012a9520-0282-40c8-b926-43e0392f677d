"""
Tests for the core app member views.
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from datetime import timedelta

from django.contrib.auth import get_user_model
from core.models import Department, Member

User = get_user_model()


class MemberViewsTests(TestCase):
    """Test cases for the Member views."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.members_url = reverse('core:admin-member-list')

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Create a regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            active=True
        )

        # Get tokens for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)
        self.regular_token = str(RefreshToken.for_user(self.regular_user).access_token)

        # Create departments
        self.department1 = Department.objects.create(
            name='Department 1',
            department_city='City 1',
            department_state='MS'
        )

        self.department2 = Department.objects.create(
            name='Department 2',
            department_city='City 2',
            department_state='MS'
        )

        # Create members
        self.active_member1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Active Member 1',
            department=self.department1,
            membership_class=Member.MembershipStatus.MEMBER,
            active=True,
            membership_active=True
        )

        self.active_member2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Active Member 2',
            department=self.department2,
            membership_class=Member.MembershipStatus.ASSOCIATE_MEMBER,
            active=True,
            membership_active=True
        )

        self.inactive_member = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Inactive Member',
            department=self.department1,
            membership_class=Member.MembershipStatus.MEMBER,
            active=True,
            membership_active=False
        )

    def test_list_members(self):
        """Test listing all members."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Make request
        response = self.client.get(self.members_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['message'], 'Members retrieved successfully')

        # Check that the response contains paginated data
        self.assertIn('data', response.data)
        self.assertIn('members', response.data['data'])
        self.assertIn('results', response.data['data']['members'])
        self.assertIn('count', response.data['data']['members'])
        self.assertIn('next', response.data['data']['members'])
        self.assertIn('previous', response.data['data']['members'])

        # Check that correct members are returned
        member_emails = [member['email'] for member in response.data['data']['members']['results']]
        self.assertIn('<EMAIL>', member_emails)
        self.assertIn('<EMAIL>', member_emails)

        # Verify member count
        self.assertEqual(response.data['data']['members']['count'], 2)

    def test_filter_members_by_department(self):
        """Test filtering members by department."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Make request with department filter
        response = self.client.get(self.members_url, {'department': self.department1.pk})

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['message'], 'Members retrieved successfully')

        # Check that the response contains paginated data
        self.assertIn('data', response.data)
        self.assertIn('members', response.data['data'])
        self.assertIn('results', response.data['data']['members'])
        self.assertIn('count', response.data['data']['members'])
        self.assertIn('next', response.data['data']['members'])
        self.assertIn('previous', response.data['data']['members'])

        # Check that only members from department1 are returned
        member_emails = [member['email'] for member in response.data['data']['members']['results']]
        self.assertIn('<EMAIL>', member_emails)
        self.assertNotIn('<EMAIL>', member_emails)

        # Verify member count
        self.assertEqual(response.data['data']['members']['count'], 1)

    def test_filter_members_by_status(self):
        """Test filtering members by membership status."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Filter by active membership
        response = self.client.get(f'{self.members_url}?membership_active=true')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check filtered members
        active_members = [member for member in response.data['data']['results'] if member.get('membership_active', False)]
        self.assertEqual(len(active_members), 2)  # 2 members with active membership

        # Filter by inactive membership
        response = self.client.get(f'{self.members_url}?membership_active=false')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check filtered members
        inactive_members = [member for member in response.data['data']['results'] if not member.get('membership_active', True)]
        self.assertGreaterEqual(len(inactive_members), 1)  # At least 1 member with inactive membership

    def test_filter_members_by_membership_class(self):
        """Test filtering members by membership class."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Filter by regular member class
        response = self.client.get(f'{self.members_url}?membership_class={Member.MembershipStatus.MEMBER}')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check that we have results
        self.assertTrue(len(response.data['data']['results']) > 0)
        # All results should have the correct membership class
        for member in response.data['data']['results']:
            self.assertEqual(member.get('membership_class'), Member.MembershipStatus.MEMBER)

        # Filter by associate member class
        response = self.client.get(f'{self.members_url}?membership_class={Member.MembershipStatus.ASSOCIATE_MEMBER}')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check that we have results
        self.assertTrue(len(response.data['data']['results']) > 0)
        # All results should have the correct membership class
        for member in response.data['data']['results']:
            self.assertEqual(member.get('membership_class'), Member.MembershipStatus.ASSOCIATE_MEMBER)

    def test_export_members_excel(self):
        """Test exporting members as Excel."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        export_url = reverse('core:admin-member-export')
        response = self.client.get(export_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertIn('attachment; filename=', response['Content-Disposition'])

        # Check that content is not empty (Excel file)
        self.assertTrue(len(response.content) > 0)

    # PDF export is not implemented, so we remove this test

    def test_unauthorized_access(self):
        """Test accessing member endpoints with a regular user."""
        # Set authentication header for regular user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')

        # Try to list members
        response = self.client.get(self.members_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Try to export members
        export_url = reverse('core:admin-member-export')
        response = self.client.get(export_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_access(self):
        """Test accessing member endpoints without authentication."""
        # Try to list members
        response = self.client.get(self.members_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Try to export members
        export_url = reverse('core:admin-member-export')
        response = self.client.get(export_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class MergeMembersViewTests(TestCase):
    """Test cases for the Merge Members view."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.merge_url = reverse('core:admin-member-merge')

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Create a regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            active=True
        )

        # Get tokens for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)
        self.regular_token = str(RefreshToken.for_user(self.regular_user).access_token)

        # Create a department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create members to merge with distinct fields for testing
        self.member1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Member One',
            department=self.department,
            home_phone='555-1111',
            business_phone='',  # Empty field - should take from member2
            membership_class=Member.MembershipStatus.MEMBER,
            active=True,
            membership_active=True,
            notes='Notes for member 1',
            address='123 Primary St',
            city='Primary City',
            st='MS',
            zip_code='12345',
            title='',  # Empty field - should take from member2
            committee='',  # Empty field - should take from member2
            executive_board=False  # False field - should take True from member2 if it's True
        )

        self.member2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Member Two',
            department=self.department,
            home_phone='',  # Empty field - primary should keep its value
            business_phone='555-2222',  # Should be used since primary's is empty
            membership_class=Member.MembershipStatus.ASSOCIATE_MEMBER,
            active=True,
            membership_active=True,
            notes='Notes for member 2',  # Should be combined with primary's notes
            address='456 Secondary St',  # Should not override primary's value
            city='Secondary City',  # Should not override primary's value
            st='AL',  # Should not override primary's value
            zip_code='67890',  # Should not override primary's value
            title='Dr.',  # Should be used since primary's is empty
            committee='Executive Committee',  # Should be used since primary's is empty
            executive_board=True  # Should be used since primary's is False
        )

    def test_merge_members_success(self):
        """Test successfully merging two members."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Merge data - only need to specify the members list
        # First member is primary (to keep), second is secondary (to be deleted)
        merge_data = {
            'members_list': [self.member1.id, self.member2.id]
        }

        response = self.client.post(self.merge_url, merge_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('members merged', response.data['message'].lower())

        # Check that secondary member was deleted
        self.assertFalse(User.objects.filter(id=self.member2.id).exists())

        # Check that primary member has the correct data
        self.member1.refresh_from_db()

        # Primary member's fields should be retained
        self.assertEqual(self.member1.name, 'Member One')  # From primary
        self.assertEqual(self.member1.email, '<EMAIL>')  # From primary
        self.assertEqual(self.member1.address, '123 Primary St')  # From primary
        self.assertEqual(self.member1.city, 'Primary City')  # From primary
        self.assertEqual(self.member1.st, 'MS')  # From primary
        self.assertEqual(self.member1.zip_code, '12345')  # From primary
        self.assertEqual(self.member1.home_phone, '555-1111')  # From primary

        # Empty fields in primary should be filled with secondary's values
        self.assertEqual(self.member1.business_phone, '555-2222')  # From secondary (primary was empty)
        self.assertEqual(self.member1.title, 'Dr.')  # From secondary (primary was empty)
        self.assertEqual(self.member1.committee, 'Executive Committee')  # From secondary (primary was empty)

        # Boolean fields should be taken from secondary if primary is False
        self.assertTrue(self.member1.executive_board)  # From secondary (primary was False)

        # Notes field should be combined since both had content
        self.assertIn('Notes for member 1', self.member1.notes)  # Combined
        self.assertIn('Notes for member 2', self.member1.notes)  # Combined

    def test_merge_members_reverse_order(self):
        """Test merging members with the secondary member first in the list."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Merge data with members in reverse order
        # Now member2 is primary (to keep) and member1 is secondary (to be deleted)
        merge_data = {
            'members_list': [self.member2.id, self.member1.id]
        }

        response = self.client.post(self.merge_url, merge_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('members merged', response.data['message'].lower())

        # Check that secondary member (member1) was deleted
        self.assertFalse(User.objects.filter(id=self.member1.id).exists())

        # Check that primary member (member2) has the correct data
        self.member2.refresh_from_db()

        # Primary member's fields should be retained
        self.assertEqual(self.member2.name, 'Member Two')  # From primary (member2)
        self.assertEqual(self.member2.email, '<EMAIL>')  # From primary (member2)
        self.assertEqual(self.member2.address, '456 Secondary St')  # From primary (member2)
        self.assertEqual(self.member2.city, 'Secondary City')  # From primary (member2)
        self.assertEqual(self.member2.st, 'AL')  # From primary (member2)
        self.assertEqual(self.member2.zip_code, '67890')  # From primary (member2)
        self.assertEqual(self.member2.business_phone, '555-2222')  # From primary (member2)

        # Empty fields in primary should be filled with secondary's values
        self.assertEqual(self.member2.home_phone, '555-1111')  # From secondary (member1) (primary was empty)

        # Notes field should be combined since both had content
        self.assertIn('Notes for member 1', self.member2.notes)  # Combined
        self.assertIn('Notes for member 2', self.member2.notes)  # Combined

    def test_merge_members_invalid_ids(self):
        """Test merging with invalid member IDs."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Merge data with invalid primary ID
        invalid_primary_data = {
            'members_list': [9999, self.member2.id]  # First ID doesn't exist
        }

        response = self.client.post(self.merge_url, invalid_primary_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('primary member not found', response.data['message'].lower())

        # Merge data with invalid secondary ID
        invalid_secondary_data = {
            'members_list': [self.member1.id, 9999]  # Second ID doesn't exist
        }

        response = self.client.post(self.merge_url, invalid_secondary_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('secondary member not found', response.data['message'].lower())

    def test_merge_members_same_id(self):
        """Test merging a member with itself."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Merge data with same ID for both members
        same_id_data = {
            'members_list': [self.member1.id, self.member1.id]  # Same ID for both members
        }

        response = self.client.post(self.merge_url, same_id_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('cannot merge a member with itself', response.data['message'].lower())

    def test_merge_members_unauthorized(self):
        """Test merging members with a regular user."""
        # Set authentication header for regular user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')

        # Merge data
        merge_data = {
            'members_list': [self.member1.id, self.member2.id]
        }

        response = self.client.post(self.merge_url, merge_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['success'], False)
        self.assertIn('permission', response.data['message'].lower())

        # Check that both members still exist
        self.assertTrue(User.objects.filter(id=self.member1.id).exists())
        self.assertTrue(User.objects.filter(id=self.member2.id).exists())

    def test_merge_members_with_related_objects(self):
        """Test merging members with related objects (payments)."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Create payments for both members
        from payments.models import Payment

        # Create a payment where member1 is the payer
        payment1 = Payment.objects.create(
            payer=self.member1,
            amount=100.00,
            payment_type=Payment.PaymentType.CASH,
            status=Payment.PaymentStatus.SUCCESS,
            payment_date=timezone.now().date(),
            due_date=timezone.now().date()
        )

        # Create a payment where member2 is the payer
        payment2 = Payment.objects.create(
            payer=self.member2,
            amount=200.00,
            payment_type=Payment.PaymentType.CHECKS,
            status=Payment.PaymentStatus.SUCCESS,
            payment_date=timezone.now().date(),
            due_date=timezone.now().date()
        )

        # Add member2 as a covered member in payment1
        payment1.covered_members.add(self.member2)

        # Merge data
        merge_data = {
            'members_list': [self.member1.id, self.member2.id]
        }

        response = self.client.post(self.merge_url, merge_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check that secondary member was deleted
        self.assertFalse(User.objects.filter(id=self.member2.id).exists())

        # Check that payments were properly transferred
        # Payment1 should still have member1 as payer
        payment1.refresh_from_db()
        self.assertEqual(payment1.payer.id, self.member1.id)

        # Payment2 should now have member1 as payer (transferred from member2)
        payment2.refresh_from_db()
        self.assertEqual(payment2.payer.id, self.member1.id)

        # Check that covered_members relationship was updated
        # Member2 was in covered_members of payment1, now it should be member1
        self.assertIn(self.member1, payment1.covered_members.all())
        self.assertEqual(payment1.covered_members.count(), 1)  # Only member1 should be there

    def test_merge_members_unauthenticated(self):
        """Test merging members without authentication."""
        # Merge data
        merge_data = {
            'members_list': [self.member1.id, self.member2.id]
        }

        response = self.client.post(self.merge_url, merge_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data['success'], False)
        self.assertIn('authentication', response.data['message'].lower())

        # Check that both members still exist
        self.assertTrue(User.objects.filter(id=self.member1.id).exists())
        self.assertTrue(User.objects.filter(id=self.member2.id).exists())


class PrintLabelsViewTests(TestCase):
    """Test cases for the Print Labels view."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.print_labels_url = reverse('core:admin-member-print-labels')

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Create a regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            active=True
        )

        # Get tokens for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)
        self.regular_token = str(RefreshToken.for_user(self.regular_user).access_token)

        # Create departments
        self.department = Department.objects.create(
            name='Label Test Department',
            department_city='Label City',
            department_state='MS'
        )

        # Create members
        self.members = []
        for i in range(5):
            member = User.objects.create_user(
                email=f'member{i}@example.com',
                password='securepassword123',
                name=f'Label Member {i}',
                department=self.department,
                address=f'123 Main St #{i}',
                city='Label City',
                st='MS',
                zip_code='12345',
                membership_class=Member.MembershipStatus.MEMBER,
                active=True,
                membership_active=True
            )
            self.members.append(member)

    def test_print_labels_success(self):
        """Test generating labels successfully."""
        # Set authentication header for staff user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Data for label generation
        label_data = {
            'member_ids': [member.id for member in self.members[:3]],  # Use first 3 members
            'format': 'avery5160',  # Standard format
            'start_position': 1,
            'include_department': True
        }

        response = self.client.post(self.print_labels_url, label_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('pdf', response.data['data'])
        self.assertTrue(response.data['data']['pdf'].startswith('data:application/pdf;base64,'))

    def test_print_labels_missing_members(self):
        """Test generating labels with missing member IDs."""
        # Set authentication header for staff user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Data with empty member list
        label_data = {
            'member_ids': [],
            'format': 'avery5160',
            'start_position': 1,
            'include_department': True
        }

        response = self.client.post(self.print_labels_url, label_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('member', response.data['message'].lower())

    def test_print_labels_invalid_format(self):
        """Test generating labels with invalid format."""
        # Set authentication header for staff user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Data with invalid format
        label_data = {
            'member_ids': [member.id for member in self.members[:3]],
            'format': 'invalid_format',  # Invalid format
            'start_position': 1,
            'include_department': True
        }

        response = self.client.post(self.print_labels_url, label_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('format', response.data['message'].lower())

    def test_print_labels_invalid_start_position(self):
        """Test generating labels with invalid start position."""
        # Set authentication header for staff user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Data with invalid start position
        label_data = {
            'member_ids': [member.id for member in self.members[:3]],
            'format': 'avery5160',
            'start_position': -1,  # Invalid start position
            'include_department': True
        }

        response = self.client.post(self.print_labels_url, label_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('position', response.data['message'].lower())

    def test_print_labels_unauthorized(self):
        """Test generating labels with a regular user (should be unauthorized)."""
        # Set authentication header for regular user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')

        # Data for label generation
        label_data = {
            'member_ids': [member.id for member in self.members[:3]],
            'format': 'avery5160',
            'start_position': 1,
            'include_department': True
        }

        response = self.client.post(self.print_labels_url, label_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['success'], False)
        self.assertIn('permission', response.data['message'].lower())

    def test_print_labels_unauthenticated(self):
        """Test generating labels without authentication."""
        # Remove authentication headers
        self.client.credentials()

        # Data for label generation
        label_data = {
            'member_ids': [member.id for member in self.members[:3]],
            'format': 'avery5160',
            'start_position': 1,
            'include_department': True
        }

        response = self.client.post(self.print_labels_url, label_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class UnverifiedUserExportViewTests(TestCase):
    """Test cases for the Unverified User Export view."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.export_url = reverse('core:unverified-users-export')

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Create a regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            active=True
        )

        # Get tokens for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)
        self.regular_token = str(RefreshToken.for_user(self.regular_user).access_token)

        # Create a department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create unverified users
        self.unverified_user1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Unverified One',
            department=self.department,
            home_phone='555-1111',
            active=False,  # Inactive user
            orig_join_date=timezone.now() - timedelta(days=10)
        )

        self.unverified_user2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Unverified Two',
            department=self.department,
            home_phone='555-2222',
            active=False,  # Inactive user
            orig_join_date=timezone.now() - timedelta(days=5)
        )

    def test_export_unverified_users_excel(self):
        """Test exporting unverified users as Excel."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        response = self.client.get(self.export_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertIn('attachment; filename=unverified_users_export.xlsx', response['Content-Disposition'])

        # Check that content is not empty (Excel file)
        self.assertTrue(len(response.content) > 0)

    def test_unauthorized_access(self):
        """Test accessing unverified users export with a regular user."""
        # Set authentication header for regular user
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')

        # Try to export unverified users
        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_access(self):
        """Test accessing unverified users export without authentication."""
        # Try to export unverified users
        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

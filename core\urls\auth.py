from django.urls import path
from django.utils.decorators import method_decorator
from rest_framework_simplejwt.views import TokenRefreshView

# Import auth views
from core.views.auth import (
    RegisterView,
    LoginView,
    LogoutView,
    ChangePasswordView,
    AdminChangePasswordView,
    UnverifiedUserListView,
    UserActivateView,
    VerifiedButInactiveUserListView,
    AdminVerifyEmailView,
    UserActivityListView,
    UserDetailView,
    DeleteUnverifiedUserView,
    DeleteVerifiedInactiveUserView
)

from core.views import (
    EmailVerificationView,
    ResendVerificationEmailView,
    RequestPasswordResetView,
    ValidatePasswordResetTokenView,
    PasswordResetConfirmView
)

# Import unverified users export view
from core.views.auth.export_unverified import UnverifiedUserExportView
# Import verified but inactive users export view
from core.views.auth.export_verified_inactive import VerifiedInactiveUserExportView

from common.utils import track_activity

# Decorate views with activity tracking

# Auth URLs
auth_urlpatterns = [
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('user-change-password/', AdminChangePasswordView.as_view(), name='admin-change-password'),
    
    # JWT token refresh
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # User detail view
    path('user-detail/', UserDetailView.as_view(), name='user-detail'),
    
    # Email verification routes
    path('verify-email/<uuid:verification_key>/', EmailVerificationView.as_view(), name='verify-email'),
    path('resend-verification-email/', ResendVerificationEmailView.as_view(), name='resend-verification-email'),
    path('admin-verify-email/<int:user_id>/', AdminVerifyEmailView.as_view(), name='admin-verify-email'),
    path('unverified-users/', UnverifiedUserListView.as_view(), name='unverified-users'), # Email Confirmations
    path('unverified-users/export/', UnverifiedUserExportView.as_view(), name='unverified-users-export'),
    path('delete-unverified-user/<int:user_id>/', DeleteUnverifiedUserView.as_view(), name='delete-unverified-user'),
    
    # Password reset routes
    path('request-password-reset/', RequestPasswordResetView.as_view(), name='request-password-reset'),
    path('validate-reset-token/', ValidatePasswordResetTokenView.as_view(), name='validate-reset-token'),
    path('validate-password-reset-token/<str:reset_key>/', ValidatePasswordResetTokenView.as_view(), name='validate-password-reset-token'),
    path('reset-password/', PasswordResetConfirmView.as_view(), name='reset-password'),
    path('password-reset-confirm/', PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
    
    # Admin user management 
    path('verified-inactive-users/', VerifiedButInactiveUserListView.as_view(), name='verified-inactive-users'),
    path('verified-inactive-users/export/', VerifiedInactiveUserExportView.as_view(), name='verified-inactive-users-export'),
    path('delete-verified-inactive-user/<int:user_id>/', DeleteVerifiedInactiveUserView.as_view(), name='delete-verified-inactive-user'),
    path('activate-user/<int:user_id>/', UserActivateView.as_view(), name='activate-user'),
    
    # User activity routes
    path('user-activities/', UserActivityListView.as_view(), name='user-activities'),
]

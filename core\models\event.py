from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from simple_history.models import HistoricalRecords

from core.models.event_config import EventConfig


class Event(models.Model):
    """
    Model for storing event information.
    """
    event_name = models.CharField(max_length=255)
    event_date = models.DateField()
    event_end_date = models.DateField(null=True, blank=True)
    event_location = models.CharField(max_length=255)
    event_description = models.TextField(blank=True, null=True)

    # Registration fees - now with defaults from EventConfig
    registration_fee_normal = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    registration_fee_late = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    guest_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    late_registration_date = models.DateField(null=True, blank=True)

    # Configuration reference - optional override of global settings
    config = models.ForeignKey(
        EventConfig,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='events',
        help_text="Configuration to use for this event. If not specified, the active configuration will be used."
    )

    # Capacity
    max_participants = models.PositiveIntegerField(null=True, blank=True)

    # Status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History
    history = HistoricalRecords()

    def __str__(self):
        return self.event_name

    class Meta:
        ordering = ['-event_date']

    def clean(self):
        """Validate model data."""
        # Validate end date is after or equal to start date
        if self.event_end_date and self.event_date and self.event_end_date < self.event_date:
            raise ValidationError({"event_end_date": "Event end date must be after or equal to the start date."})

        # Validate late registration date is before event date
        if self.late_registration_date and self.event_date and self.late_registration_date > self.event_date:
            raise ValidationError({"late_registration_date": "Late registration date must be before the event date."})

    def save(self, *args, **kwargs):
        """Override save to set default values from configuration if not provided."""
        # Get configuration to use
        config = self.config or EventConfig.get_active_config()

        # Set fee defaults if not provided
        if self.registration_fee_normal is None:
            self.registration_fee_normal = config.registration_fee_normal

        if self.registration_fee_late is None:
            self.registration_fee_late = config.registration_fee_late

        if self.guest_fee is None:
            self.guest_fee = config.guest_fee

        # Set max participants if not provided
        if self.max_participants is None:
            self.max_participants = config.default_max_participants

        # Set late registration date if not provided and event date exists
        if self.late_registration_date is None and self.event_date:
            # Calculate late registration date based on config
            self.late_registration_date = self.event_date - timezone.timedelta(days=config.days_until_late_registration)

        super().save(*args, **kwargs)

    @property
    def is_late_registration(self):
        """Check if current date is past the late registration date"""
        if not self.late_registration_date:
            return False
        return timezone.now().date() > self.late_registration_date

    @property
    def total_registrations(self):
        """Get the total number of registrations for this event"""
        return self.registrations.count()

    @property
    def get_effective_config(self):
        """Get the configuration being used for this event (either event-specific or global)."""
        return self.config or EventConfig.get_active_config()

    @property
    def spots_remaining(self):
        """Get the number of spots remaining for this event."""
        if self.max_participants is None:
            return None  # Unlimited capacity
        return max(0, self.max_participants - self.total_registrations)

    @property
    def is_at_capacity(self):
        """Check if the event is at capacity."""
        if self.max_participants is None:
            return False  # Unlimited capacity
        return self.total_registrations >= self.max_participants

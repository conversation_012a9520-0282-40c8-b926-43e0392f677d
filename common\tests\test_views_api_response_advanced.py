"""
Tests for advanced functionality of the APIResponse class in the common app.

This module contains comprehensive tests for advanced features of the APIResponse class,
including serialization, error handling, and integration with DRF.
"""
from django.test import TestCase
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.serializers import ValidationError

from common.views import APIResponse


class APIResponseAdvancedTests(TestCase):
    """Test cases for advanced functionality of the APIResponse class."""

    def test_api_response_serialization(self):
        """Test that APIResponse objects can be properly serialized to JSON."""
        # Create an APIResponse
        response = APIResponse(
            message="Test message",
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # Check that the serialized response contains the expected data
        self.assertIn(b'"message":"Test message"', serialized)
        self.assertIn(b'"success":true', serialized)
        self.assertIn(b'"data":{"key":"value"}', serialized)

    def test_api_response_with_complex_data(self):
        """Test APIResponse with complex nested data structures."""
        # Create a complex data structure
        complex_data = {
            "users": [
                {"id": 1, "name": "User 1", "roles": ["admin", "editor"]},
                {"id": 2, "name": "User 2", "roles": ["viewer"]}
            ],
            "metadata": {
                "total": 2,
                "page": 1,
                "settings": {
                    "per_page": 10,
                    "sort": "name"
                }
            }
        }

        # Create an APIResponse with the complex data
        response = APIResponse(
            message="Complex data response",
            data=complex_data,
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], "Complex data response")
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], complex_data)

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # Check that the serialized response contains the expected data
        self.assertIn(b'"users":', serialized)
        self.assertIn(b'"metadata":', serialized)
        self.assertIn(b'"roles":["admin","editor"]', serialized)

    def test_api_response_with_validation_error(self):
        """Test APIResponse handling of ValidationError objects."""
        # Create a ValidationError
        validation_error = ValidationError({
            'field1': ['Error 1', 'Error 2'],
            'field2': 'Error 3'
        })

        # Create an APIResponse with the validation error
        response = APIResponse(
            message="Validation failed",
            error=validation_error,
            status_code=status.HTTP_400_BAD_REQUEST
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], "Validation failed")
        self.assertEqual(response.data['success'], False)
        self.assertIsNone(response.data['data'])

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # Check that the serialized response contains the expected data
        self.assertIn(b'"message":"Validation failed"', serialized)
        self.assertIn(b'"success":false', serialized)

    def test_api_response_with_empty_message(self):
        """Test APIResponse with an empty message."""
        # Create an APIResponse with an empty message
        response = APIResponse(
            message="",
            data={"key": "value"},
            status_code=status.HTTP_200_OK,
            use_default_message=True
        )

        # Check the response data structure - should have a default message
        self.assertEqual(response.data['message'], "Operation completed successfully")
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

    def test_api_response_with_none_message(self):
        """Test APIResponse with a None message."""
        # Create an APIResponse with a None message
        response = APIResponse(
            message=None,
            data={"key": "value"},
            status_code=status.HTTP_200_OK,
            use_default_message=True
        )

        # Check the response data structure - should have a default message
        self.assertEqual(response.data['message'], "Operation completed successfully")
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

    def test_api_response_with_non_string_message(self):
        """Test APIResponse with a non-string message."""
        # Create an APIResponse with a non-string message
        response = APIResponse(
            message=123,
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure - should convert message to string
        self.assertEqual(response.data['message'], "123")
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

    def test_api_response_with_very_long_message(self):
        """Test APIResponse with a very long message."""
        # Create a very long message
        long_message = "A" * 1000

        # Create an APIResponse with the long message
        response = APIResponse(
            message=long_message,
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], long_message)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # Check that the serialized response contains the long message
        self.assertIn(f'"message":"{long_message}"'.encode(), serialized)

    def test_api_response_with_special_characters(self):
        """Test APIResponse with special characters in the message."""
        # Create a message with special characters
        special_message = "Special characters: !@#$%^&*()_+-=[]{}|;:'\",.<>/?`~"

        # Create an APIResponse with the special message
        response = APIResponse(
            message=special_message,
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], special_message)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # The serialized response should contain the escaped special characters
        # We don't check the exact string as escaping can vary, but we ensure it can be serialized
        self.assertIsNotNone(serialized)

    def test_api_response_with_unicode_characters(self):
        """Test APIResponse with Unicode characters in the message."""
        # Create a message with Unicode characters
        unicode_message = "Unicode characters: 你好, こんにちは, 안녕하세요, Привет, مرحبا"

        # Create an APIResponse with the Unicode message
        response = APIResponse(
            message=unicode_message,
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], unicode_message)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # The serialized response should contain the Unicode characters (possibly escaped)
        # We don't check the exact string as escaping can vary, but we ensure it can be serialized
        self.assertIsNotNone(serialized)

    def test_api_response_with_html_characters(self):
        """Test APIResponse with HTML characters in the message."""
        # Create a message with HTML characters
        html_message = "<script>alert('XSS');</script><b>Bold</b><i>Italic</i>"

        # Create an APIResponse with the HTML message
        response = APIResponse(
            message=html_message,
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], html_message)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {"key": "value"})

        # Serialize the response using DRF's JSONRenderer
        renderer = JSONRenderer()
        serialized = renderer.render(response.data)

        # The serialized response should contain the escaped HTML characters
        # We don't check the exact string as escaping can vary, but we ensure it can be serialized
        self.assertIsNotNone(serialized)

    def test_api_response_with_non_serializable_data(self):
        """Test APIResponse with non-serializable data."""
        # Create a class that is not JSON serializable
        class NonSerializable:
            def __init__(self):
                self.value = "test"

        # Create an APIResponse with non-serializable data
        # This should raise a TypeError when serialized
        response = APIResponse(
            message="Non-serializable data",
            data={"object": NonSerializable()},
            status_code=status.HTTP_200_OK
        )

        # Check the response data structure
        self.assertEqual(response.data['message'], "Non-serializable data")
        self.assertEqual(response.data['success'], True)

        # Attempting to serialize this would raise a TypeError
        # In a real application, DRF would handle this with its exception handlers

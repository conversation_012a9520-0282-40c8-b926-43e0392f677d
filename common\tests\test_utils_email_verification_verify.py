"""
Tests for the email verification validation functionality in the common app.

This module contains comprehensive tests for the verify_email function.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from common.utils.email import verify_email
from common.models import EmailVerification

User = get_user_model()


class VerifyEmailTests(TestCase):
    """Test cases for the verify_email function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a verification record
        self.verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )

    def test_verify_email_success(self):
        """Test successful email verification."""
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
        
        # Check that the verification was marked as verified
        self.verification.refresh_from_db()
        self.assertTrue(self.verification.verified)

    def test_verify_email_invalid_key(self):
        """Test email verification with invalid key."""
        # Call the function with a non-existent key
        user, error = verify_email(str(uuid.uuid4()))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_already_verified(self):
        """Test email verification with already verified key."""
        # Mark the verification as verified
        self.verification.verified = True
        self.verification.save()
        
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Email has already been verified")

    def test_verify_email_expired(self):
        """Test email verification with expired key."""
        # Set the verification to be expired
        self.verification.expires_at = timezone.now() - timedelta(days=1)
        self.verification.save()
        
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Verification link has expired")

    def test_verify_email_does_not_activate_user(self):
        """Test that verify_email does not activate the user."""
        # Ensure user is not active before verification
        self.user.active = False
        self.user.save()
        
        # Call the function
        verify_email(str(self.verification.key))
        
        # Check that the user is still not active
        self.user.refresh_from_db()
        self.assertFalse(self.user.active)

    def test_verify_email_with_invalid_uuid_format(self):
        """Test email verification with an invalid UUID format."""
        # Call the function with an invalid UUID
        user, error = verify_email("not-a-uuid")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_with_none_key(self):
        """Test email verification with a None key."""
        # Call the function with None
        user, error = verify_email(None)
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_with_empty_key(self):
        """Test email verification with an empty key."""
        # Call the function with an empty string
        user, error = verify_email("")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")
    
    def test_verify_email_with_deleted_user(self):
        """Test email verification when the user has been deleted."""
        # Create a verification for a user that will be deleted
        temp_user = User.objects.create_user(
            email='<EMAIL>',
            password='temppassword123',
            name='Temp User'
        )
        
        temp_verification = EmailVerification.objects.create(
            user=temp_user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        # Store the key before deleting the user
        temp_key = temp_verification.key
        
        # Delete the user
        temp_user.delete()
        
        # Call the function
        user, error = verify_email(str(temp_key))
        
        # Check the result - should fail because the user is gone
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")
    
    def test_verify_email_with_multiple_verifications(self):
        """Test email verification when a user has multiple verification records."""
        # Create a second verification for the same user
        second_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        # Verify the first verification
        user1, error1 = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertEqual(user1, self.user)
        self.assertIsNone(error1)
        
        # Verify the second verification
        user2, error2 = verify_email(str(second_verification.key))
        
        # Check the result
        self.assertEqual(user2, self.user)
        self.assertIsNone(error2)
        
        # Check that both verifications are marked as verified
        self.verification.refresh_from_db()
        second_verification.refresh_from_db()
        self.assertTrue(self.verification.verified)
        self.assertTrue(second_verification.verified)

# Frontend Routing Guide for PayPal Integration

## Overview
This guide explains how to set up frontend routing to handle PayPal payment callbacks and provide a seamless user experience.

## Configuration Setup

### 1. Backend Configuration
Set your frontend URL in Django settings:

```python
# settings.py
FRONTEND_BASE_URL = "https://yourfrontend.com"  # Production
# or
FRONTEND_BASE_URL = "http://localhost:3000"     # Development
```

### 2. PaymentConfig Default
The system falls back to the config default if not set in settings:

```python
# payments/config.py
class PaymentConfig:
    FRONTEND_BASE_URL = "http://localhost:3000"  # Default for development
```

## Frontend Routes Required

Your frontend application needs to handle these routes:

### 1. Payment Success Route
**Route**: `/payment-success`

**Query Parameters**:
- `payment_id`: The payment ID
- `transaction_id`: PayPal transaction ID

**Example URL**: 
```
https://yourfrontend.com/payment-success?payment_id=123&transaction_id=PAYPAL_TXN_123
```

### 2. Payment Cancelled Route
**Route**: `/payment-cancelled`

**Query Parameters**:
- `payment_id`: The payment ID (optional)
- `error`: Error type (optional)

**Example URLs**:
```
https://yourfrontend.com/payment-cancelled?payment_id=123
https://yourfrontend.com/payment-cancelled?error=payment_not_found
```

### 3. Payment Error Route
**Route**: `/payment-error`

**Query Parameters**:
- `error`: Error type
- `payment_id`: The payment ID (optional)

**Example URLs**:
```
https://yourfrontend.com/payment-error?error=paypal_api_error
https://yourfrontend.com/payment-error?error=processing_error
```

## Frontend Implementation Examples

### React Router Example

```jsx
// App.js
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PaymentSuccess from './components/PaymentSuccess';
import PaymentCancelled from './components/PaymentCancelled';
import PaymentError from './components/PaymentError';

function App() {
  return (
    <Router>
      <Routes>
        {/* Your other routes */}
        <Route path="/payment-success" element={<PaymentSuccess />} />
        <Route path="/payment-cancelled" element={<PaymentCancelled />} />
        <Route path="/payment-error" element={<PaymentError />} />
      </Routes>
    </Router>
  );
}
```

### Payment Success Component

```jsx
// components/PaymentSuccess.js
import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

function PaymentSuccess() {
  const [searchParams] = useSearchParams();
  const [paymentDetails, setPaymentDetails] = useState(null);
  
  const paymentId = searchParams.get('payment_id');
  const transactionId = searchParams.get('transaction_id');

  useEffect(() => {
    if (paymentId) {
      // Fetch payment details from your API
      fetchPaymentDetails(paymentId);
    }
  }, [paymentId]);

  const fetchPaymentDetails = async (id) => {
    try {
      const response = await fetch(`/api/payments/${id}/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setPaymentDetails(data);
    } catch (error) {
      console.error('Error fetching payment details:', error);
    }
  };

  return (
    <div className="payment-success">
      <h1>Payment Successful!</h1>
      <p>Your payment has been processed successfully.</p>
      
      {transactionId && (
        <div className="transaction-details">
          <p><strong>Transaction ID:</strong> {transactionId}</p>
          <p><strong>Payment ID:</strong> {paymentId}</p>
        </div>
      )}
      
      {paymentDetails && (
        <div className="payment-details">
          <p><strong>Amount:</strong> ${paymentDetails.total_amount}</p>
          <p><strong>Status:</strong> {paymentDetails.status}</p>
          <p><strong>Date:</strong> {paymentDetails.payment_date}</p>
        </div>
      )}
      
      <button onClick={() => window.location.href = '/dashboard'}>
        Continue to Dashboard
      </button>
    </div>
  );
}

export default PaymentSuccess;
```

### Payment Cancelled Component

```jsx
// components/PaymentCancelled.js
import React from 'react';
import { useSearchParams } from 'react-router-dom';

function PaymentCancelled() {
  const [searchParams] = useSearchParams();
  const paymentId = searchParams.get('payment_id');
  const error = searchParams.get('error');

  const handleRetryPayment = () => {
    if (paymentId) {
      // Redirect back to payment page with the same payment ID
      window.location.href = `/payment/${paymentId}`;
    } else {
      // Redirect to payments list
      window.location.href = '/payments';
    }
  };

  return (
    <div className="payment-cancelled">
      <h1>Payment Cancelled</h1>
      <p>Your payment was cancelled. No charges have been made.</p>
      
      {error && (
        <div className="error-details">
          <p><strong>Error:</strong> {error.replace('_', ' ')}</p>
        </div>
      )}
      
      <div className="actions">
        <button onClick={handleRetryPayment} className="btn-primary">
          Try Again
        </button>
        <button onClick={() => window.location.href = '/dashboard'} className="btn-secondary">
          Back to Dashboard
        </button>
      </div>
    </div>
  );
}

export default PaymentCancelled;
```

### Payment Error Component

```jsx
// components/PaymentError.js
import React from 'react';
import { useSearchParams } from 'react-router-dom';

function PaymentError() {
  const [searchParams] = useSearchParams();
  const error = searchParams.get('error');
  const paymentId = searchParams.get('payment_id');

  const getErrorMessage = (errorType) => {
    switch (errorType) {
      case 'paypal_api_error':
        return 'There was an issue with PayPal. Please try again.';
      case 'processing_error':
        return 'There was an error processing your payment. Please try again.';
      case 'missing_parameters':
        return 'Invalid payment request. Please start over.';
      case 'payment_not_found':
        return 'Payment not found. Please contact support.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  };

  return (
    <div className="payment-error">
      <h1>Payment Error</h1>
      <p>{getErrorMessage(error)}</p>
      
      <div className="actions">
        {paymentId && (
          <button 
            onClick={() => window.location.href = `/payment/${paymentId}`}
            className="btn-primary"
          >
            Try Again
          </button>
        )}
        <button 
          onClick={() => window.location.href = '/support'}
          className="btn-secondary"
        >
          Contact Support
        </button>
        <button 
          onClick={() => window.location.href = '/dashboard'}
          className="btn-secondary"
        >
          Back to Dashboard
        </button>
      </div>
    </div>
  );
}

export default PaymentError;
```

## Vue.js Router Example

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import PaymentSuccess from '@/views/PaymentSuccess.vue'
import PaymentCancelled from '@/views/PaymentCancelled.vue'
import PaymentError from '@/views/PaymentError.vue'

const routes = [
  // Your other routes
  {
    path: '/payment-success',
    name: 'PaymentSuccess',
    component: PaymentSuccess
  },
  {
    path: '/payment-cancelled',
    name: 'PaymentCancelled',
    component: PaymentCancelled
  },
  {
    path: '/payment-error',
    name: 'PaymentError',
    component: PaymentError
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
```

## Next.js Pages Example

```javascript
// pages/payment-success.js
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export default function PaymentSuccess() {
  const router = useRouter();
  const { payment_id, transaction_id } = router.query;
  const [paymentDetails, setPaymentDetails] = useState(null);

  useEffect(() => {
    if (payment_id) {
      fetchPaymentDetails(payment_id);
    }
  }, [payment_id]);

  // Implementation similar to React example
  
  return (
    <div>
      <h1>Payment Successful!</h1>
      {/* Rest of component */}
    </div>
  );
}
```

## Testing Frontend Routes

### 1. Test URLs Manually
Navigate to these URLs to test your routes:
```
http://localhost:3000/payment-success?payment_id=123&transaction_id=TXN_123
http://localhost:3000/payment-cancelled?payment_id=123
http://localhost:3000/payment-error?error=paypal_api_error
```

### 2. Integration Testing
Create tests that simulate the PayPal callback flow:

```javascript
// Example Jest test
test('handles payment success callback', () => {
  const mockPush = jest.fn();
  jest.mock('react-router-dom', () => ({
    useNavigate: () => mockPush,
    useSearchParams: () => [new URLSearchParams('payment_id=123&transaction_id=TXN_123')]
  }));

  render(<PaymentSuccess />);
  
  expect(screen.getByText('Payment Successful!')).toBeInTheDocument();
  expect(screen.getByText('TXN_123')).toBeInTheDocument();
});
```

## Production Considerations

### 1. HTTPS Required
- PayPal requires HTTPS for production callbacks
- Ensure your frontend domain uses HTTPS

### 2. Domain Configuration
- Set `FRONTEND_BASE_URL` to your production domain
- Ensure the domain is accessible from PayPal's servers

### 3. Error Handling
- Implement proper error boundaries
- Log frontend errors for debugging
- Provide clear user feedback

### 4. Security
- Validate payment status on backend
- Don't trust frontend-only payment confirmations
- Always verify with your backend API

---

**Note**: The backend automatically handles PayPal callbacks and redirects users to your frontend with the appropriate parameters. Your frontend just needs to handle these routes and provide a good user experience.

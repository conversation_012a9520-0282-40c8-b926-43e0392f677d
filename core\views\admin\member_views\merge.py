from django.core.cache import cache
from django.db import transaction
from rest_framework import status

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Member
from core.serializers import MergeMembersSerializer
from payments.models import Payment


class MergeMembersView(BaseAPIView):
    """
    Merge multiple members into one (admin only)
    """
    permission_classes = [IsStaffUser]

    @transaction.atomic
    def post(self, request):
        try:
            serializer = MergeMembersSerializer(data=request.data)
            if not serializer.is_valid():
                return APIResponse(
                    data=None,
                    message=f"Invalid input data: {serializer.errors}",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            members_list = serializer.validated_data['members_list']
            print(members_list)

            # The first member in the list is the primary member (to keep)
            # The second member in the list is the secondary member (to be deleted after merge)
            primary_member_id = members_list[0]
            secondary_member_id = members_list[1]

            # Get the primary member
            try:
                primary_member = Member.objects.get(id=primary_member_id)
            except Member.DoesNotExist:
                return APIResponse(
                    data=None,
                    message="Primary member not found",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Get the secondary member
            try:
                secondary_member = Member.objects.get(id=secondary_member_id)
            except Member.DoesNotExist:
                return APIResponse(
                    data=None,
                    message="Secondary member not found",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Get all fields from the Member model
            member_fields = [field.name for field in Member._meta.get_fields()
                            if not field.is_relation or field.many_to_one]

            # Skip these fields
            skip_fields = ['id', 'user', 'date_created', 'date_updated', 'password', 'last_login',
                          'is_superuser', 'groups', 'user_permissions', 'history']

            # Process each field
            for field_name in member_fields:
                # Skip certain fields
                if field_name in skip_fields:
                    continue

                # Get values from both members
                primary_value = getattr(primary_member, field_name)
                secondary_value = getattr(secondary_member, field_name)

                # For text fields, combine if both have content
                if isinstance(primary_value, str) and isinstance(secondary_value, str):
                    if primary_value and secondary_value and field_name == 'notes':
                        # For notes field, always combine
                        combined_value = f"{primary_value}\n{secondary_value}".strip()
                        setattr(primary_member, field_name, combined_value)
                    elif not primary_value and secondary_value:
                        # If primary is empty but secondary has value
                        setattr(primary_member, field_name, secondary_value)
                # For other fields, use secondary value if primary is None/empty/False
                elif primary_value in [None, '', False] and secondary_value not in [None, '', False]:
                    setattr(primary_member, field_name, secondary_value)

            # Update related objects (payments, etc.)
            # For ManyToManyField, we need to handle it differently
            for payment in Payment.objects.filter(covered_members=secondary_member):
                payment.covered_members.remove(secondary_member)
                payment.covered_members.add(primary_member)

            # For ForeignKey, we can use update
            Payment.objects.filter(payer=secondary_member).update(payer=primary_member)

            # Save the updated primary member
            primary_member.save()

            # Delete the secondary member
            secondary_member_id = secondary_member.id
            secondary_member.delete()

            # Clear cache after merge
            cache.delete('membership_roster_queryset')

            return APIResponse(
                data={
                    "primary_member_id": primary_member.id,
                    "merged_member_id": secondary_member_id
                },
                message=f"Members merged successfully. Member #{primary_member.id} was kept and member #{secondary_member_id} was merged into it.",
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            print(f"Error in MergeMembersView: {str(e)}")
            import traceback
            traceback.print_exc()
            return APIResponse(
                data=None,
                message=f"Error merging members: {str(e)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
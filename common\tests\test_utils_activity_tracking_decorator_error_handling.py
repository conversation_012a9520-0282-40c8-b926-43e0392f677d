"""
Tests for error handling in the activity tracking decorator.

This module contains tests specifically focused on error handling scenarios
for the track_activity decorator.
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse

from common.utils.activity_tracking import track_activity
from common.models import UserActivity

User = get_user_model()


class TrackActivityErrorHandlingTests(TestCase):
    """Test cases for error handling in the track_activity decorator."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()
        
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_track_activity_exception_handling(self, mock_create):
        """Test that exceptions in activity tracking don't affect the response."""
        # Mock UserActivity.objects.create to raise an exception
        mock_create.side_effect = Exception("Test exception")

        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Capture print output to check error logging
        with patch('builtins.print') as mock_print:
            # Call the view
            response = test_view(request)

            # Verify print was called with our error message
            self.assertTrue(mock_print.called)
            # Check if any of the print calls contain our error message
            message_found = False
            for call_args in mock_print.call_args_list:
                if isinstance(call_args[0][0], str) and "Error tracking activity" in call_args[0][0]:
                    message_found = True
                    break
            self.assertTrue(message_found, "Error tracking activity message not found in print calls")

        # Check that the response was returned correctly despite the exception
        self.assertEqual(response.content, b"Test response")

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_track_activity_database_error(self, mock_create):
        """Test handling of database errors in activity tracking."""
        # Mock UserActivity.objects.create to raise a database error
        from django.db import DatabaseError
        mock_create.side_effect = DatabaseError("Database error")

        # Define a function-based view with the decorator
        @track_activity("User performed database action")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Capture print output to check error logging
        with patch('builtins.print') as mock_print:
            # Call the view
            response = test_view(request)

            # Verify print was called with our error message
            self.assertTrue(mock_print.called)
            # Check if any of the print calls contain our error message
            message_found = False
            for call_args in mock_print.call_args_list:
                if isinstance(call_args[0][0], str) and "Error tracking activity" in call_args[0][0]:
                    message_found = True
                    break
            self.assertTrue(message_found, "Error tracking activity message not found in print calls")

        # Check that the response was returned correctly despite the exception
        self.assertEqual(response.content, b"Test response")

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_track_activity_integrity_error(self, mock_create):
        """Test handling of integrity errors in activity tracking."""
        # Mock UserActivity.objects.create to raise an integrity error
        from django.db import IntegrityError
        mock_create.side_effect = IntegrityError("Integrity error")

        # Define a function-based view with the decorator
        @track_activity("User performed action with integrity constraint")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Capture print output to check error logging
        with patch('builtins.print') as mock_print:
            # Call the view
            response = test_view(request)

            # Verify print was called with our error message
            self.assertTrue(mock_print.called)
            # Check if any of the print calls contain our error message
            message_found = False
            for call_args in mock_print.call_args_list:
                if isinstance(call_args[0][0], str) and "Error tracking activity" in call_args[0][0]:
                    message_found = True
                    break
            self.assertTrue(message_found, "Error tracking activity message not found in print calls")

        # Check that the response was returned correctly despite the exception
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_with_request_without_user(self):
        """Test track_activity decorator with a request that has no user attribute."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request without a user attribute
        request = self.factory.get('/test/')
        # Deliberately not setting request.user

        # Call the view
        response = test_view(request)

        # Check that no activity was recorded (should handle missing user gracefully)
        self.assertEqual(UserActivity.objects.count(), 0)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_with_invalid_user_object(self):
        """Test track_activity decorator with an invalid user object."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an invalid user object
        request = self.factory.get('/test/')
        request.user = "Not a user object"  # Invalid user object

        # Call the view
        response = test_view(request)

        # Check that no activity was recorded (should handle invalid user gracefully)
        self.assertEqual(UserActivity.objects.count(), 0)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_with_module_resolution_error(self):
        """Test activity tracking with simulated module resolution issues."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view(request)

        # Check that activity was recorded as expected
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_with_function_that_raises_exception(self):
        """Test track_activity decorator with a function that raises an exception."""
        # Define a function-based view with the decorator that raises an exception
        @track_activity("User triggered an error")
        def error_view(request):
            raise ValueError("Test error")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view and expect an exception
        with self.assertRaises(ValueError):
            error_view(request)

        # Check that no activity was recorded (exception prevented completion)
        self.assertEqual(UserActivity.objects.count(), 0)

    def test_track_activity_with_very_long_description(self):
        """Test track_activity decorator with a very long description."""
        # Create a very long description (over 255 chars)
        long_description = "This is a very long description that exceeds the maximum length allowed for activity descriptions. " * 5

        # Define a function-based view with the decorator and long description
        @track_activity(long_description)
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view(request)

        # Check that the activity was recorded with truncated description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        
        # Verify description was truncated to 255 characters
        self.assertEqual(len(activity.description), 255)
        self.assertTrue(activity.description.startswith(long_description[:250]))
        
        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

"""
Tests for dynamic descriptions in the activity tracking decorator.

This module contains tests specifically focused on dynamic descriptions
and context handling in the track_activity decorator.
"""
from unittest.mock import patch
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response

from common.utils.activity_tracking import track_activity
from common.models import UserActivity

User = get_user_model()


class TrackActivityDynamicDescriptionTests(TestCase):
    """Test cases for dynamic descriptions in the track_activity decorator."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()
        
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
    # Helper function for general description
    def get_user_description(self, request, *args, **kwargs):
        return f"User {request.user.name} viewed test page"
        
    # Helper function for args-based description
    def get_user_id_from_args(self, request, *args, **kwargs):
        user_id = args[0] if args else 'unknown'
        return f"User viewed profile {user_id}"
        
    # Helper function for kwargs-based description
    def get_user_id_from_kwargs(self, request, *args, **kwargs):
        user_id = kwargs.get('user_id', 'unknown')
        return f"User viewed profile {user_id}"
        
    # Helper function for request data-based description
    def get_action_from_request(self, request, *args, **kwargs):
        action = request.GET.get('action', 'viewed')
        return f"User {action} test page"
        
    # Helper function that raises an error
    def get_description_with_error(self, request, *args, **kwargs):
        raise ValueError("Description error")
        
    # Helper function that returns None
    def get_description_returning_none(self, request, *args, **kwargs):
        return None
        
    def test_track_activity_with_dynamic_description(self):
        """Test track_activity decorator with a dynamic description function."""
        # Define a function-based view with the decorator using dynamic description
        @track_activity(description=self.get_user_description)
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view(request)

        # Check that the activity was recorded with the dynamic description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, f"User {self.user.name} viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_with_dynamic_description_using_args(self):
        """Test track_activity decorator with a dynamic description using view args."""
        # Define a function-based view with the decorator using dynamic description
        @track_activity(description=self.get_user_id_from_args)
        def test_view(request, user_id):
            return HttpResponse(f"Profile for {user_id}")

        # Create a request with an authenticated user
        request = self.factory.get('/test/123/')
        request.user = self.user

        # Call the view with args
        response = test_view(request, '123')

        # Check that the activity was recorded with the dynamic description using args
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed profile 123")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Profile for 123")

    def test_track_activity_with_dynamic_description_using_kwargs(self):
        """Test track_activity decorator with a dynamic description using view kwargs."""
        # Define a function-based view with the decorator using dynamic description
        @track_activity(description=self.get_user_id_from_kwargs)
        def test_view(request, **kwargs):
            user_id = kwargs.get('user_id')
            return HttpResponse(f"Profile for {user_id}")

        # Create a request with an authenticated user
        request = self.factory.get('/test/123/')
        request.user = self.user

        # Call the view with kwargs
        response = test_view(request, user_id='123')

        # Check that the activity was recorded with the dynamic description using kwargs
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed profile 123")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Profile for 123")

    def test_track_activity_with_dynamic_description_in_class_view(self):
        """Test track_activity decorator with a dynamic description in a class-based view."""
        # Define a class-based view with the decorator using dynamic description
        class TestView:
            @track_activity(description=self.get_user_description)
            def get(self, request):
                return HttpResponse("Class view response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Create an instance of the view and call the method
        view = TestView()
        response = view.get(request)

        # Check that the activity was recorded with the dynamic description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, f"User {self.user.name} viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Class view response")

    def test_track_activity_with_dynamic_description_in_api_view(self):
        """Test track_activity decorator with a dynamic description in an APIView."""
        # Define an APIView with the decorator using dynamic description
        class TestAPIView(APIView):
            @track_activity(description=self.get_user_description)
            def get(self, request):
                return Response({"message": "API view response"})

        # Create a request with an authenticated user
        request = self.factory.get('/api/test/')
        request.user = self.user

        # Create an instance of the view and call the method
        view = TestAPIView()
        response = view.get(request)

        # Check that the activity was recorded with the dynamic description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, f"User {self.user.name} viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.data, {"message": "API view response"})

    def test_track_activity_with_dynamic_description_error(self):
        """Test track_activity decorator when dynamic description function raises an error."""
        # Define a function-based view with the decorator using dynamic description
        @track_activity(description=self.get_description_with_error)
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        # Expect the view to run successfully, even though the description func will error
        response = test_view(request)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

        # Check that activity WAS recorded with the FALLBACK description (function name)
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "test_view")

        # Optional: Check logs if necessary, but the primary check is fallback activity

    def test_track_activity_with_dynamic_description_returning_none(self):
        """Test track_activity decorator when dynamic description function returns None."""
        # Define a function-based view with the decorator using dynamic description
        @track_activity(description=self.get_description_returning_none)
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view(request)

        # Check that the activity was recorded with a fallback description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        # Should fall back to function name as description
        self.assertEqual(activity.description, "test_view")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_with_dynamic_description_using_request_data(self):
        """Test track_activity decorator with dynamic description using request data."""
        # Define a function-based view with the decorator using dynamic description
        @track_activity(description=self.get_action_from_request)
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user and query parameters
        request = self.factory.get('/test/?action=edited')
        request.user = self.user

        # Call the view
        response = test_view(request)

        # Check that the activity was recorded with the dynamic description using request data
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User edited test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

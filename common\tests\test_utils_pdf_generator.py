"""
Tests for the PDF generator utility functions in the common app.

This module contains tests for the PDF generation functionality,
including invoice generation and PDF response handling.
"""
import os
from unittest.mock import patch, MagicMock, mock_open
from io import BytesIO

from django.test import TestCase
from django.http import HttpResponse
from django.template.loader import get_template
from django.conf import settings

from common.utils.pdf_generator import generate_invoice_pdf


class GenerateInvoicePDFTests(TestCase):
    """Test cases for the generate_invoice_pdf function."""

    @patch('common.utils.pdf_generator.get_template')
    @patch('common.utils.pdf_generator.HTML')
    @patch('common.utils.pdf_generator.tempfile.NamedTemporaryFile')
    @patch('common.utils.pdf_generator.os.unlink')
    def test_generate_invoice_pdf_with_default_parameters(self, mock_unlink, mock_temp_file, mock_html, mock_get_template):
        """Test PDF generation with default parameters."""
        # Set up mocks
        mock_template = MagicMock()
        mock_template.render.return_value = '<html><body>Invoice</body></html>'
        mock_get_template.return_value = mock_template
        
        mock_temp_file_instance = MagicMock()
        mock_temp_file_instance.name = '/tmp/temp_file.html'
        mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance
        
        mock_pdf_writer = MagicMock()
        mock_html.return_value.write_pdf = mock_pdf_writer
        
        # Call the function
        response = generate_invoice_pdf('pdf/invoice.html')
        
        # Check that the template was rendered
        mock_get_template.assert_called_once_with('pdf/invoice.html')
        mock_template.render.assert_called_once_with({})
        
        # Check that a temporary file was created
        mock_temp_file.assert_called_once()
        mock_temp_file_instance.write.assert_called_once_with(b'<html><body>Invoice</body></html>')
        
        # Check that HTML was created and PDF was written
        mock_html.assert_called_once_with(filename='/tmp/temp_file.html')
        mock_pdf_writer.assert_called_once()
        
        # Check that the temporary file was deleted
        mock_unlink.assert_called_once_with('/tmp/temp_file.html')
        
        # Check the response
        self.assertIsInstance(response, HttpResponse)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="invoice.pdf"')

    @patch('common.utils.pdf_generator.get_template')
    @patch('common.utils.pdf_generator.HTML')
    @patch('common.utils.pdf_generator.tempfile.NamedTemporaryFile')
    @patch('common.utils.pdf_generator.os.unlink')
    def test_generate_invoice_pdf_with_custom_parameters(self, mock_unlink, mock_temp_file, mock_html, mock_get_template):
        """Test PDF generation with custom parameters."""
        # Set up mocks
        mock_template = MagicMock()
        mock_template.render.return_value = '<html><body>Custom Invoice</body></html>'
        mock_get_template.return_value = mock_template
        
        mock_temp_file_instance = MagicMock()
        mock_temp_file_instance.name = '/tmp/temp_file.html'
        mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance
        
        mock_pdf_writer = MagicMock()
        mock_html.return_value.write_pdf = mock_pdf_writer
        
        # Custom context and filename
        context = {
            'invoice_number': '12345',
            'date': '2023-01-01',
            'customer': 'Test Customer',
            'items': [{'name': 'Item 1', 'price': 10.99}]
        }
        filename = 'custom_invoice.pdf'
        
        # Call the function
        response = generate_invoice_pdf('pdf/invoice.html', context, filename)
        
        # Check that the template was rendered with the custom context
        mock_get_template.assert_called_once_with('pdf/invoice.html')
        mock_template.render.assert_called_once_with(context)
        
        # Check that a temporary file was created
        mock_temp_file.assert_called_once()
        mock_temp_file_instance.write.assert_called_once_with(b'<html><body>Custom Invoice</body></html>')
        
        # Check that HTML was created and PDF was written
        mock_html.assert_called_once_with(filename='/tmp/temp_file.html')
        mock_pdf_writer.assert_called_once()
        
        # Check that the temporary file was deleted
        mock_unlink.assert_called_once_with('/tmp/temp_file.html')
        
        # Check the response
        self.assertIsInstance(response, HttpResponse)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], f'attachment; filename="{filename}"')

    @patch('common.utils.pdf_generator.get_template')
    @patch('common.utils.pdf_generator.HTML')
    @patch('common.utils.pdf_generator.tempfile.NamedTemporaryFile')
    @patch('common.utils.pdf_generator.os.unlink')
    def test_generate_invoice_pdf_with_empty_context(self, mock_unlink, mock_temp_file, mock_html, mock_get_template):
        """Test PDF generation with an empty context."""
        # Set up mocks
        mock_template = MagicMock()
        mock_template.render.return_value = '<html><body>Empty Context Invoice</body></html>'
        mock_get_template.return_value = mock_template
        
        mock_temp_file_instance = MagicMock()
        mock_temp_file_instance.name = '/tmp/temp_file.html'
        mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance
        
        mock_pdf_writer = MagicMock()
        mock_html.return_value.write_pdf = mock_pdf_writer
        
        # Call the function with an empty context
        response = generate_invoice_pdf('pdf/invoice.html', {})
        
        # Check that the template was rendered with an empty context
        mock_get_template.assert_called_once_with('pdf/invoice.html')
        mock_template.render.assert_called_once_with({})
        
        # Check the response
        self.assertIsInstance(response, HttpResponse)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="invoice.pdf"')

    @patch('common.utils.pdf_generator.get_template')
    @patch('common.utils.pdf_generator.HTML')
    @patch('common.utils.pdf_generator.tempfile.NamedTemporaryFile')
    @patch('common.utils.pdf_generator.os.unlink')
    def test_generate_invoice_pdf_handles_unicode_content(self, mock_unlink, mock_temp_file, mock_html, mock_get_template):
        """Test PDF generation with Unicode content."""
        # Set up mocks
        mock_template = MagicMock()
        mock_template.render.return_value = '<html><body>Unicode Content: 你好, こんにちは, Привет</body></html>'
        mock_get_template.return_value = mock_template
        
        mock_temp_file_instance = MagicMock()
        mock_temp_file_instance.name = '/tmp/temp_file.html'
        mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance
        
        mock_pdf_writer = MagicMock()
        mock_html.return_value.write_pdf = mock_pdf_writer
        
        # Call the function
        response = generate_invoice_pdf('pdf/invoice.html')
        
        # Check that the template was rendered
        mock_get_template.assert_called_once_with('pdf/invoice.html')
        
        # Check that a temporary file was created with Unicode content
        mock_temp_file.assert_called_once()
        mock_temp_file_instance.write.assert_called_once_with(
            '<html><body>Unicode Content: 你好, こんにちは, Привет</body></html>'.encode('utf-8')
        )
        
        # Check the response
        self.assertIsInstance(response, HttpResponse)

from django_filters import rest_framework as filters
from payments.models.payment import Payment
from .base_filters import BaseBooleanFilterSet
from django.utils import timezone
import datetime
from django.db.models import Count

class DynamicFieldsPaymentFilter(BaseBooleanFilterSet):
    """
    Filter class for Payment model with dynamic field filtering,
    including custom boolean handling.
    """
    # Basic filters
    payer_name = filters.CharFilter(field_name='payer__name', lookup_expr='icontains',
                                  help_text='Filter by payer name (case-insensitive)')
    payer_email = filters.CharFilter(field_name='payer__email', lookup_expr='icontains',
                                   help_text='Filter by payer email (case-insensitive)')
    amount_min = filters.NumberFilter(field_name='amount', lookup_expr='gte',
                                    help_text='Filter by minimum amount')
    amount_max = filters.NumberFilter(field_name='amount', lookup_expr='lte',
                                    help_text='Filter by maximum amount')
    invoice_number = filters.Char<PERSON>ilter(lookup_expr='icontains',
                                      help_text='Filter by invoice number (case-insensitive)')
    po_number = filters.CharFilter(lookup_expr='icontains',
                                 help_text='Filter by PO number (case-insensitive)')
    paid_year = filters.NumberFilter(help_text='Filter by payment year')

    # Date filters
    date_from = filters.DateFilter(field_name='date', lookup_expr='gte',
                                 help_text='Filter by minimum date')
    date_to = filters.DateFilter(field_name='date', lookup_expr='lte',
                               help_text='Filter by maximum date')
    due_date_from = filters.DateFilter(field_name='due_date', lookup_expr='gte',
                                     help_text='Filter by minimum due date')
    due_date_to = filters.DateFilter(field_name='due_date', lookup_expr='lte',
                                   help_text='Filter by maximum due date')
    payment_date_from = filters.DateFilter(field_name='payment_date', lookup_expr='gte',
                                         help_text='Filter by minimum payment date')
    payment_date_to = filters.DateFilter(field_name='payment_date', lookup_expr='lte',
                                       help_text='Filter by maximum payment date')

    # Choice filters
    status = filters.CharFilter(method='filter_status',
                              help_text='Filter by payment status')
    payment_type = filters.CharFilter(method='filter_payment_type',
                                    help_text='Filter by payment type')
    payment_for = filters.CharFilter(method='filter_payment_for',
                                   help_text='Filter by payment purpose')

    # Boolean filters
    draft = filters.CharFilter(method='filter_boolean_field',
                             help_text='Filter by draft status (yes/no/all)')
    is_group_payment = filters.CharFilter(method='filter_is_group_payment',
                                        help_text='Filter by group payment status (yes/no/all)')

    # Ensure all filters have help_text by setting it at initialization
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set help_text for any filters that don't have it
        for name, filter_obj in self.filters.items():
            if not hasattr(filter_obj, 'help_text') or filter_obj.help_text is None:
                filter_obj.help_text = f"Filter by {name.replace('_', ' ')}"

    class Meta:
        model = Payment
        fields = [
            'payer_name', 'payer_email', 'amount_min', 'amount_max', 'invoice_number',
            'po_number', 'paid_year', 'date_from', 'date_to', 'due_date_from', 'due_date_to',
            'payment_date_from', 'payment_date_to', 'status', 'payment_type', 'payment_for',
            'draft', 'is_group_payment'
        ]

    def filter_queryset(self, queryset):
        """
        Override to handle empty querysets and prevent SQL injection
        """
        # Apply the standard filtering
        queryset = super().filter_queryset(queryset)

        # Return empty queryset if model is Member but we're filtering payments
        if queryset.model.__name__ == 'Member' and self.Meta.model.__name__ == 'Payment':
            return queryset.none()

        # Store the queryset in the instance for caching
        self._filtered_queryset = queryset
        return queryset

    @property
    def qs(self):
        """
        Override to provide caching of queryset results
        """
        if not hasattr(self, '_filtered_queryset'):
            self._filtered_queryset = super().qs
        return self._filtered_queryset

    def is_valid(self):
        """
        Override to validate filter inputs
        """
        # Basic validation for numeric fields
        try:
            if 'amount_min' in self.data and self.data['amount_min']:
                float(self.data['amount_min'])
            if 'amount_max' in self.data and self.data['amount_max']:
                float(self.data['amount_max'])
            if 'paid_year' in self.data and self.data['paid_year']:
                int(self.data['paid_year'])
        except (ValueError, TypeError):
            return False

        return True

    def filter_status(self, queryset, name, value):
        """
        Filter by payment status
        """
        if not value or value.lower() == 'all':
            return queryset

        # Case insensitive status filtering
        try:
            # Try to match the status value case-insensitively
            status_values = [s[0] for s in Payment.PaymentStatus.choices]
            matched_status = next((s for s in status_values if s.lower() == value.lower()), None)

            if matched_status:
                return queryset.filter(status=matched_status)
            return queryset.none()
        except (AttributeError, TypeError):
            return queryset.none()

    def filter_payment_type(self, queryset, name, value):
        """
        Filter by payment type
        """
        if not value or value.lower() == 'all':
            return queryset

        # Case insensitive payment type filtering
        try:
            # Try to match the payment type value case-insensitively
            type_values = [t[0] for t in Payment.PaymentType.choices]
            matched_type = next((t for t in type_values if t.lower() == value.lower()), None)

            if matched_type:
                return queryset.filter(payment_type=matched_type)
            return queryset.none()
        except (AttributeError, TypeError):
            return queryset.none()

    def filter_payment_for(self, queryset, name, value):
        """
        Filter by payment_for field
        """
        if not value or value.lower() == 'all':
            return queryset

        # Case insensitive payment_for filtering
        try:
            # Try to match the payment_for value case-insensitively
            for_values = [f[0] for f in Payment.PaymentFor.choices]
            matched_for = next((f for f in for_values if f.lower() == value.lower()), None)

            if matched_for:
                return queryset.filter(payment_for=matched_for)
            return queryset.none()
        except (AttributeError, TypeError):
            return queryset.none()

    def filter_is_group_payment(self, queryset, name, value):
        """
        Filter payments based on whether they are group payments or not.
        Group payments are defined as payments covering more than one member.
        """
        if not value or value.lower() == 'all':
            return queryset

        # Annotate the queryset with the count of covered members
        queryset = queryset.annotate(covered_member_count=Count('covered_members'))

        if value.lower() in ['yes', 'true']:
            return queryset.filter(covered_member_count__gt=1)
        elif value.lower() in ['no', 'false']:
            return queryset.filter(covered_member_count=1)
        
        return queryset

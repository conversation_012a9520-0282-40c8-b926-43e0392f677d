"""
Tests for the activity tracking decorator in the common app - advanced functionality.

This module contains tests for the advanced functionality of the track_activity decorator,
including authentication handling, error handling, and edge cases.
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response

from common.utils.activity_tracking import track_activity
from common.models import UserActivity

User = get_user_model()


class TrackActivityDecoratorAdvancedTests(TestCase):
    """Test cases for the advanced functionality of the track_activity decorator."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()
        
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )

    def test_track_activity_unauthenticated_user(self):
        """Test track_activity decorator with an unauthenticated user."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an unauthenticated user
        request = self.factory.get('/test/')
        request.user = None  # No user

        # Call the view
        response = test_view(request)

        # Check that no activity was recorded
        self.assertEqual(UserActivity.objects.count(), 0)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_anonymous_user(self):
        """Test track_activity decorator with an anonymous user."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an anonymous user
        request = self.factory.get('/test/')
        request.user = MagicMock()
        request.user.is_authenticated = False

        # Call the view
        response = test_view(request)

        # Check that no activity was recorded
        self.assertEqual(UserActivity.objects.count(), 0)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_track_activity_exception_handling(self, mock_create):
        """Test that exceptions in activity tracking don't affect the response."""
        # Mock UserActivity.objects.create to raise an exception
        mock_create.side_effect = Exception("Test exception")

        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Capture print output to check error logging
        with patch('builtins.print') as mock_print:
            # Call the view
            response = test_view(request)

            # Verify print was called with our error message
            self.assertTrue(mock_print.called)
            # Check if any of the print calls contain our error message
            message_found = False
            for call_args in mock_print.call_args_list:
                if isinstance(call_args[0][0], str) and "Error tracking activity" in call_args[0][0]:
                    message_found = True
                    break
            self.assertTrue(message_found, "Error tracking activity message not found in print calls")

        # Check that the response was returned correctly despite the exception
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_multiple_calls(self):
        """Test multiple calls to views with track_activity decorator."""
        # Define two function-based views with the decorator
        @track_activity("User viewed first page")
        def first_view(request):
            return HttpResponse("First response")

        @track_activity("User viewed second page")
        def second_view(request):
            return HttpResponse("Second response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call both views
        first_view(request)
        second_view(request)

        # Check that both activities were recorded
        self.assertEqual(UserActivity.objects.count(), 2)
        
        activities = UserActivity.objects.order_by('description')
        self.assertEqual(activities[0].description, "User viewed first page")
        self.assertEqual(activities[1].description, "User viewed second page")

    def test_track_activity_preserves_function_metadata(self):
        """Test that track_activity preserves function metadata."""
        # Define a function with docstring and attributes
        @track_activity("User viewed test page")
        def test_view(request):
            """Test view docstring."""
            return HttpResponse("Test response")
        
        test_view.custom_attr = "custom value"

        # Check that the docstring and attributes are preserved
        self.assertEqual(test_view.__doc__, "Test view docstring.")
        self.assertEqual(test_view.custom_attr, "custom value")
        self.assertEqual(test_view.__name__, "test_view")
    
    def test_track_activity_with_view_raising_exception(self):
        """Test track_activity when the decorated view raises an exception."""
        # Define a function-based view that raises an exception
        @track_activity("User viewed error page")
        def error_view(request):
            raise ValueError("Test error")

        # Create a request with an authenticated user
        request = self.factory.get('/error/')
        request.user = self.user

        # Call the view, which should raise the exception
        with self.assertRaises(ValueError) as context:
            error_view(request)
        
        # Check that the exception was raised
        self.assertEqual(str(context.exception), "Test error")
        
        # Check that no activity was recorded (since the view didn't complete)
        self.assertEqual(UserActivity.objects.count(), 0)
    
    def test_track_activity_with_nested_decorators(self):
        """Test track_activity with nested decorators."""
        # Define a simple decorator
        def simple_decorator(func):
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return wrapper
        
        # Define a function with nested decorators
        @simple_decorator
        @track_activity("User viewed nested page")
        def nested_view(request):
            return HttpResponse("Nested response")

        # Create a request with an authenticated user
        request = self.factory.get('/nested/')
        request.user = self.user

        # Call the view
        response = nested_view(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.description, "User viewed nested page")
        
        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Nested response")

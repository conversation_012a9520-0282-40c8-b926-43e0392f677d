"""
Comprehensive tests for the Department model.
"""
from django.test import TestCase
from django.db import IntegrityError, transaction, models
from django.core.exceptions import ValidationError
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Q, F, Count, Value, Case, When
from django.db.models.fields.related import RelatedField
from django.db.models.options import Options
from django.utils import timezone
from django.contrib.auth import get_user_model
import uuid
import json

from core.models import Department
from simple_history.models import HistoricalRecords

User = get_user_model()


class DepartmentModelBasicTests(TestCase):
    """Basic tests for the Department model."""

    def setUp(self):
        """Set up test data."""
        self.department_data = {
            'name': 'Test Department',
            'department_address1': '123 Main St',
            'department_address2': 'Suite 100',
            'department_city': 'Anytown',
            'department_district': 'Central',
            'department_county': 'Test County',
            'department_state': 'MS',
            'department_zip_code': '12345',
            'billing_address1': '456 Billing St',
            'billing_address2': 'Floor 2',
            'billing_city': 'Billtown',
            'billing_district': 'North',
            'billing_county': 'Bill County',
            'billing_state': 'MS',
            'billing_zip_code': '67890'
        }

    def test_create_department_with_minimal_fields(self):
        """Test creating a department with only required fields."""
        department = Department.objects.create(name='Minimal Department')
        self.assertEqual(department.name, 'Minimal Department')
        self.assertEqual(department.department_address1, '')
        self.assertEqual(department.department_address2, '')
        self.assertEqual(department.department_city, '')
        self.assertEqual(department.department_district, '')
        self.assertEqual(department.department_county, '')
        self.assertEqual(department.department_state, 'MS')
        self.assertEqual(department.department_zip_code, '')
        self.assertEqual(department.billing_address1, '')
        self.assertEqual(department.billing_address2, '')
        self.assertEqual(department.billing_city, '')
        self.assertEqual(department.billing_district, '')
        self.assertEqual(department.billing_county, '')
        self.assertEqual(department.billing_state, 'MS')
        self.assertEqual(department.billing_zip_code, '')

    def test_create_department_with_all_fields(self):
        """Test creating a department with all fields."""
        department = Department.objects.create(**self.department_data)
        
        # Check that all fields were saved correctly
        for field, value in self.department_data.items():
            self.assertEqual(getattr(department, field), value)

    def test_str_method(self):
        """Test the __str__ method returns the department name."""
        department = Department.objects.create(name='String Test Department')
        self.assertEqual(str(department), 'String Test Department')

    def test_meta_ordering(self):
        """Test that departments are ordered by name."""
        Department.objects.create(name='C Department')
        Department.objects.create(name='A Department')
        Department.objects.create(name='B Department')
        
        departments = Department.objects.all()
        self.assertEqual(departments[0].name, 'A Department')
        self.assertEqual(departments[1].name, 'B Department')
        self.assertEqual(departments[2].name, 'C Department')

    def test_department_id_auto_generated(self):
        """Test that department ID is auto-generated."""
        department = Department.objects.create(name='ID Test Department')
        self.assertIsNotNone(department.id)
        self.assertIsInstance(department.id, int)

    def test_department_model_verbose_name(self):
        """Test the verbose name of the Department model."""
        self.assertEqual(Department._meta.verbose_name, 'Department')
        self.assertEqual(Department._meta.verbose_name_plural, 'Departments')


class DepartmentModelConstraintsTests(TestCase):
    """Tests for the Department model constraints."""

    def test_name_unique_constraint(self):
        """Test that department name must be unique."""
        Department.objects.create(name='Unique Department')
        
        with self.assertRaises(IntegrityError):
            with transaction.atomic():
                Department.objects.create(name='Unique Department')

    def test_name_max_length(self):
        """Test the max length constraint on department name."""
        max_length = Department._meta.get_field('name').max_length
        self.assertEqual(max_length, 255)
        
        # Test with name at max length
        name_at_max = 'a' * 255
        department = Department(name=name_at_max)
        department.full_clean()  # Should not raise ValidationError
        
        # Test with name exceeding max length
        name_too_long = 'a' * 256
        department = Department(name=name_too_long)
        with self.assertRaises(ValidationError):
            department.full_clean()

    def test_address_fields_max_length(self):
        """Test the max length constraints on address fields."""
        address_fields = [
            'department_address1', 'department_address2', 'department_city',
            'department_district', 'department_county', 'billing_address1',
            'billing_address2', 'billing_city', 'billing_district', 'billing_county'
        ]
        
        for field_name in address_fields:
            field = Department._meta.get_field(field_name)
            self.assertEqual(field.max_length, 255)
            
            # Test with value at max length
            test_data = {field_name: 'a' * 255}
            department = Department(name='Test Max Length', **test_data)
            department.full_clean()  # Should not raise ValidationError
            
            # Test with value exceeding max length
            test_data = {field_name: 'a' * 256}
            department = Department(name='Test Max Length', **test_data)
            with self.assertRaises(ValidationError):
                department.full_clean()

    def test_state_fields_max_length(self):
        """Test the max length constraints on state fields."""
        state_fields = ['department_state', 'billing_state']
        
        for field_name in state_fields:
            field = Department._meta.get_field(field_name)
            self.assertEqual(field.max_length, 2)
            
            # Test with value at max length
            test_data = {field_name: 'MS'}
            department = Department(name='Test State Length', **test_data)
            department.full_clean()  # Should not raise ValidationError
            
            # Test with value exceeding max length
            test_data = {field_name: 'MST'}
            department = Department(name='Test State Length', **test_data)
            with self.assertRaises(ValidationError):
                department.full_clean()

    def test_zip_code_fields_max_length(self):
        """Test the max length constraints on zip code fields."""
        zip_fields = ['department_zip_code', 'billing_zip_code']
        
        for field_name in zip_fields:
            field = Department._meta.get_field(field_name)
            self.assertEqual(field.max_length, 10)
            
            # Test with value at max length
            test_data = {field_name: '1234567890'}
            department = Department(name='Test Zip Length', **test_data)
            department.full_clean()  # Should not raise ValidationError
            
            # Test with value exceeding max length
            test_data = {field_name: '12345678901'}
            department = Department(name='Test Zip Length', **test_data)
            with self.assertRaises(ValidationError):
                department.full_clean()

    def test_blank_fields(self):
        """Test that blank fields are allowed."""
        blank_fields = [
            'department_address1', 'department_address2', 'department_city',
            'department_district', 'department_county', 'department_zip_code',
            'billing_address1', 'billing_address2', 'billing_city',
            'billing_district', 'billing_county', 'billing_zip_code'
        ]
        
        for field_name in blank_fields:
            field = Department._meta.get_field(field_name)
            self.assertTrue(field.blank)

    def test_default_values(self):
        """Test default values for fields."""
        default_fields = {
            'department_address1': '',
            'department_address2': '',
            'department_city': '',
            'department_district': '',
            'department_county': '',
            'department_state': 'MS',
            'department_zip_code': '',
            'billing_address1': '',
            'billing_address2': '',
            'billing_city': '',
            'billing_district': '',
            'billing_county': '',
            'billing_state': 'MS',
            'billing_zip_code': ''
        }
        
        for field_name, expected_default in default_fields.items():
            field = Department._meta.get_field(field_name)
            self.assertEqual(field.default, expected_default)


class DepartmentModelRelationshipsTests(TestCase):
    """Tests for the Department model relationships."""

    def setUp(self):
        """Set up test data."""
        self.department = Department.objects.create(
            name='Relationship Test Department',
            department_city='Test City',
            department_state='MS'
        )

    def test_member_relationship(self):
        """Test the relationship between departments and members."""
        # Create users and assign to department
        user1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='User One',
            department=self.department
        )
        
        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='User Two',
            department=self.department
        )
        
        # Refresh from database
        self.department.refresh_from_db()
        
        # Test relationship from department to members
        self.assertEqual(self.department.members.count(), 2)
        self.assertIn(user1, self.department.members.all())
        self.assertIn(user2, self.department.members.all())
        
        # Test relationship from member to department
        self.assertEqual(user1.department, self.department)
        self.assertEqual(user2.department, self.department)

    def test_member_relationship_on_department_delete(self):
        """Test what happens to members when a department is deleted."""
        # Create a user and assign to department
        user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Delete Test User',
            department=self.department
        )
        
        # Delete the department
        self.department.delete()
        
        # Refresh the user from database
        user.refresh_from_db()
        
        # The user should still exist but have no department (SET_NULL)
        self.assertIsNone(user.department)

    def test_member_relationship_on_member_delete(self):
        """Test what happens to department when a member is deleted."""
        # Create a user and assign to department
        user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Member Delete Test User',
            department=self.department
        )
        
        # Delete the user
        user.delete()
        
        # Refresh the department from database
        self.department.refresh_from_db()
        
        # The department should still exist
        self.assertTrue(Department.objects.filter(id=self.department.id).exists())
        
        # The department should have no members
        self.assertEqual(self.department.members.count(), 0)


class DepartmentModelHistoryTests(TestCase):
    """Tests for the Department model history tracking."""

    def setUp(self):
        """Set up test data."""
        self.department = Department.objects.create(
            name='History Test Department',
            department_city='History City',
            department_state='MS'
        )

    def test_history_field_exists(self):
        """Test that the department model has a history field."""
        # Check that the history field exists and is a history manager
        self.assertTrue(hasattr(self.department, 'history'))
        self.assertTrue(hasattr(Department, 'history'))
        # Check that we can access history records through the manager
        self.assertTrue(hasattr(self.department.history, 'all'))
        self.assertTrue(callable(self.department.history.all))

    def test_history_on_create(self):
        """Test that a historical record is created when a department is created."""
        # Check that a historical record was created
        self.assertEqual(self.department.history.count(), 1)
        
        # Check that the historical record has the correct values
        historical_record = self.department.history.first()
        self.assertEqual(historical_record.name, 'History Test Department')
        self.assertEqual(historical_record.department_city, 'History City')
        self.assertEqual(historical_record.department_state, 'MS')
        
        # Check history metadata
        self.assertEqual(historical_record.history_type, '+')  # '+' indicates creation
        self.assertIsNotNone(historical_record.history_date)
        self.assertIsNone(historical_record.history_user)  # No user in test context

    def test_history_on_update(self):
        """Test that a historical record is created when a department is updated."""
        # Initial count of historical records
        initial_count = self.department.history.count()
        
        # Update the department
        self.department.name = 'Updated Department'
        self.department.department_city = 'Updated City'
        self.department.save()
        
        # Check that a new historical record was created
        self.assertEqual(self.department.history.count(), initial_count + 1)
        
        # Check that the historical record has the new values
        historical_record = self.department.history.first()
        self.assertEqual(historical_record.name, 'Updated Department')
        self.assertEqual(historical_record.department_city, 'Updated City')
        
        # Check history metadata
        self.assertEqual(historical_record.history_type, '~')  # '~' indicates update
        self.assertIsNotNone(historical_record.history_date)

    def test_history_on_delete(self):
        """Test that a historical record is created when a department is deleted."""
        # Get the ID for later reference
        dept_id = self.department.id
        
        # Delete the department
        self.department.delete()
        
        # Check that the historical record still exists
        historical_records = Department.history.filter(id=dept_id)
        self.assertTrue(historical_records.exists())
        
        # Check that the most recent historical record has a deletion flag
        latest_record = historical_records.first()
        self.assertEqual(latest_record.history_type, '-')  # '-' indicates deletion
        self.assertIsNotNone(latest_record.history_date)

    def test_history_tracks_all_fields(self):
        """Test that all fields are tracked in the historical records."""
        # Update all fields
        self.department.name = 'All Fields Department'
        self.department.department_address1 = '123 History St'
        self.department.department_address2 = 'Suite 100'
        self.department.department_city = 'History City'
        self.department.department_district = 'History District'
        self.department.department_county = 'History County'
        self.department.department_state = 'NY'
        self.department.department_zip_code = '12345'
        self.department.billing_address1 = '456 Billing St'
        self.department.billing_address2 = 'Floor 2'
        self.department.billing_city = 'Billing City'
        self.department.billing_district = 'Billing District'
        self.department.billing_county = 'Billing County'
        self.department.billing_state = 'CA'
        self.department.billing_zip_code = '67890'
        self.department.save()
        
        # Get the latest historical record
        historical_record = self.department.history.first()
        
        # Check that all fields were tracked
        self.assertEqual(historical_record.name, 'All Fields Department')
        self.assertEqual(historical_record.department_address1, '123 History St')
        self.assertEqual(historical_record.department_address2, 'Suite 100')
        self.assertEqual(historical_record.department_city, 'History City')
        self.assertEqual(historical_record.department_district, 'History District')
        self.assertEqual(historical_record.department_county, 'History County')
        self.assertEqual(historical_record.department_state, 'NY')
        self.assertEqual(historical_record.department_zip_code, '12345')
        self.assertEqual(historical_record.billing_address1, '456 Billing St')
        self.assertEqual(historical_record.billing_address2, 'Floor 2')
        self.assertEqual(historical_record.billing_city, 'Billing City')
        self.assertEqual(historical_record.billing_district, 'Billing District')
        self.assertEqual(historical_record.billing_county, 'Billing County')
        self.assertEqual(historical_record.billing_state, 'CA')
        self.assertEqual(historical_record.billing_zip_code, '67890')


class DepartmentModelEdgeCasesTests(TestCase):
    """Tests for edge cases in the Department model."""

    def test_empty_string_values(self):
        """Test that empty string values are handled correctly."""
        department = Department.objects.create(
            name='Empty String Department',
            department_address1='',
            department_address2='',
            department_city='',
            department_district='',
            department_county='',
            department_zip_code='',
            billing_address1='',
            billing_address2='',
            billing_city='',
            billing_district='',
            billing_county='',
            billing_zip_code=''
        )
        
        # Check that empty strings are saved as empty strings
        self.assertEqual(department.department_address1, '')
        self.assertEqual(department.department_address2, '')
        self.assertEqual(department.department_city, '')
        self.assertEqual(department.department_district, '')
        self.assertEqual(department.department_county, '')
        self.assertEqual(department.department_zip_code, '')
        self.assertEqual(department.billing_address1, '')
        self.assertEqual(department.billing_address2, '')
        self.assertEqual(department.billing_city, '')
        self.assertEqual(department.billing_district, '')
        self.assertEqual(department.billing_county, '')
        self.assertEqual(department.billing_zip_code, '')

    def test_whitespace_values(self):
        """Test that whitespace values are handled correctly."""
        department = Department.objects.create(
            name='Whitespace Department',
            department_address1=' ',
            department_city='  ',
            department_county='   '
        )
        
        # Check that whitespace is saved as is
        self.assertEqual(department.department_address1, ' ')
        self.assertEqual(department.department_city, '  ')
        self.assertEqual(department.department_county, '   ')

    def test_special_characters(self):
        """Test that special characters are handled correctly."""
        special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?\\`~"
        department = Department.objects.create(
            name=f'Special {special_chars} Department',
            department_address1=f'Address {special_chars}',
            department_city=f'City {special_chars}'
        )
        
        # Check that special characters are saved correctly
        self.assertEqual(department.name, f'Special {special_chars} Department')
        self.assertEqual(department.department_address1, f'Address {special_chars}')
        self.assertEqual(department.department_city, f'City {special_chars}')

    def test_unicode_characters(self):
        """Test that Unicode characters are handled correctly."""
        unicode_chars = "你好, こんにちは, Привет, مرحبا, 안녕하세요, Olá, Γειά σου, नमस्ते"
        department = Department.objects.create(
            name=f'Unicode {unicode_chars} Department',
            department_address1=f'Address {unicode_chars}',
            department_city=f'City {unicode_chars}'
        )
        
        # Check that Unicode characters are saved correctly
        self.assertEqual(department.name, f'Unicode {unicode_chars} Department')
        self.assertEqual(department.department_address1, f'Address {unicode_chars}')
        self.assertEqual(department.department_city, f'City {unicode_chars}')

    def test_very_long_values_within_limits(self):
        """Test that very long values within limits are handled correctly."""
        long_text = 'a' * 254  # Just under the 255 limit
        department = Department.objects.create(
            name=f'Long {long_text}',
            department_address1=f'Address {long_text}',
            department_city=f'City {long_text}'
        )
        
        # Check that long values are saved correctly
        self.assertEqual(len(department.name), 5 + len(long_text))
        self.assertEqual(len(department.department_address1), 8 + len(long_text))
        self.assertEqual(len(department.department_city), 5 + len(long_text))

    def test_json_serialization(self):
        """Test that Department objects can be serialized to JSON."""
        department = Department.objects.create(
            name='JSON Test Department',
            department_address1='123 JSON St',
            department_city='JSON City',
            department_state='MS'
        )
        
        # Try to serialize to JSON
        try:
            json_data = json.dumps({
                'id': department.id,
                'name': department.name,
                'department_address1': department.department_address1,
                'department_city': department.department_city,
                'department_state': department.department_state
            })
            # If we get here, serialization succeeded
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"JSON serialization failed: {str(e)}")


class DepartmentModelQueryTests(TestCase):
    """Tests for querying the Department model."""

    def setUp(self):
        """Set up test data."""
        # Create multiple departments
        self.dept1 = Department.objects.create(
            name='Alpha Department',
            department_city='Alpha City',
            department_state='MS'
        )
        
        self.dept2 = Department.objects.create(
            name='Beta Department',
            department_city='Beta City',
            department_state='AL'
        )
        
        self.dept3 = Department.objects.create(
            name='Gamma Department',
            department_city='Gamma City',
            department_state='TN'
        )

    def test_basic_query(self):
        """Test basic querying of departments."""
        # Query all departments
        departments = Department.objects.all()
        self.assertEqual(departments.count(), 3)
        
        # Query by ID
        department = Department.objects.get(id=self.dept1.id)
        self.assertEqual(department.name, 'Alpha Department')
        
        # Query by name
        department = Department.objects.get(name='Beta Department')
        self.assertEqual(department.department_city, 'Beta City')

    def test_filtering(self):
        """Test filtering departments."""
        # Filter by state
        ms_departments = Department.objects.filter(department_state='MS')
        self.assertEqual(ms_departments.count(), 1)
        self.assertEqual(ms_departments[0].name, 'Alpha Department')
        
        # Filter by city
        gamma_departments = Department.objects.filter(department_city='Gamma City')
        self.assertEqual(gamma_departments.count(), 1)
        self.assertEqual(gamma_departments[0].name, 'Gamma Department')
        
        # Filter by multiple criteria
        filtered_departments = Department.objects.filter(
            department_state='MS',
            department_city='Alpha City'
        )
        self.assertEqual(filtered_departments.count(), 1)
        self.assertEqual(filtered_departments[0].name, 'Alpha Department')

    def test_exclude(self):
        """Test excluding departments."""
        # Exclude by state
        non_ms_departments = Department.objects.exclude(department_state='MS')
        self.assertEqual(non_ms_departments.count(), 2)
        self.assertNotIn(self.dept1, non_ms_departments)
        
        # Exclude by city
        non_beta_departments = Department.objects.exclude(department_city='Beta City')
        self.assertEqual(non_beta_departments.count(), 2)
        self.assertNotIn(self.dept2, non_beta_departments)

    def test_ordering(self):
        """Test ordering departments."""
        # Order by name (default)
        departments = Department.objects.all()
        self.assertEqual(departments[0].name, 'Alpha Department')
        self.assertEqual(departments[1].name, 'Beta Department')
        self.assertEqual(departments[2].name, 'Gamma Department')
        
        # Order by name descending
        departments = Department.objects.order_by('-name')
        self.assertEqual(departments[0].name, 'Gamma Department')
        self.assertEqual(departments[1].name, 'Beta Department')
        self.assertEqual(departments[2].name, 'Alpha Department')
        
        # Order by state
        departments = Department.objects.order_by('department_state')
        states = [dept.department_state for dept in departments]
        self.assertEqual(states, ['AL', 'MS', 'TN'])

    def test_case_insensitive_query(self):
        """Test case-insensitive querying."""
        # Case-insensitive name query
        department = Department.objects.filter(name__iexact='alpha department').first()
        self.assertEqual(department.id, self.dept1.id)
        
        # Case-insensitive city query
        department = Department.objects.filter(department_city__iexact='BETA CITY').first()
        self.assertEqual(department.id, self.dept2.id)

    def test_partial_match_query(self):
        """Test partial match querying."""
        # Contains query
        departments = Department.objects.filter(name__contains='Department')
        self.assertEqual(departments.count(), 3)
        
        # Starts with query
        departments = Department.objects.filter(name__startswith='Alpha')
        self.assertEqual(departments.count(), 1)
        self.assertEqual(departments[0].id, self.dept1.id)
        
        # Ends with query
        departments = Department.objects.filter(department_city__endswith='City')
        self.assertEqual(departments.count(), 3)

    def test_query_with_related_objects(self):
        """Test querying with related objects."""
        # Create users and assign to departments
        user1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='User One',
            department=self.dept1
        )
        
        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='User Two',
            department=self.dept1
        )
        
        user3 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='User Three',
            department=self.dept2
        )
        
        # Query departments with members
        departments_with_members = Department.objects.filter(members__isnull=False).distinct()
        self.assertEqual(departments_with_members.count(), 2)
        
        # Query departments by member
        departments_with_user1 = Department.objects.filter(members=user1)
        self.assertEqual(departments_with_user1.count(), 1)
        self.assertEqual(departments_with_user1[0].id, self.dept1.id)
        
        # Query departments with multiple members
        departments_with_multiple_members = Department.objects.annotate(
            member_count=models.Count('members')
        ).filter(member_count__gt=1)
        self.assertEqual(departments_with_multiple_members.count(), 1)
        self.assertEqual(departments_with_multiple_members[0].id, self.dept1.id)


class DepartmentModelPerformanceTests(TestCase):
    """Tests for Department model performance."""

    @classmethod
    def setUpTestData(cls):
        """Set up test data once for all tests."""
        # Create a large number of departments
        for i in range(100):
            Department.objects.create(
                name=f'Department {i}',
                department_city=f'City {i}',
                department_state='MS' if i % 2 == 0 else 'AL'
            )

    def test_bulk_create(self):
        """Test bulk creation of departments."""
        # Create a list of departments
        departments = [
            Department(
                name=f'Bulk Department {i}',
                department_city=f'Bulk City {i}',
                department_state='MS'
            )
            for i in range(100, 200)
        ]
        
        # Bulk create
        created_departments = Department.objects.bulk_create(departments)
        
        # Check that all departments were created
        self.assertEqual(len(created_departments), 100)
        self.assertEqual(Department.objects.count(), 200)
        
        # Check that the departments have IDs
        self.assertIsNotNone(created_departments[0].id)

    def test_bulk_update(self):
        """Test bulk update of departments."""
        # Get all departments with state MS
        ms_departments = list(Department.objects.filter(department_state='MS'))
        
        # Update their state
        for dept in ms_departments:
            dept.department_state = 'TX'
        
        # Bulk update
        Department.objects.bulk_update(ms_departments, ['department_state'])
        
        # Check that the updates were applied
        self.assertEqual(Department.objects.filter(department_state='TX').count(), 50)
        self.assertEqual(Department.objects.filter(department_state='MS').count(), 0)

    def test_select_related_performance(self):
        """Test performance with select_related."""
        # Create users and assign to departments
        for i in range(10):
            dept = Department.objects.get(name=f'Department {i}')
            User.objects.create_user(
                email=f'user{i}@example.com',
                password='securepassword123',
                name=f'User {i}',
                department=dept
            )
        
        # Query users with select_related
        users_with_dept = User.objects.select_related('department').filter(
            department__name__startswith='Department'
        )
        
        # Check that we can access department attributes without additional queries
        with self.assertNumQueries(1):
            for user in users_with_dept:
                # Access department attributes
                dept_name = user.department.name
                dept_city = user.department.department_city
                dept_state = user.department.department_state

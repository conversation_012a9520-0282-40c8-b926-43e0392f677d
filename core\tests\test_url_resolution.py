"""
Tests for URL resolution in the core app.
"""
from django.test import TestCase
from django.urls import reverse, resolve, NoReverseMatch


class UrlResolutionTests(TestCase):
    """Test cases for URL resolution."""

    def test_admin_urls_resolution(self):
        """Test that admin URLs are correctly resolved."""
        # Test admin-member-export URL
        try:
            url = reverse('core:admin-member-export')
            self.assertEqual(url, '/api/admin/members/export/')
            resolver = resolve(url)
            self.assertEqual(resolver.view_name, 'core:admin-member-export')
        except NoReverseMatch:
            self.fail("URL 'core:admin-member-export' could not be reversed")
        
        # Test admin-event-roster-export URL
        try:
            url = reverse('core:admin-event-roster-export', kwargs={'event_id': 1})
            self.assertEqual(url, '/api/admin/events/1/export/')
            resolver = resolve(url)
            self.assertEqual(resolver.view_name, 'core:admin-event-roster-export')
        except NoReverseMatch:
            self.fail("URL 'core:admin-event-roster-export' could not be reversed")
        
        # Test admin-member-print-labels URL
        try:
            url = reverse('core:admin-member-print-labels')
            self.assertEqual(url, '/api/admin/members/print-labels/')
            resolver = resolve(url)
            self.assertEqual(resolver.view_name, 'core:admin-member-print-labels')
        except NoReverseMatch:
            self.fail("URL 'core:admin-member-print-labels' could not be reversed")

"""
Tests for the error formatting of non_field_errors in the common app.

This module contains tests for the error message formatting methods of the BaseAPIView class
and the format_error_message function in exception_handlers.py, specifically for non_field_errors.
"""
from django.test import TestCase
from rest_framework.exceptions import ErrorDetail

from common.views import BaseAPIView
from common.exception_handlers.exception_handlers import format_error_message


class MockView(BaseAPIView):
    """Mock view for testing BaseAPIView error formatting."""
    pass


class NonFieldErrorFormattingTests(TestCase):
    """Test cases for non_field_errors formatting."""

    def setUp(self):
        """Set up the test case."""
        self.view_instance = MockView()

    def test_format_error_message_with_non_field_errors(self):
        """Test _format_error_message with non_field_errors."""
        # Create an error dictionary with non_field_errors
        error_dict = {
            'non_field_errors': [
                ErrorDetail(string='Invalid credentials.', code='invalid')
            ]
        }
        
        # Call the method
        result = self.view_instance._format_error_message(error_dict)
        
        # Check the result - should not include 'non_field_errors:' prefix
        self.assertEqual(result, 'Invalid credentials.')
        self.assertNotIn('non_field_errors:', result)

    def test_format_error_message_with_multiple_non_field_errors(self):
        """Test _format_error_message with multiple non_field_errors."""
        # Create an error dictionary with multiple non_field_errors
        error_dict = {
            'non_field_errors': [
                ErrorDetail(string='Invalid credentials.', code='invalid'),
                ErrorDetail(string='Email must be a valid email.', code='invalid')
            ]
        }
        
        # Call the method
        result = self.view_instance._format_error_message(error_dict)
        
        # Check the result - should include both errors without 'non_field_errors:' prefix
        self.assertIn('Invalid credentials.', result)
        self.assertIn('Email must be a valid email.', result)
        self.assertNotIn('non_field_errors:', result)

    def test_format_error_message_with_mixed_errors(self):
        """Test _format_error_message with both non_field_errors and field-specific errors."""
        # Create an error dictionary with both non_field_errors and field-specific errors
        error_dict = {
            'non_field_errors': [
                ErrorDetail(string='Invalid credentials.', code='invalid')
            ],
            'email': [
                ErrorDetail(string='Email must be a valid email.', code='invalid')
            ]
        }
        
        # Call the method
        result = self.view_instance._format_error_message(error_dict)
        
        # Check the result - should include both errors, with field prefix only for field-specific errors
        self.assertIn('Invalid credentials.', result)
        self.assertIn('email: Email must be a valid email.', result)
        self.assertNotIn('non_field_errors:', result)

    def test_exception_handler_format_error_message_with_non_field_errors(self):
        """Test format_error_message in exception_handlers.py with non_field_errors."""
        # Create an error dictionary with non_field_errors
        error_dict = {
            'non_field_errors': [
                'Invalid credentials.'
            ]
        }
        
        # Call the function
        result = format_error_message(error_dict)
        
        # Check the result - should not include 'non_field_errors:' prefix
        self.assertEqual(result, 'Invalid credentials.')
        self.assertNotIn('non_field_errors:', result)

    def test_exception_handler_format_error_message_with_multiple_non_field_errors(self):
        """Test format_error_message in exception_handlers.py with multiple non_field_errors."""
        # Create an error dictionary with multiple non_field_errors
        error_dict = {
            'non_field_errors': [
                'Invalid credentials.',
                'Email must be a valid email.'
            ]
        }
        
        # Call the function
        result = format_error_message(error_dict)
        
        # Check the result - should include both errors without 'non_field_errors:' prefix
        self.assertIn('Invalid credentials.', result)
        self.assertIn('Email must be a valid email.', result)
        self.assertNotIn('non_field_errors:', result)

    def test_exception_handler_format_error_message_with_mixed_errors(self):
        """Test format_error_message in exception_handlers.py with both non_field_errors and field-specific errors."""
        # Create an error dictionary with both non_field_errors and field-specific errors
        error_dict = {
            'non_field_errors': [
                'Invalid credentials.'
            ],
            'email': [
                'Email must be a valid email.'
            ]
        }
        
        # Call the function
        result = format_error_message(error_dict)
        
        # Check the result - should include both errors, with field prefix only for field-specific errors
        self.assertIn('Invalid credentials.', result)
        self.assertIn('email: Email must be a valid email.', result)
        self.assertNotIn('non_field_errors:', result)

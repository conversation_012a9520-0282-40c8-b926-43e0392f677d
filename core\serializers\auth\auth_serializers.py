"""
Serializers for authentication views.
"""
from typing import Dict, Any, Optional

from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.password_validation import validate_password
from rest_framework import serializers
from rest_framework_simplejwt.tokens import <PERSON><PERSON><PERSON><PERSON><PERSON>, TokenError
from django.core.exceptions import ValidationError as DjangoValidationError

from common.models import EmailVerification
from .register_serializer import RegisterSerializer

Member = get_user_model()


class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    """
    email = serializers.EmailField(required=True)
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate user credentials and return tokens
        """
        email = attrs.get('email')
        password = attrs.get('password')
        request = self.context.get('request')

        if email and password:
            # First check if user exists (case-insensitive)
            user = Member.objects.filter(email__iexact=email).first()
            if not user:
                # Using a generic error message for security reasons
                raise serializers.ValidationError("Invalid credentials.")

            # Check if user has verified their email
            has_verified_email = EmailVerification.objects.filter(
                user=user,
                verified=True
            ).exists()

            if not has_verified_email:
                raise serializers.ValidationError("Email has not been verified. Please check your email for the verification link or request a new one.")

            # Check if user account is inactive (not yet approved by admin)
            if not user.active:
                raise serializers.ValidationError("Your account is awaiting approval by an administrator. Please contact support for assistance.")

            # Attempt authentication with credentials
            # Use the original email from the user object for authentication
            # This ensures the case matches what's in the database
            user = authenticate(
                request=request,
                email=user.email,
                password=password
            )

            if not user:
                raise serializers.ValidationError("Invalid credentials.")
        else:
            raise serializers.ValidationError("Must include 'email' and 'password'.")

        # Generate token
        refresh = RefreshToken.for_user(user)

        # Include basic user details with tokens

        return {
            'user': user,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
        }


class TokenRefreshSerializer(serializers.Serializer):
    """
    Serializer for refreshing JWT token
    """
    refresh = serializers.CharField(required=True)

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, str]:
        """
        Validate refresh token and return new access token
        """
        refresh_token = attrs.get('refresh')

        try:
            refresh = RefreshToken(refresh_token)
            return {
                'access': str(refresh.access_token),
                'refresh': str(refresh)
            }
        except TokenError:
            raise serializers.ValidationError("Invalid or expired refresh token.")


class PasswordMatchMixin:
    """
    Mixin to validate password confirmation matches
    """
    def validate_password_match(self, new_password: str, confirm_password: str) -> bool:
        """
        Check if passwords match
        """
        if new_password != confirm_password:
            raise serializers.ValidationError({
                "confirm_password": "Password fields didn't match."
            })
        return True


class ChangePasswordSerializer(serializers.Serializer, PasswordMatchMixin):
    """
    Serializer for password change endpoint
    """
    old_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'}
    )
    new_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'}
    )

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate passwords
        """
        # Check password match
        self.validate_password_match(
            attrs['new_password'],
            attrs['confirm_password']
        )
        return attrs

    def validate_old_password(self, value: str) -> str:
        """
        Validate that the old password is correct
        """
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError(
                "Current password is incorrect."
            )
        return value


class AdminChangePasswordSerializer(serializers.Serializer, PasswordMatchMixin):
    """
    Serializer for admin changing a user's password
    """
    user_id = serializers.IntegerField(required=True)
    new_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'}
    )

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate passwords match and user exists
        """
        # Check password match
        self.validate_password_match(
            attrs['new_password'],
            attrs['confirm_password']
        )

        # Verify user exists
        try:
            user = Member.objects.get(id=attrs['user_id'])
            # Store user instance for later use in view
            attrs['user'] = user
        except Member.DoesNotExist:
            raise serializers.ValidationError({
                "user_id": "User with this ID does not exist."
            })

        return attrs


class UserDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer for retrieving user details
    """
    role = serializers.SerializerMethodField()

    class Meta:
        model = Member
        fields = [
            'id', 'email', 'name', 'is_staff', 'is_superuser',
            'active', 'membership_active', 'date_created', 'date_updated', 'role',
            'membership_class', 'department', 'picture'
        ]
        read_only_fields = [
            'id', 'is_staff', 'is_superuser',
            'date_created', 'date_updated', 'active', 'membership_active', 'role'
        ]

    def get_role(self, obj: Member) -> str:
        """
        Get user role based on permissions
        """
        if obj.is_superuser:
            return "superuser"
        elif obj.is_staff:
            return "staff"
        return "user"

    def to_representation(self, instance: Member) -> Dict[str, Any]:
        """
        Customize representation based on requesting user
        """
        data = super().to_representation(instance)
        request = self.context.get('request')

        # If not an admin user, remove sensitive fields
        if request and request.user and not request.user.is_staff:
            # Regular users can only see limited info about other users
            if request.user.id != instance.id:
                allowed_fields = ['id', 'name', 'role', 'membership_class', 'picture']
                data = {k: v for k, v in data.items() if k in allowed_fields}

        return data


class UnverifiedUserListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing unverified users
    """
    department_name = serializers.SerializerMethodField()
    phone_number = serializers.CharField(source='home_phone', read_only=True)
    member_type = serializers.CharField(source='membership_class', read_only=True)
    is_email_verified = serializers.SerializerMethodField()
    date_joined = serializers.DateTimeField(source='orig_join_date', read_only=True)

    class Meta:
        model = Member
        fields = [
            'id', 'email', 'name', 'date_created',
            'department', 'department_name', 'phone_number',
            'member_type', 'is_email_verified', 'date_joined',
        ]
        read_only_fields = fields

    def get_department_name(self, obj: Member) -> str:
        """
        Get department name if department exists
        """
        if obj.department:
            return obj.department.name
        return ""

    def get_is_email_verified(self, obj: Member) -> bool:
        """
        Check if user's email is verified based on prefetched data
        """
        # If email_verifications is prefetched, use it directly
        if hasattr(obj, 'email_verifications'):
            return any(verification.verified for verification in obj.email_verifications.all())

        # Fallback to database query if not prefetched
        return EmailVerification.objects.filter(user=obj, verified=True).exists()
"""
Payment model for handling membership and event registration payments.
"""
from django.db import models
from django.utils import timezone
from simple_history.models import HistoricalRecords

from payments.models.validators import validate_invoice_format


class Payment(models.Model):
    """
    Payment model for handling both membership and event registration payments
    """
    class PaymentStatus(models.TextChoices):
        """Defines the possible payment statuses"""
        SUCCESS = 'success', 'Success'
        PENDING = 'pending', 'Pending'
        REFUND = 'refund', 'Refund'
        FAILED = 'failed', 'Failed'
        CANCELLED = 'cancelled', 'Cancelled'
        INVOICED = 'invoiced', 'Invoiced'
        ADJUSTMENT = 'adjustment', 'Adjustment'

    class PaymentType(models.TextChoices):
        """Defines the possible payment types"""
        PREPAID = 'prepaid', 'Prepaid'
        COLLECT = 'collect', 'Collect'
        CHECKS = 'checks', 'Checks'
        CASH = 'cash', 'Cash'
        PAYPAL = 'paypal', 'PayPal'
        MONEY_ORDER = 'money_order', 'Money Order'

    class PaymentFor(models.TextChoices):
        """Defines what the payment is for"""
        MEMBERSHIP = 'membership', 'Membership'
        EVENT = 'event', 'Event Registration'

    payer = models.ForeignKey(
        'core.Member',
        on_delete=models.CASCADE,
        related_name='payer_payments')
    covered_members = models.ManyToManyField(
        'core.Member', related_name='covered_payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2,
                               help_text="Per-member amount before multiplication")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2,
                                     null=True, blank=True,
                                     help_text="Total amount after multiplying by covered members count")
    invoice_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        validators=[validate_invoice_format],
        help_text="Format: YYDDD-XXX (year+julian_date-sequence)"
    )
    po_number = models.CharField(max_length=100, blank=True, null=True)
    paid_year = models.IntegerField(default=timezone.now().year)
    paid_next_year = models.CharField(max_length=100, blank=True, null=True)
    payment_link = models.URLField(max_length=200, blank=True, null=True)
    payment_id = models.CharField(max_length=365, blank=True, null=True)
    payment_date = models.DateField(blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING
    )
    notes = models.TextField(blank=True, null=True)
    payment_type = models.CharField(
        max_length=20,
        choices=PaymentType.choices,
        blank=True,
        null=True,
        default=""
    )
    draft = models.BooleanField(default=True)
    billing_address = models.CharField(max_length=255, blank=True, default="")
    transaction_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_response = models.JSONField(blank=True, null=True)
    due_date = models.DateField()

    # PayPal specific fields
    paypal_order_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_payer_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_payment_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    paypal_payment_status = models.CharField(max_length=100, blank=True, null=True)
    paypal_payment_method = models.CharField(max_length=100, blank=True, null=True)

    # Fields for handling both membership and event registration payments
    payment_for = models.CharField(
        max_length=20,
        choices=PaymentFor.choices,
        default=PaymentFor.MEMBERSHIP
    )
    event_registration = models.ForeignKey(
        'core.EventRegistration',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='payments'
    )

    history = HistoricalRecords()



    def save(self, *args, **kwargs):
        """
        Override save method with simplified logic using service layer.
        Handles invoice number generation and basic field defaults.
        """
        is_new = self.pk is None

        # For new payments, handle invoice number and due date
        if is_new:
            if self.invoice_number is None:
                # Import here to avoid circular imports
                from payments.services import PaymentService
                self.invoice_number = PaymentService.generate_invoice_number(self.payment_for)

            if not self.due_date:
                # Import here to avoid circular imports
                from payments.services import PaymentService
                default_due_date = PaymentService.get_default_due_date()
                if default_due_date:
                    self.due_date = default_due_date

            # If this is an event payment, get the amount from the event registration
            if (self.payment_for == self.PaymentFor.EVENT and
                    self.event_registration and not self.amount):
                self.amount = self.event_registration.total_amount

            # Set default total_amount if not provided
            if self.total_amount is None:
                self.total_amount = self.amount

        # Save the object
        super().save(*args, **kwargs)

    def __str__(self):
        """Return string representation of Payment"""
        payment_type = ("Event Registration" if self.payment_for ==
                        self.PaymentFor.EVENT else "Membership")
        return f"{payment_type} Payment {self.pk} by {self.payer}"
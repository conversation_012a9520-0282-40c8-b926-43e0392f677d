"""
Serializers for invoice due date models.
"""
from rest_framework import serializers
from payments.models.invoice_due_date import InvoiceDueDate


class InvoiceDueDateSerializer(serializers.ModelSerializer):
    """Serializer for InvoiceDueDate model"""
    class Meta:
        model = InvoiceDueDate
        fields = ['id', 'due_date', 'created', 'updated']
        read_only_fields = ['created', 'updated']

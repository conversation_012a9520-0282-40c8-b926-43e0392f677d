"""
Views module for the core app.
"""

# Import views from auth module
from .auth import (
    RegisterView,
    LoginView,
    LogoutView,
    ChangePasswordView,
    AdminChangePasswordView,
)

# Import email verification views
from .email_verification import (
    EmailVerificationView,
    ResendVerificationEmailView,
)

# Import password reset views
from .password_reset import (
    RequestPasswordResetView,
    ValidatePasswordResetTokenView,
    PasswordResetConfirmView,
)

# Import member views from admin module
from .admin.member_views import (
    MembershipRosterCreateAdminView,
    MembershipRosterListAdminView,
    MembershipRosterDetailAdminView,
    MembershipRosterUpdateAdminView,
    MembershipRosterDeleteAdminView,
    MembershipClassTypesAPIView,
    MergeMembersView,
    PrintLabelsView,
    MembershipRosterExportAdminView,
)

# Import department views from admin module
from .admin.department_views import (
    DepartmentCreateAPIView,
    DepartmentListView,
    DepartmentDetailAPIView,
    DepartmentUpdateAPIView,
    DepartmentDeleteAPIView,
    DepartmentMembersAPIView,
    DepartmentExportView,
    DepartmentMemberExportView,
)

# Import event views from admin module
from .admin.event_views import (
    EventRosterExportView,
)

# Import department payment views
from .department_payment_views import DepartmentPaymentsView
from .department_payment_export import DepartmentPaymentExportView

# Import public member views
from .public.member_views import (
    MembershipRosterPublicListView,
    MembershipRosterPublicDetailView,
    MembershipClassTypesPublicAPIView,
)

# Import public department views
from .public.department_views import (
    DepartmentPublicListView,
    DepartmentPublicDetailView,
    DepartmentPublicMembersAPIView,
)

# Import event views
from .events import (
    EventListCreateView,
    EventDetailView,
    EventInfoView,
    EventRegistrationListView,
    EventRegistrationDetailView,
    EventRegistrationByEventView,
    RegisterForEventView
)

# Export all views
__all__ = [
    # Auth views
    'RegisterView',
    'LoginView',
    'LogoutView',
    'ChangePasswordView',
    'AdminChangePasswordView',

    # Email verification views
    'EmailVerificationView',
    'ResendVerificationEmailView',

    # Password reset views
    'RequestPasswordResetView',
    'ValidatePasswordResetTokenView',
    'PasswordResetConfirmView',

    # Admin views - Members
    'MembershipRosterCreateAdminView',
    'MembershipRosterListAdminView',
    'MembershipRosterDetailAdminView',
    'MembershipRosterUpdateAdminView',
    'MembershipRosterDeleteAdminView',
    'MembershipClassTypesAPIView',
    'MergeMembersView',
    'PrintLabelsView',
    'MembershipRosterExportAdminView',

    # Admin views - Departments
    'DepartmentCreateAPIView',
    'DepartmentListView',
    'DepartmentDetailAPIView',
    'DepartmentUpdateAPIView',
    'DepartmentDeleteAPIView',
    'DepartmentMembersAPIView',
    'DepartmentPaymentsView',
    'DepartmentExportView',
    'DepartmentMemberExportView',
    'DepartmentPaymentExportView',

    # Admin views - Events
    'EventRosterExportView',

    # Public views - Members
    'MembershipRosterPublicListView',
    'MembershipRosterPublicDetailView',
    'MembershipClassTypesPublicAPIView',

    # Public views - Departments
    'DepartmentPublicListView',
    'DepartmentPublicDetailView',
    'DepartmentPublicMembersAPIView',

    # Event views
    'EventListCreateView',
    'EventDetailView',
    'EventInfoView',

    # Event Registration views
    'EventRegistrationListView',
    'EventRegistrationDetailView',
    'EventRegistrationByEventView',
    'RegisterForEventView',
]

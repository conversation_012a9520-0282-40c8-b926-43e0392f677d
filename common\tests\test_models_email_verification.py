"""
Tests for the EmailVerification model in the common app.

This module contains comprehensive tests for the EmailVerification model,
including creation, properties, and methods.
"""
import uuid
from datetime import timedelta

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model

from common.models import EmailVerification

User = get_user_model()


class EmailVerificationModelTests(TestCase):
    """Test cases for the EmailVerification model."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a verification record
        self.verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )

    def test_email_verification_creation(self):
        """Test that an EmailVerification instance can be created with correct attributes."""
        self.assertIsInstance(self.verification, EmailVerification)
        self.assertEqual(self.verification.user, self.user)
        self.assertFalse(self.verification.verified)
        self.assertIsNotNone(self.verification.key)
        self.assertIsNotNone(self.verification.created_at)
        self.assertIsNotNone(self.verification.expires_at)

    def test_email_verification_string_representation_unverified(self):
        """Test the string representation of an unverified EmailVerification."""
        expected_string = f"{self.user.email} - Unverified"
        self.assertEqual(str(self.verification), expected_string)
        
    def test_email_verification_string_representation_verified(self):
        """Test the string representation of a verified EmailVerification."""
        # Mark as verified
        self.verification.verified = True
        self.verification.save()
        
        expected_string = f"{self.user.email} - Verified"
        self.assertEqual(str(self.verification), expected_string)

    def test_email_verification_save_method_sets_expires_at(self):
        """Test the save method sets expires_at if not provided."""
        # Create a new verification without expires_at
        new_verification = EmailVerification(user=self.user)
        new_verification.save()
        
        # Check that expires_at was set
        self.assertIsNotNone(new_verification.expires_at)
        
        # Check that expires_at is approximately 30 days in the future
        expected_expiry = timezone.now() + timedelta(days=30)
        self.assertAlmostEqual(
            new_verification.expires_at.timestamp(),
            expected_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )
    
    def test_email_verification_save_method_preserves_expires_at(self):
        """Test the save method preserves expires_at if already set."""
        # Create a verification with a specific expiry date
        custom_expiry = timezone.now() + timedelta(days=15)
        verification = EmailVerification(
            user=self.user,
            expires_at=custom_expiry
        )
        verification.save()
        
        # Check that the custom expires_at was preserved
        self.assertAlmostEqual(
            verification.expires_at.timestamp(),
            custom_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )

    def test_is_expired_property_when_verified(self):
        """Test is_expired property returns True when verification is verified."""
        self.verification.verified = True
        self.verification.save()
        self.assertTrue(self.verification.is_expired)

    def test_is_expired_property_when_expired(self):
        """Test is_expired property returns True when expiry date has passed."""
        self.verification.expires_at = timezone.now() - timedelta(days=1)
        self.verification.save()
        self.assertTrue(self.verification.is_expired)

    def test_is_expired_property_when_not_expired(self):
        """Test is_expired property returns False when not expired and not verified."""
        self.verification.expires_at = timezone.now() + timedelta(days=1)
        self.verification.verified = False
        self.verification.save()
        self.assertFalse(self.verification.is_expired)

    def test_is_expired_property_with_none_expires_at(self):
        """Test is_expired property handles None expires_at value."""
        # Instead of testing with None (which violates the NOT NULL constraint),
        # we'll create a new instance without explicitly setting expires_at
        # and then test the is_expired property before saving
        verification = EmailVerification(
            user=self.user,
            key=uuid.uuid4(),
            verified=False
        )
        # Temporarily set expires_at to None for testing the property
        # (without saving to the database)
        verification.expires_at = None
        # The is_expired property should handle None gracefully
        self.assertFalse(verification.is_expired)
    
    def test_multiple_verifications_for_same_user(self):
        """Test that multiple verification records can exist for the same user."""
        # Create a second verification for the same user
        second_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        # Check that both verifications exist and are different
        verifications = EmailVerification.objects.filter(user=self.user)
        self.assertEqual(verifications.count(), 2)
        self.assertNotEqual(self.verification.key, second_verification.key)
    
    def test_unique_key_constraint(self):
        """Test that verification keys must be unique."""
        # Try to create a second verification with the same key
        with self.assertRaises(Exception):  # Should raise an integrity error
            EmailVerification.objects.create(
                user=self.user,
                key=self.verification.key,  # Same key as existing verification
                verified=False,
                expires_at=timezone.now() + timedelta(days=30)
            )

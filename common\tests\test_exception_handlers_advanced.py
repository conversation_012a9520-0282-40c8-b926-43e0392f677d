"""
Tests for advanced functionality of exception handlers in the common app.

This module contains comprehensive tests for the custom exception handlers,
focusing on different error types and scenarios.
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.core.exceptions import PermissionDenied
from django.http import Http404
from rest_framework.views import APIView
from rest_framework.exceptions import (
    APIException, NotAuthenticated, AuthenticationFailed,
    NotFound, MethodNotAllowed, ValidationError,
    Throttled, ParseError
)
from rest_framework import status

from common.exception_handlers import custom_exception_handler


class CustomExceptionHandlerAdvancedTests(TestCase):
    """Test cases for advanced functionality of the custom exception handler."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()
        
        # Create a request
        self.request = self.factory.get('/test/')
        
        # Create a mock context
        self.context = {'request': self.request}

    def test_handle_permission_denied(self):
        """Test handling of PermissionDenied exception."""
        # Create a PermissionDenied exception
        exc = PermissionDenied("You don't have permission to access this resource")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data['success'], False)
        self.assertIn("You don't have permission", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_http404(self):
        """Test handling of Http404 exception."""
        # Create a Http404 exception
        exc = Http404("Resource not found")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Resource not found", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_not_authenticated(self):
        """Test handling of NotAuthenticated exception."""
        # Create a NotAuthenticated exception
        exc = NotAuthenticated("Authentication credentials were not provided")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Authentication credentials were not provided", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_authentication_failed(self):
        """Test handling of AuthenticationFailed exception."""
        # Create an AuthenticationFailed exception
        exc = AuthenticationFailed("Invalid token")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Invalid token", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_not_found(self):
        """Test handling of NotFound exception."""
        # Create a NotFound exception
        exc = NotFound("Resource not found")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Resource not found", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_method_not_allowed(self):
        """Test handling of MethodNotAllowed exception."""
        # Create a MethodNotAllowed exception
        exc = MethodNotAllowed("GET")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Method 'GET' not allowed", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_validation_error(self):
        """Test handling of ValidationError exception."""
        # Create a ValidationError exception
        exc = ValidationError({
            'field1': ['Error 1', 'Error 2'],
            'field2': 'Error 3'
        })
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn("field1: Error 1", response.data['message'])
        self.assertIn("field1: Error 2", response.data['message'])
        self.assertIn("field2: Error 3", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_throttled(self):
        """Test handling of Throttled exception."""
        # Create a Throttled exception
        exc = Throttled(wait=60, detail="Request was throttled")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Request was throttled", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_parse_error(self):
        """Test handling of ParseError exception."""
        # Create a ParseError exception
        exc = ParseError("Malformed request")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Malformed request", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_custom_api_exception(self):
        """Test handling of a custom APIException."""
        # Create a custom APIException
        class CustomAPIException(APIException):
            status_code = status.HTTP_418_IM_A_TEAPOT
            default_detail = "I'm a teapot"
            default_code = 'teapot'
        
        exc = CustomAPIException()
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_418_IM_A_TEAPOT)
        self.assertEqual(response.data['success'], False)
        self.assertIn("I'm a teapot", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_exception_without_context(self):
        """Test handling of an exception without context."""
        # Create an exception
        exc = ValidationError("Validation error")
        
        # Call the exception handler without context
        response = custom_exception_handler(exc, None)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Validation error", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_exception_with_empty_context(self):
        """Test handling of an exception with empty context."""
        # Create an exception
        exc = ValidationError("Validation error")
        
        # Call the exception handler with empty context
        response = custom_exception_handler(exc, {})
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Validation error", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_exception_with_custom_status_code(self):
        """Test handling of an exception with a custom status code."""
        # Create an APIException with a custom status code
        class CustomStatusException(APIException):
            status_code = 499  # Custom status code
            default_detail = "Custom status exception"
        
        exc = CustomStatusException()
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, 499)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Custom status exception", response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_handle_exception_with_very_long_detail(self):
        """Test handling of an exception with a very long detail message."""
        # Create a very long detail message
        long_detail = "A" * 1000
        
        # Create an exception with the long detail
        exc = ValidationError(long_detail)
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], long_detail)
        self.assertIsNone(response.data['data'])

    def test_handle_non_api_exception(self):
        """Test handling of a non-API exception."""
        # Create a non-API exception
        exc = ValueError("Value error")
        
        # Call the exception handler
        response = custom_exception_handler(exc, self.context)
        
        # Check the response - should be None as non-API exceptions are not handled
        self.assertIsNone(response)

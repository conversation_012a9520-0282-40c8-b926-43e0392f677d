from django.shortcuts import get_object_or_404
from django.db.models import Exists, OuterRef
from rest_framework import status
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters
from rest_framework.filters import OrderingFilter

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from common.pagination import StandardPagination
from common.filters.member_filters import DynamicFieldsMemberFilter
from common.utils import track_activity
from core.models import Department, Member
from core.serializers import MembershipRosterAdminSerializer
from payments.models import Payment


class DepartmentMembersAPIView(BaseAPIView, ListAPIView):
    """
    List all members in a specific department with pagination and filtering (admin only)
    """
    serializer_class = MembershipRosterAdminSerializer
    permission_classes = [IsStaffUser]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend, OrderingFilter)
    filterset_class = DynamicFieldsMemberFilter
    ordering_fields = [
        'id', 'name', 'membership_active', 'dst', 'department__name', 'title',
        'mi', 'address', 'city', 'st', 'zip_code', 'home_phone', 'email',
        'executive_board', 'committee_member', 'committee', 'new_member',
        'orig_join_date', 'lifetime', 'is_deceased', 'lapel_pin', 'notes'
    ]
    ordering = ['name']

    def get_queryset(self):
        # Check if this is a schema generation request
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return Member.objects.none()

        # Get department ID from URL
        department_id = self.kwargs['pk']

        # Get the base queryset filtered by department
        queryset = Member.objects.filter(department_id=department_id).select_related('department')

        # Check if we need to filter by payment status for a specific year
        payment_year = self.request.query_params.get('year')
        payment_status = self.request.query_params.get('payment_status')

        if payment_year:
            try:
                year = int(payment_year)

                # Create subqueries to check if a member has paid for the specified year
                has_paid_as_payer = Payment.objects.filter(
                    payer=OuterRef('pk'),
                    paid_year=year,
                    payment_for='membership',
                    status='success'
                )

                has_paid_as_covered = Payment.objects.filter(
                    covered_members=OuterRef('pk'),
                    paid_year=year,
                    payment_for='membership',
                    status='success'
                )

                # Annotate the queryset with payment status
                queryset = queryset.annotate(
                    has_paid=Exists(has_paid_as_payer) | Exists(has_paid_as_covered)
                )

                # Filter by payment status if specified
                if payment_status == 'paid':
                    queryset = queryset.filter(has_paid=True)
                    print(queryset)
                elif payment_status == 'unpaid':
                    queryset = queryset.filter(has_paid=False)

            except ValueError:
                # If year is not a valid integer, ignore the filter
                pass

        return queryset.order_by('name')

    def get_paginated_response(self, data, department):
        """
        Custom paginated response to match the expected format in tests.
        """
        # Create a standard paginated response
        paginated_response = self.paginator.get_paginated_response(data)
        
        # Customize the response to use 'members' instead of 'results'
        response_data = {
            
                'results': paginated_response.data.get('results', []),
                'count': paginated_response.data.get('count', 0),
                'next': paginated_response.data.get('next'),
                'previous': paginated_response.data.get('previous')
            
        }
        
        return APIResponse(
            data=response_data,
            message=f"Members in department '{department.name}' retrieved successfully",
            status_code=status.HTTP_200_OK
        )

    @track_activity(description="Viewed department members")
    def list(self, request, *args, **kwargs):
        try:
            # Get the department
            department = get_object_or_404(Department, pk=kwargs['pk'])

            # Apply filters from filter backends
            queryset = self.filter_queryset(self.get_queryset())

            # Paginate the queryset
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data, department)

            serializer = self.get_serializer(queryset, many=True)
            return APIResponse(
                data={

                        'results': serializer.data,
                        'count': len(serializer.data),
                        'next': None,
                        'previous': None
            
                },
                message=f"Members in department '{department.name}' retrieved successfully",
                status_code=status.HTTP_200_OK
            )
        except Exception as e:
            return APIResponse(
                message=f"Error retrieving department members: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )


from django_filters import rest_framework as filters

class DepartmentFilter(filters.FilterSet):
    """
    Filter class for Department model
    """
    name = filters.CharFilter(lookup_expr='icontains')

    # Department address filters
    department_city = filters.CharFilter(lookup_expr='icontains')
    department_county = filters.CharFilter(lookup_expr='icontains')
    department_state = filters.CharFilter(lookup_expr='iexact')

    # Billing address filters
    billing_city = filters.CharFilter(lookup_expr='icontains')
    billing_county = filters.CharFilter(lookup_expr='icontains')
    billing_state = filters.CharFilter(lookup_expr='iexact')

    class Meta:
        model = None  # To be set in the view when used
        fields = [
            'name',
            'department_city', 'department_county', 'department_state',
            'billing_city', 'billing_county', 'billing_state'
        ]
"""
Security tests for the password reset functionality.

This test file focuses specifically on security aspects of the password reset
functionality, including:
- Information leakage
- Brute-force protection
- Token security
- Edge cases that could lead to security issues
"""
import uuid
from datetime import timedelta
from unittest.mock import patch, MagicMock

from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from common.models import PasswordReset
from common.utils.email import verify_password_reset_token
from core.serializers.auth.password_reset import (
    RequestPasswordResetSerializer,
    ValidateResetTokenSerializer,
    PasswordResetConfirmSerializer
)

User = get_user_model()


class PasswordResetInfoLeakageTests(TestCase):
    """Test cases for information leakage in password reset process."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:request-password-reset')
        
        # Create users with different statuses
        self.active_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Active User',
            active=True
        )
        
        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Inactive User',
            active=False
        )

    def test_identical_response_for_existing_and_nonexistent_users(self):
        """Test that responses are identical for existing and non-existent users."""
        # Request for active user
        active_response = self.client.post(self.url, {'email': self.active_user.email})
        
        # Request for inactive user
        inactive_response = self.client.post(self.url, {'email': self.inactive_user.email})
        
        # Request for non-existent user
        nonexistent_response = self.client.post(self.url, {'email': '<EMAIL>'})
        
        # All responses should have the same status code
        self.assertEqual(active_response.status_code, inactive_response.status_code)
        self.assertEqual(active_response.status_code, nonexistent_response.status_code)
        
        # All responses should have the same message
        self.assertEqual(active_response.data['message'], inactive_response.data['message'])
        self.assertEqual(active_response.data['message'], nonexistent_response.data['message'])

    def test_email_only_sent_to_active_users(self):
        """Test that password reset emails are only sent to active users."""
        with patch('core.serializers.auth.password_reset.send_password_reset_email') as mock_send_email:
            # Request for active user
            self.client.post(self.url, {'email': self.active_user.email})
            
            # Email should be sent for active user
            self.assertEqual(mock_send_email.call_count, 1)
            mock_send_email.reset_mock()
            
            # Request for inactive user
            self.client.post(self.url, {'email': self.inactive_user.email})
            
            # Email should not be sent for inactive user
            self.assertEqual(mock_send_email.call_count, 0)


class PasswordResetTokenSecurityTests(TestCase):
    """Test cases for token security in password reset process."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.validate_url = reverse('core:validate-reset-token')
        self.reset_url = reverse('core:reset-password')
        
        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )
        
        # Create a valid reset token
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_token_is_uuid(self):
        """Test that the reset token is a UUID."""
        self.assertIsInstance(self.valid_reset.key, uuid.UUID)

    def test_token_is_unique(self):
        """Test that reset tokens are unique."""
        # Create 10 tokens
        tokens = []
        for _ in range(10):
            reset = PasswordReset.objects.create(
                user=self.user,
                expires_at=timezone.now() + timedelta(hours=24)
            )
            tokens.append(reset.key)
        
        # Check that all tokens are unique
        self.assertEqual(len(tokens), len(set(tokens)))

    def test_token_invalidation_after_use(self):
        """Test that tokens are invalidated after use."""
        # Use the token once
        with patch('core.serializers.auth.password_reset.verify_password_reset_token') as mock_verify:
            mock_verify.return_value = (self.user, None)
            
            self.client.post(self.reset_url, {
                'token': self.valid_reset.key,
                'new_password': 'NewSecurePassword123!',
                'confirm_password': 'NewSecurePassword123!'
            })
        
        # Check that token is marked as used
        self.valid_reset.refresh_from_db()
        self.assertTrue(self.valid_reset.used)
        
        # Try to verify the token again
        user, error = verify_password_reset_token(self.valid_reset.key)
        self.assertIsNone(user)
        self.assertEqual(error, "This password reset link has already been used")

    def test_token_expiration(self):
        """Test that expired tokens cannot be used."""
        # Create an expired token
        expired_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(minutes=1)
        )
        
        # Try to verify the token
        user, error = verify_password_reset_token(expired_reset.key)
        self.assertIsNone(user)
        self.assertEqual(error, "Password reset link has expired")

    def test_token_uuid_validation(self):
        """Test validation of token UUID format."""
        # Valid UUID
        uuid_response = self.client.post(self.validate_url, {'token': str(uuid.uuid4())})
        self.assertEqual(uuid_response.status_code, status.HTTP_400_BAD_REQUEST)  # Invalid but proper format
        
        # Invalid formats
        invalid_formats = [
            'not-a-uuid',
            '123',
            'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
            '',
            '00000000-0000-0000-0000-000000000000',  # Nil UUID
        ]
        
        for invalid_format in invalid_formats:
            response = self.client.post(self.validate_url, {'token': invalid_format})
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn('token', response.data['data'])
        
        # Test with None separately
        response = self.client.post(self.validate_url, {})  # Omit token entirely
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class PasswordComplexityTests(TestCase):
    """Test cases for password complexity requirements."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )
        
        self.reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_validate_password_complexity(self):
        """Test validation of password complexity."""
        weak_passwords = [
            'password',
            '12345678',
            'qwerty',
            'test',
            'verylongpasswordbutstillweakwithnodigitsorsymbols'
        ]
        
        for weak_password in weak_passwords:
            with patch('core.serializers.auth.password_reset.verify_password_reset_token') as mock_verify:
                mock_verify.return_value = (self.user, None)
                
                serializer = PasswordResetConfirmSerializer(data={
                    'token': str(self.reset.key),
                    'new_password': weak_password,
                    'confirm_password': weak_password
                })
                self.assertFalse(serializer.is_valid(), f"Password '{weak_password}' should be rejected")
                self.assertIn('new_password', serializer.errors)

    def test_validate_password_minimum_length(self):
        """Test validation of password minimum length."""
        short_password = 'short'
        serializer = PasswordResetConfirmSerializer(data={
            'token': self.reset.key,
            'new_password': short_password,
            'confirm_password': short_password
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('new_password', serializer.errors)


class SerializerEdgeCaseTests(TestCase):
    """Test edge cases for the password reset serializers."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )
        
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_token_whitespace_handling(self):
        """Test handling of whitespace in tokens."""
        # Add whitespace to token
        token_with_whitespace = f" {self.valid_reset.key} "
        
        # Create serializer with whitespace
        serializer = ValidateResetTokenSerializer(data={'token': token_with_whitespace})
        
        # Should be invalid due to UUID validation
        self.assertFalse(serializer.is_valid())
        self.assertIn('token', serializer.errors)

    def test_serializer_extreme_values(self):
        """Test serializers with extreme or unusual values."""
        # Test RequestPasswordResetSerializer with very long email
        long_email = 'a' * 240 + '@example.com'  # Just under 254 chars which is the max for emails
        serializer = RequestPasswordResetSerializer(data={'email': long_email})
        self.assertTrue(serializer.is_valid())
        
        # Test ValidateResetTokenSerializer with unusual but valid UUID
        nil_uuid = '00000000-0000-0000-0000-000000000000'
        with patch('core.serializers.auth.password_reset.verify_password_reset_token') as mock_verify:
            mock_verify.return_value = (None, "Invalid token")
            serializer = ValidateResetTokenSerializer(data={'token': nil_uuid})
            # This should be valid from a format perspective but will fail token verification
            self.assertFalse(serializer.is_valid())
            self.assertIn('token', serializer.errors)
        
        # Test PasswordResetConfirmSerializer with identical but unusual passwords
        unusual_password = '!@#$%^&*()_+<>?:"{}|~`'
        serializer = PasswordResetConfirmSerializer(data={
            'token': self.valid_reset.key,
            'new_password': unusual_password,
            'confirm_password': unusual_password
        })
        # Should fail validation because it doesn't meet requirements
        self.assertFalse(serializer.is_valid())
        self.assertIn('new_password', serializer.errors)

    def test_race_condition_handling(self):
        """Test handling of race conditions in password reset process."""
        # Mock verify_password_reset_token to return our user
        with patch('core.serializers.auth.password_reset.verify_password_reset_token') as mock_verify:
            mock_verify.return_value = (self.user, None)
            
            # Create and validate serializer
            serializer = PasswordResetConfirmSerializer(data={
                'token': self.valid_reset.key,
                'new_password': 'NewSecurePassword123!',
                'confirm_password': 'NewSecurePassword123!'
            })
            self.assertTrue(serializer.is_valid())
            
            # Delete the token to simulate a race condition
            # where the token is deleted between validation and save
            self.valid_reset.delete()
            
            # Set the user manually (normally set during validation)
            serializer.user = self.user
            
            # Call save - should not raise an exception
            user = serializer.save()
            
            # Password should still be changed
            self.user.refresh_from_db()
            self.assertTrue(self.user.check_password('NewSecurePassword123!'))



class MultiFactorAuthTests(TestCase):
    """
    Test cases for multi-factor authentication in password reset.
    
    Note: These tests assume MFA would be implemented in the future.
    """

    def test_mfa_documentation(self):
        """
        Document that MFA should be considered for password reset.
        
        This is a placeholder test to document that multi-factor authentication
        should be considered for critical operations like password reset.
        """
        # This is a documentation test, not an actual test
        self.assertTrue(True) 

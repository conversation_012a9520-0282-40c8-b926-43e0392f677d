from django.db import models
import uuid
from datetime import timedelta
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

# Create your models here.

class EmailVerification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_verifications')
    key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.email} - {'Verified' if self.verified else 'Unverified'}"

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(days=30)
        return super().save(*args, **kwargs)

    @property
    def is_expired(self):
        # Make sure expires_at is not None before comparison
        return self.verified or (self.expires_at is not None and timezone.now() > self.expires_at)

    @property
    def used(self):
        # For backward compatibility with test cases
        return self.verified

    @used.setter
    def used(self, value):
        # For backward compatibility with test cases
        self.verified = value


class PasswordReset(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='password_resets')
    key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    def __str__(self):
        return f"{self.user.email} - {'Used' if self.used else 'Unused'}"

    def save(self, *args, **kwargs):
        if not self.pk and not self.expires_at:
            # Password reset tokens expire after 24 hours
            self.expires_at = timezone.now() + timedelta(hours=24)
        return super().save(*args, **kwargs)

    @property
    def is_expired(self):
        # Make sure expires_at is not None before comparison
        return self.used or (self.expires_at is not None and timezone.now() > self.expires_at)


class UserActivity(models.Model):
    """
    Model to track user activities such as page visits
    """
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='activities')
    description = models.CharField(max_length=255)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - {self.description} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'User Activity'
        verbose_name_plural = 'User Activities'

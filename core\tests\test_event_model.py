"""
Tests for the Event model.
This file contains tests that focus on all aspects of the Event model.
"""
from django.test import TestCase
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta, date
from decimal import Decimal
import json

from core.models import Event, EventRegistration, Member, EventConfig
from django.contrib.auth import get_user_model

User = get_user_model()


class EventModelTests(TestCase):
    """Comprehensive tests for the Event model."""

    def setUp(self):
        """Set up test data for each test."""
        # Dates for testing
        self.today = timezone.now().date()
        self.tomorrow = self.today + timedelta(days=1)
        self.next_week = self.today + timedelta(days=7)
        self.next_month = self.today + timedelta(days=30)
        self.yesterday = self.today - timedelta(days=1)
        self.last_week = self.today - timedelta(days=7)

        # Create a user for registration tests
        self.user = Member.objects.create(
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
        )

        # Create a default event config for tests
        self.config = EventConfig.objects.create(
            name="Test Config",
            description="Configuration for tests",
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=True
        )

        # Base event data for tests
        self.event_data = {
            'event_name': 'Test Event',
            'event_date': self.next_week,
            'event_end_date': self.next_week + timedelta(days=2),
            'event_location': 'Test Location',
            'event_description': 'This is a test event description',
            'registration_fee_normal': Decimal('100.00'),
            'registration_fee_late': Decimal('115.00'),
            'guest_fee': Decimal('50.00'),
            'late_registration_date': self.next_week - timedelta(days=3),
            'max_participants': 100,
            'is_active': True,
            'config': self.config
        }

    def test_event_creation_with_minimal_fields(self):
        """Test creating an event with only required fields."""
        event = Event.objects.create(
            event_name=self.event_data['event_name'],
            event_date=self.event_data['event_date'],
            event_location=self.event_data['event_location']
        )

        # Verify required fields
        self.assertEqual(event.event_name, self.event_data['event_name'])
        self.assertEqual(event.event_date, self.event_data['event_date'])
        self.assertEqual(event.event_location, self.event_data['event_location'])

        # Verify default values
        self.assertEqual(event.registration_fee_normal, self.config.registration_fee_normal)
        self.assertEqual(event.registration_fee_late, self.config.registration_fee_late)
        self.assertEqual(event.guest_fee, self.config.guest_fee)
        self.assertEqual(event.max_participants, self.config.default_max_participants)
        self.assertTrue(event.is_active)
        self.assertIsNone(event.event_end_date)
        self.assertIsNone(event.event_description)
        expected_late_date = event.event_date - timedelta(days=self.config.days_until_late_registration)
        self.assertEqual(event.late_registration_date, expected_late_date)

    def test_event_creation_with_all_fields(self):
        """Test creating an event with all fields."""
        event = Event.objects.create(**self.event_data)

        # Verify all fields were set correctly
        for field, value in self.event_data.items():
            self.assertEqual(getattr(event, field), value)

        # Verify auto fields
        self.assertIsNotNone(event.created_at)
        self.assertIsNotNone(event.updated_at)

    def test_event_string_representation(self):
        """Test the __str__ method returns the event name."""
        event = Event.objects.create(**self.event_data)
        self.assertEqual(str(event), self.event_data['event_name'])

    def test_event_meta_ordering(self):
        """Test that events are ordered by event_date in descending order."""
        # Create events with different dates
        event1 = Event.objects.create(
            event_name='First Event',
            event_date=self.tomorrow,
            event_location='Location 1'
        )

        event2 = Event.objects.create(
            event_name='Second Event',
            event_date=self.next_week,
            event_location='Location 2'
        )

        event3 = Event.objects.create(
            event_name='Third Event',
            event_date=self.next_month,
            event_location='Location 3'
        )

        # Get ordered events
        events = Event.objects.all()

        # Verify ordering is by event_date descending
        self.assertEqual(events[0], event3)  # Latest date first
        self.assertEqual(events[1], event2)
        self.assertEqual(events[2], event1)  # Earliest date last

    def test_event_date_validation(self):
        """Test validation that end date cannot be before start date."""
        # Create event with end_date before event_date
        event = Event(
            event_name='Date Validation Test',
            event_date=self.next_week,
            event_end_date=self.tomorrow,  # Before event_date
            event_location='Test Location'
        )

        # Should raise ValidationError when full_clean is called
        with self.assertRaises(ValidationError):
            event.full_clean()

        # Create valid event with end_date after event_date
        valid_event = Event(
            event_name='Valid Date Test',
            event_date=self.tomorrow,
            event_end_date=self.next_week,  # After event_date
            event_location='Test Location'
        )

        # Should not raise error
        try:
            valid_event.full_clean()
        except ValidationError:
            self.fail("ValidationError raised with valid end_date")

    def test_late_registration_date_validation(self):
        """Test validation that late registration date must be before event date."""
        # Create event with late_registration_date after event_date
        event = Event(
            event_name='Late Reg Date Test',
            event_date=self.next_week,
            late_registration_date=self.next_month,  # After event_date
            event_location='Test Location'
        )

        # Should raise ValidationError when full_clean is called
        with self.assertRaises(ValidationError):
            event.full_clean()

        # Create valid event with late_registration_date before event_date
        valid_event = Event(
            event_name='Valid Late Reg Test',
            event_date=self.next_week,
            late_registration_date=self.tomorrow,  # Before event_date
            event_location='Test Location'
        )

        # Should not raise error
        try:
            valid_event.full_clean()
        except ValidationError:
            self.fail("ValidationError raised with valid late_registration_date")

    def test_is_late_registration_property(self):
        """Test is_late_registration property correctly indicates late registration period."""
        # Create event with late_registration_date in the past
        past_late_date_event = Event.objects.create(
            event_name='Past Late Date Event',
            event_date=self.next_week,
            late_registration_date=self.yesterday,  # Late registration date passed
            event_location='Test Location'
        )

        # Should be in late registration period
        self.assertTrue(past_late_date_event.is_late_registration)

        # Create event with late_registration_date in the future
        future_late_date_event = Event.objects.create(
            event_name='Future Late Date Event',
            event_date=self.next_month,
            late_registration_date=self.next_week,  # Late registration date not passed
            event_location='Test Location'
        )

        # Should not be in late registration period
        self.assertFalse(future_late_date_event.is_late_registration)

        # Create event with no late_registration_date
        no_late_date_event = Event.objects.create(
            event_name='No Late Date Event',
            event_date=self.next_week,
            late_registration_date=None,  # No late registration date
            event_location='Test Location'
        )

        # Should not be in late registration period when no date is specified
        self.assertFalse(no_late_date_event.is_late_registration)

    def test_total_registrations_property(self):
        """Test total_registrations property returns the correct count of registrations."""
        # Create an event
        event = Event.objects.create(**self.event_data)

        # Initial count should be 0
        self.assertEqual(event.total_registrations, 0)

        # Create some registrations
        registration_data = {
            'first_name': 'Test',
            'last_name': 'User',
            'title': 'Mr.',
            'fire_department': 'Test Department',
            'address': '123 Test St',
            'city': 'Test City',
            'state': 'TS',
            'zipcode': '12345',
            'phone': '555-1234',
            'email': '<EMAIL>',
            'registration_type': 'NORMAL',
            'base_amount': Decimal('100.00'),
            'guest_amount': Decimal('0.00'),
            'total_amount': Decimal('100.00')
        }

        # Create 3 registrations
        for i in range(3):
            member = User.objects.create_user(
                email=f'member_for_total_reg_{i}@example.com',
                password='password',
                name=f'Member TotalReg {i}'
            )
            current_registration_data = registration_data.copy()
            current_registration_data['event'] = event
            current_registration_data['member'] = member
            current_registration_data['email'] = f'test{i}@example.com'
            EventRegistration.objects.create(**current_registration_data)

        # Refresh event from db to be sure
        event.refresh_from_db()

        # Should have 3 registrations
        self.assertEqual(event.total_registrations, 3)

    def test_active_events_filter(self):
        """Test that we can filter for active events."""
        # Create active and inactive events
        active_event = Event.objects.create(
            event_name='Active Event',
            event_date=self.next_week,
            event_location='Active Location',
            is_active=True
        )

        inactive_event = Event.objects.create(
            event_name='Inactive Event',
            event_date=self.next_week,
            event_location='Inactive Location',
            is_active=False
        )

        # Filter for active events
        active_events = Event.objects.filter(is_active=True)

        # Check that only active event is in result
        self.assertIn(active_event, active_events)
        self.assertNotIn(inactive_event, active_events)

        # Filter for inactive events
        inactive_events = Event.objects.filter(is_active=False)

        # Check that only inactive event is in result
        self.assertIn(inactive_event, inactive_events)
        self.assertNotIn(active_event, inactive_events)

    def test_event_history_tracking(self):
        """Test that historical records are created when events are updated."""
        # Create an event
        event = Event.objects.create(**self.event_data)

        # Initial history count should be 1 (creation)
        self.assertEqual(event.history.count(), 1)

        # Update the event
        event.event_name = 'Updated Event Name'
        event.save()

        # Should now have 2 historical records
        self.assertEqual(event.history.count(), 2)

        # Most recent history record should have updated name
        self.assertEqual(event.history.first().event_name, 'Updated Event Name')

    def test_event_capacity_check(self):
        """Test checking if an event has reached capacity."""
        # Create event with low max_participants
        event = Event.objects.create(
            event_name='Limited Event',
            event_date=self.next_week,
            event_location='Test Location',
            max_participants=2
        )

        # Initially event should not be at capacity
        self.assertEqual(event.total_registrations, 0)
        self.assertFalse(event.is_at_capacity)

        # Add registrations until at capacity
        registration_data = {
            'event': event,
            'first_name': 'Test',
            'last_name': 'User',
            'title': 'Mr.',
            'fire_department': 'Test Department',
            'address': '123 Test St',
            'city': 'Test City',
            'state': 'TS',
            'zipcode': '12345',
            'phone': '555-1234',
            'registration_type': 'NORMAL',
            'base_amount': Decimal('100.00'),
            'guest_amount': Decimal('0.00'),
            'total_amount': Decimal('100.00')
        }

        # Create 2 registrations (up to capacity)
        for i in range(2):
            registration_data['email'] = f'capacity{i}@example.com'
            EventRegistration.objects.create(**registration_data)

        # Refresh event from db
        event.refresh_from_db()

        # Now event should be at capacity
        self.assertEqual(event.total_registrations, 2)
        self.assertEqual(event.max_participants, 2)
        self.assertTrue(event.is_at_capacity)

        # Check spots_remaining property
        self.assertEqual(event.spots_remaining, 0)

        # Test event with unlimited capacity (max_participants = None)
        unlimited_event = Event.objects.create(
            event_name='Unlimited Event',
            event_date=self.next_week,
            event_location='Test Location',
            max_participants=None
        )

        # Explicitly set max_participants to None after save
        # This is needed because the save method sets it to the default from config
        Event.objects.filter(pk=unlimited_event.pk).update(max_participants=None)
        unlimited_event.refresh_from_db()

        # Should never be at capacity
        self.assertFalse(unlimited_event.is_at_capacity)

        # spots_remaining should be None
        self.assertIsNone(unlimited_event.spots_remaining)

    def test_multiple_events_same_date(self):
        """Test that multiple events can be created on the same date."""
        # Create multiple events on the same date
        event1 = Event.objects.create(
            event_name='Morning Event',
            event_date=self.next_week,
            event_location='Location A'
        )

        event2 = Event.objects.create(
            event_name='Afternoon Event',
            event_date=self.next_week,
            event_location='Location B'
        )

        event3 = Event.objects.create(
            event_name='Evening Event',
            event_date=self.next_week,
            event_location='Location C'
        )

        # Query events for that date
        events_on_date = Event.objects.filter(event_date=self.next_week)

        # Should have 3 events
        self.assertEqual(events_on_date.count(), 3)

        # All 3 events should be in the result
        self.assertIn(event1, events_on_date)
        self.assertIn(event2, events_on_date)
        self.assertIn(event3, events_on_date)

    def test_past_and_upcoming_events(self):
        """Test filtering for past and upcoming events."""
        # Create past event
        past_event = Event.objects.create(
            event_name='Past Event',
            event_date=self.yesterday,
            event_location='Past Location'
        )

        # Create current (today) event
        current_event = Event.objects.create(
            event_name='Current Event',
            event_date=self.today,
            event_location='Current Location'
        )

        # Create future event
        future_event = Event.objects.create(
            event_name='Future Event',
            event_date=self.tomorrow,
            event_location='Future Location'
        )

        # Get past events (events with date before today)
        past_events = Event.objects.filter(event_date__lt=self.today)

        # Get today's events
        todays_events = Event.objects.filter(event_date=self.today)

        # Get upcoming events (events with date >= today)
        upcoming_events = Event.objects.filter(event_date__gte=self.today)

        # Verify past events
        self.assertEqual(past_events.count(), 1)
        self.assertIn(past_event, past_events)

        # Verify today's events
        self.assertEqual(todays_events.count(), 1)
        self.assertIn(current_event, todays_events)

        # Verify future events
        self.assertEqual(upcoming_events.count(), 2)  # Current and future events
        self.assertIn(current_event, upcoming_events)
        self.assertIn(future_event, upcoming_events)

    def test_multi_day_events(self):
        """Test multi-day events with start and end dates."""
        # Create multi-day event
        multi_day_event = Event.objects.create(
            event_name='Multi-day Event',
            event_date=self.tomorrow,
            event_end_date=self.next_week,
            event_location='Multi-day Location'
        )

        # Verify dates are set correctly
        self.assertEqual(multi_day_event.event_date, self.tomorrow)
        self.assertEqual(multi_day_event.event_end_date, self.next_week)

        # Calculate duration
        duration = (multi_day_event.event_end_date - multi_day_event.event_date).days
        self.assertEqual(duration, 6)  # 7 days including end day

        # Test that an event can happen on a single day (start and end the same)
        single_day_event = Event.objects.create(
            event_name='Single-day Event',
            event_date=self.tomorrow,
            event_end_date=self.tomorrow,
            event_location='Single-day Location'
        )

        # Verify dates are set correctly
        self.assertEqual(single_day_event.event_date, self.tomorrow)
        self.assertEqual(single_day_event.event_end_date, self.tomorrow)

    def test_event_update(self):
        """Test updating an event."""
        # Create an event
        event = Event.objects.create(**self.event_data)

        # Store original updated_at timestamp
        original_updated_at = event.updated_at

        # Update the event
        new_name = 'Updated Event Name'
        new_location = 'Updated Location'
        new_description = 'Updated Description'
        new_fee_normal = Decimal('120.00')
        new_fee_late = Decimal('135.00')
        new_guest_fee = Decimal('60.00')

        # Apply updates
        event.event_name = new_name
        event.event_location = new_location
        event.event_description = new_description
        event.registration_fee_normal = new_fee_normal
        event.registration_fee_late = new_fee_late
        event.guest_fee = new_guest_fee
        event.save()

        # Refresh from database
        event.refresh_from_db()

        # Verify updates
        self.assertEqual(event.event_name, new_name)
        self.assertEqual(event.event_location, new_location)
        self.assertEqual(event.event_description, new_description)
        self.assertEqual(event.registration_fee_normal, new_fee_normal)
        self.assertEqual(event.registration_fee_late, new_fee_late)
        self.assertEqual(event.guest_fee, new_guest_fee)

        # Verify updated_at was updated
        self.assertNotEqual(event.updated_at, original_updated_at)

    def test_event_delete(self):
        """Test deleting an event."""
        # Create an event
        event = Event.objects.create(**self.event_data)

        # Verify it exists
        self.assertTrue(Event.objects.filter(pk=event.pk).exists())

        # Delete the event
        event_id = event.id
        event.delete()

        # Verify it no longer exists
        self.assertFalse(Event.objects.filter(pk=event_id).exists())

    def test_event_delete_with_registrations(self):
        """Test deleting an event with registrations deletes the registrations too."""
        # Create an event
        event = Event.objects.create(**self.event_data)

        # Create some registrations
        registration_data = {
            'first_name': 'Test',
            'last_name': 'User',
            'title': 'Mr.',
            'fire_department': 'Test Department',
            'address': '123 Test St',
            'city': 'Test City',
            'state': 'TS',
            'zipcode': '12345',
            'phone': '555-1234',
            'email': '<EMAIL>',
            'registration_type': 'NORMAL',
            'base_amount': Decimal('100.00'),
            'guest_amount': Decimal('0.00'),
            'total_amount': Decimal('100.00')
        }

        # Create 3 registrations
        registrations = []
        for i in range(3):
            member = User.objects.create_user(
                email=f'member_for_del_reg_{i}@example.com',
                password='password',
                name=f'Member DelReg {i}'
            )
            current_registration_data = registration_data.copy()
            current_registration_data['event'] = event
            current_registration_data['member'] = member
            current_registration_data['email'] = f'delete-test{i}@example.com'
            reg = EventRegistration.objects.create(**current_registration_data)
            registrations.append(reg.id)

        # Verify registrations exist
        self.assertEqual(EventRegistration.objects.filter(event=event).count(), 3)

        # Delete the event
        event.delete()

        # Verify registrations were deleted (cascade delete)
        for reg_id in registrations:
            self.assertFalse(EventRegistration.objects.filter(pk=reg_id).exists())

    def test_event_serialization(self):
        """Test that we can serialize event models to JSON-compatible format."""
        event = Event.objects.create(**self.event_data)

        # Try to serialize to JSON-compatible format
        # This is a simple test to ensure the model can be serialized
        data = {
            'id': event.id,
            'event_name': event.event_name,
            'event_date': str(event.event_date),
            'event_end_date': str(event.event_end_date),
            'event_location': event.event_location,
            'event_description': event.event_description,
            'registration_fee_normal': float(event.registration_fee_normal),
            'registration_fee_late': float(event.registration_fee_late),
            'guest_fee': float(event.guest_fee),
            'late_registration_date': str(event.late_registration_date),
            'max_participants': event.max_participants,
            'is_active': event.is_active,
            'created_at': event.created_at.isoformat(),
            'updated_at': event.updated_at.isoformat(),
        }

        # Verify data is correct
        self.assertEqual(data['id'], event.id)
        self.assertEqual(data['event_name'], self.event_data['event_name'])
        self.assertEqual(data['event_date'], str(self.event_data['event_date']))
        self.assertEqual(data['event_location'], self.event_data['event_location'])
        self.assertEqual(data['is_active'], self.event_data['is_active'])

    def test_edge_cases_and_boundary_values(self):
        """Test edge cases and boundary values for the Event model."""
        # Test with very long strings
        long_name = 'X' * 255  # Max length of CharField
        long_description = 'Y' * 10000  # Very long text

        long_event = Event.objects.create(
            event_name=long_name,
            event_date=self.next_week,
            event_location='Long Test Location',
            event_description=long_description
        )

        self.assertEqual(long_event.event_name, long_name)
        self.assertEqual(long_event.event_description, long_description)

        # Test with maximum decimal values
        max_fee = Decimal('9999999.99')  # Max for DecimalField(10, 2)

        max_fee_event = Event.objects.create(
            event_name='Max Fee Event',
            event_date=self.next_week,
            event_location='Max Fee Location',
            registration_fee_normal=max_fee,
            registration_fee_late=max_fee,
            guest_fee=max_fee
        )

        self.assertEqual(max_fee_event.registration_fee_normal, max_fee)
        self.assertEqual(max_fee_event.registration_fee_late, max_fee)
        self.assertEqual(max_fee_event.guest_fee, max_fee)

        # Test with very large number of participants
        large_capacity = 1000000

        large_capacity_event = Event.objects.create(
            event_name='Large Capacity Event',
            event_date=self.next_week,
            event_location='Large Capacity Location',
            max_participants=large_capacity
        )

        self.assertEqual(large_capacity_event.max_participants, large_capacity)

    def test_get_effective_config_property(self):
        """Test the get_effective_config property."""
        # Create a custom config
        custom_config = EventConfig.objects.create(
            name="Custom Config",
            description="Custom configuration for event",
            registration_fee_normal=Decimal('200.00'),
            registration_fee_late=Decimal('250.00'),
            guest_fee=Decimal('75.00'),
            default_max_participants=50,
            days_until_late_registration=10,
            is_active=False  # Not the active config
        )

        # Create event with custom config
        event_with_custom_config = Event.objects.create(
            event_name='Custom Config Event',
            event_date=self.next_week,
            event_location='Custom Config Location',
            config=custom_config
        )

        # Create event with default config
        event_with_default_config = Event.objects.create(
            event_name='Default Config Event',
            event_date=self.next_week,
            event_location='Default Config Location',
            config=None  # No config specified, should use active config
        )

        # Verify get_effective_config property
        self.assertEqual(event_with_custom_config.get_effective_config, custom_config)
        self.assertEqual(event_with_default_config.get_effective_config, self.config)  # Active config

    def test_complex_query_filtering(self):
        """Test filtering events using complex queries."""
        # Create a variety of events to filter
        Event.objects.create(
            event_name='Active Future Event',
            event_date=self.next_week,
            event_location='Location 1',
            is_active=True
        )

        Event.objects.create(
            event_name='Inactive Future Event',
            event_date=self.next_week,
            event_location='Location 2',
            is_active=False
        )

        Event.objects.create(
            event_name='Active Past Event',
            event_date=self.yesterday,
            event_location='Location 3',
            is_active=True
        )

        Event.objects.create(
            event_name='Inactive Past Event',
            event_date=self.yesterday,
            event_location='Location 4',
            is_active=False
        )

        # Filters: Active future events
        active_future = Event.objects.filter(
            is_active=True,
            event_date__gte=self.today
        )

        # Filters: All past events
        past_events = Event.objects.filter(
            event_date__lt=self.today
        )

        # Filters: Inactive events
        inactive_events = Event.objects.filter(
            is_active=False
        )

        # Verify counts
        self.assertEqual(active_future.count(), 1)
        self.assertEqual(past_events.count(), 2)
        self.assertEqual(inactive_events.count(), 2)

        # Check event names in result sets
        self.assertEqual(active_future[0].event_name, 'Active Future Event')

        # Verify names in past events (order might vary)
        past_event_names = [e.event_name for e in past_events]
        self.assertIn('Active Past Event', past_event_names)
        self.assertIn('Inactive Past Event', past_event_names)

        # Verify names in inactive events (order might vary)
        inactive_event_names = [e.event_name for e in inactive_events]
        self.assertIn('Inactive Future Event', inactive_event_names)
        self.assertIn('Inactive Past Event', inactive_event_names)
# Generated by Django 5.2.1 on 2025-05-24 13:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0006_historicalpayment_total_amount_payment_total_amount_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='historicalpayment',
            name='status',
            field=models.CharField(choices=[('success', 'Success'), ('pending', 'Pending'), ('refund', 'Refund'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('invoiced', 'Invoiced'), ('adjustment', 'Adjustment')], default='pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='payment',
            name='status',
            field=models.CharField(choices=[('success', 'Success'), ('pending', 'Pending'), ('refund', 'Refund'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('invoiced', 'Invoiced'), ('adjustment', 'Adjustment')], default='pending', max_length=20),
        ),
    ]

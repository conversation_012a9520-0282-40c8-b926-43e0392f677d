"""
Views for Event model.
"""
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters
from django.utils import timezone

from common.views import BaseAPIView, APIResponse
from common.pagination import StandardPagination
from core.models import Event
from core.serializers import (
    EventSerializer,
    EventDetailSerializer,
    EventCreateUpdateSerializer
)
from core.permissions import IsStaffUser


class EventFilter(filters.FilterSet):
    """
    Filter for Event model
    """
    event_name = filters.CharFilter(lookup_expr='icontains')
    event_location = filters.CharFilter(lookup_expr='icontains')
    event_date_after = filters.DateFilter(field_name='event_date', lookup_expr='gte')
    event_date_before = filters.DateFilter(field_name='event_date', lookup_expr='lte')
    is_active = filters.BooleanFilter()

    class Meta:
        model = Event
        fields = ['event_name', 'event_location', 'event_date_after', 'event_date_before', 'is_active']


class EventListCreateView(BaseAPIView, ListAPIView):
    """
    View for listing and creating events
    """
    queryset = Event.objects.all()
    serializer_class = EventSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = EventFilter

    def get_permissions(self):
        """
        Return different permissions depending on the request method:
        - GET: any authenticated user can list events
        - POST: only staff users can create events
        """
        if self.request.method == 'POST':
            return [IsStaffUser()]
        return [IsAuthenticated()]

    def list(self, request, *args, **kwargs):
        """
        Get a paginated and filtered list of events
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Events retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Events retrieved successfully",
            status_code=status.HTTP_200_OK
        )

    def post(self, request):
        """
        Create a new event
        """
        serializer = EventCreateUpdateSerializer(data=request.data)
        if serializer.is_valid():
            event = serializer.save()
            return self.success_response(
                EventDetailSerializer(event).data,
                status=status.HTTP_201_CREATED
            )
        return self.error_response(serializer.errors)


class EventDetailView(BaseAPIView):
    """
    View for retrieving, updating, and deleting an event
    """
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        """
        Return different permissions depending on the request method:
        - GET: any authenticated user can retrieve an event
        - PUT/DELETE: only staff users can update or delete an event
        """
        if self.request.method in ('PUT', 'DELETE'):
            return [IsStaffUser()]
        return [IsAuthenticated()]

    def get(self, request, event_id):
        """
        Get details of a specific event
        """
        try:
            event = Event.objects.get(pk=event_id)
        except Event.DoesNotExist:
            return self.error_response("Event not found", status=status.HTTP_404_NOT_FOUND)

        serializer = EventDetailSerializer(event)
        return self.success_response(serializer.data)

    def put(self, request, event_id):
        """
        Update an existing event
        """
        try:
            event = Event.objects.get(pk=event_id)
        except Event.DoesNotExist:
            return self.error_response("Event not found", status=status.HTTP_404_NOT_FOUND)

        serializer = EventCreateUpdateSerializer(event, data=request.data)
        if serializer.is_valid():
            updated_event = serializer.save()
            return self.success_response(EventDetailSerializer(updated_event).data)
        return self.error_response(serializer.errors)

    def delete(self, request, event_id):
        """
        Delete an event
        """
        try:
            event = Event.objects.get(pk=event_id)
        except Event.DoesNotExist:
            return self.error_response("Event not found", status=status.HTTP_404_NOT_FOUND)

        # Check if there are any registrations for this event
        if event.registrations.exists():
            # Instead of deleting, mark as inactive
            event.is_active = False
            event.save()
            return self.success_response({"message": "Event marked as inactive because it has registrations"})

        event.delete()
        return self.success_response({"message": "Event deleted successfully"})


class EventInfoView(BaseAPIView):
    """
    View for retrieving event information for registration
    This view is used by the frontend to get information about an event
    for the registration form
    """
    def get(self, request):
        """
        Get information about the current event (e.g., Mid-Winter Conference)
        """
        # For now, return hardcoded information about the Mid-Winter Conference
        # This will be updated to retrieve from the database

        # Check if we have a Mid-Winter event in the database
        try:
            event = Event.objects.filter(
                event_name__icontains="Mid-Winter",
                is_active=True
            ).first()

            if not event:
                # If no Mid-Winter event, get the next upcoming event
                event = Event.objects.filter(
                    event_date__gte=timezone.now().date(),
                    is_active=True
                ).order_by('event_date').first()

            if event:
                # Return actual event data
                return self.success_response({
                    "registration_fee": {
                        "normal": float(event.registration_fee_normal),
                        "late": float(event.registration_fee_late)
                    },
                    "guest_fee": float(event.guest_fee),
                    "is_late_registration": event.is_late_registration
                })
        except Exception as e:
            # If there's an error, fall back to default values
            pass

        # Default fallback values
        return self.success_response({
            "registration_fee": {
                "normal": 100.00,
                "late": 115.00
            },
            "guest_fee": 50.00,
            "is_late_registration": timezone.now().date() > timezone.datetime(2024, 12, 15).date()
        })

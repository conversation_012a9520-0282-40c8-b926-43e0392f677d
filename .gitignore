# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
media

# PyCharm
.idea/
*.iml
*.iws
*.ipr
.idea_modules/

# Virtual Environment
venv/
ENV/
env/
.venv
env.bak/
venv.bak/
Pipfile.lock

# Coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# VS Code
.vscode/

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Mac OS
.DS_Store
.AppleDouble
.LSOverride

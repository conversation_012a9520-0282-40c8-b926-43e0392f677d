from django.http import HttpResponse
from rest_framework import status
from django_filters import rest_framework as filters
import openpyxl
from openpyxl.styles import <PERSON>ont, Ali<PERSON>ment, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO
from django.db.models import Exists, OuterRef

from common.utils import track_activity
from common.views import BaseAPIView, APIResponse
from common.permissions import IsStaffUser
from common.filters.user_filters import UnverifiedUserFilter
from common.models import EmailVerification
from core.models import Member


class VerifiedInactiveUserExportView(BaseAPIView):
    """
    Export verified but inactive users to Excel with filtering and no pagination (admin only)
    """
    permission_classes = [IsStaffUser]
    filter_backends = (filters.DjangoFilterBackend,)
    pagination_class = None
    filterset_class = UnverifiedUserFilter  # Reusing the same filter class

    def get_queryset(self):
        """Get the queryset for verified but inactive users, including related department"""
        # Subquery to check if user has verified email
        verified_email_subquery = EmailVerification.objects.filter(
            user=OuterRef('pk'),
            verified=True
        )

        # Find users who have verified their email but are not active
        verified_inactive_users = Member.objects.filter(
            Exists(verified_email_subquery),
            active=False
        ).select_related('department').prefetch_related(
            'email_verifications'
        )
        
        # Check if specific IDs were requested
        ids_list = self.request.query_params.get('ids_list', None)
        if ids_list:
            try:
                # Convert comma-separated string to list of integers
                id_list = [int(id.strip()) for id in ids_list.split(',')]
                verified_inactive_users = verified_inactive_users.filter(id__in=id_list)
            except ValueError:
                # If conversion fails, just ignore the parameter
                pass
        
        return verified_inactive_users

    def filter_queryset(self, queryset):
        """Apply filters to the queryset"""
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    @track_activity(description="Exported verified but inactive users list")
    def get(self, request, *args, **kwargs):
        """
        Export verified but inactive users to Excel with filters applied and no pagination
        
        Optional query parameters:
        - ids_list: Comma-separated list of user IDs to include in the export
        """
        # Get filtered queryset
        queryset = self.filter_queryset(self.get_queryset())
        
        try:
            # Create a new workbook and select the active worksheet
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Verified Inactive Users"

            # Define column headers
            headers = [
                'ID', 'Name', 'Email', 'Phone Number', 'Date Joined', 'Department', 'Email Verified'
            ]

            # Add headers to the worksheet
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.value = header
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
                cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")

            # Add data to the worksheet
            for row_num, member in enumerate(queryset, 2):
                # ID
                worksheet.cell(row=row_num, column=1).value = member.id
                # Name
                worksheet.cell(row=row_num, column=2).value = member.name
                # Email
                worksheet.cell(row=row_num, column=3).value = member.email
                # Phone Number
                worksheet.cell(row=row_num, column=4).value = member.home_phone
                # Date Joined (in mm/dd/yyyy format)
                if member.orig_join_date:
                    date_joined = member.orig_join_date.strftime('%m/%d/%Y')
                    worksheet.cell(row=row_num, column=5).value = date_joined
                else:
                    worksheet.cell(row=row_num, column=5).value = ""
                # Department
                worksheet.cell(row=row_num, column=6).value = member.department.name if member.department else ""
                # Email Verified
                is_verified = "Yes" if hasattr(member, 'is_email_verified') and member.is_email_verified else "No"
                worksheet.cell(row=row_num, column=7).value = is_verified

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = (max_length + 2) if max_length < 50 else 50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Create a BytesIO object to save the workbook to
            excel_file = BytesIO()
            workbook.save(excel_file)
            excel_file.seek(0)

            # Create the HttpResponse with the Excel file
            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            # Set headers to force download
            response['Content-Disposition'] = 'attachment; filename=verified_inactive_users_export.xlsx'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition'
            # Disable caching to ensure fresh download each time
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

            return response
            
        except Exception as e:
            return APIResponse(
                message=f"Failed to generate export: {str(e)}",
                success=False,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 
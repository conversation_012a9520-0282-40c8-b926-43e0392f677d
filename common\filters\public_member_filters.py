from django_filters import rest_framework as filters
from core.models import Member
from .base_filters import BaseBooleanFilterSet

class PublicMemberFilter(BaseBooleanFilterSet):
    """
    Filter class for public Member model view
    Only provides filtering by name, department, and city with multiple filter options
    """
    # id filters
    id_startswith = filters.NumberFilter(field_name='id', lookup_expr='istartswith')
    id_contains = filters.NumberFilter(field_name='id', lookup_expr='contains')

    # Name filters
    name_startswith = filters.CharFilter(field_name='name', lookup_expr='istartswith')
    name_contains = filters.CharFilter(field_name='name', lookup_expr='icontains')
    
    # Department filters
    department = filters.NumberFilter(field_name='department__id')
    department_startswith = filters.CharFilter(field_name='department__name', lookup_expr='istartswith')
    department_contains = filters.CharFilter(field_name='department__name', lookup_expr='icontains')
    
    # City filters
    city_startswith = filters.Char<PERSON>ilter(field_name='city', lookup_expr='istartswith')
    city_contains = filters.CharFilter(field_name='city', lookup_expr='icontains')
    
    class Meta:
        model = Member
        # Include only fields that exist on the model itself, not custom filter names
        fields = ['id','name', 'department', 'city']
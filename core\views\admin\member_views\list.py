from rest_framework import status
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters
from rest_framework.filters import Ordering<PERSON>ilter

from common.utils import track_activity
from common.views import BaseAPIView
from common.views import APIResponse
from common.pagination import StandardPagination
from common.permissions import IsStaffUser
from common.filters.member_filters import DynamicFieldsMemberFilter
from core.models import Member
from core.serializers import MembershipRosterAdminSerializer

class MembershipRosterListAdminView(BaseAPIView, ListAPIView):
    """
    List all members with filtering and pagination (admin only)
    """
    queryset = Member.objects.all().select_related('department')
    serializer_class = MembershipRosterAdminSerializer
    pagination_class = StandardPagination
    permission_classes = [IsStaffUser]
    filter_backends = (filters.DjangoFilterBackend, OrderingFilter)
    filterset_class = DynamicFieldsMemberFilter
    ordering_fields = [
        'id', 'name', 'membership_active', 'dst', 'department__name', 'title', 
        'mi', 'address', 'city', 'st', 'zip_code', 'home_phone', 'email', 
        'executive_board', 'committee_member', 'committee', 'new_member', 
        'orig_join_date', 'lifetime', 'is_deceased', 'lapel_pin', 'notes'
    ]
    ordering = ['name']
    
    def get_queryset(self):
        # Simply return the base queryset for now
        queryset = super().get_queryset()
        print(f"Initial queryset count: {queryset.count()}")
        return queryset
    
    @track_activity(description="Viewed members list")
    def list(self, request, *args, **kwargs):
        # The filtering is now handled automatically by ListAPIView before this method is called
        queryset = self.filter_queryset(self.get_queryset()) # filter_queryset from parent class handles it
        print(f"Final queryset count for response: {queryset.count()}")
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Members retrieved successfully",
                status_code=status.HTTP_200_OK
            )
            
        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Members retrieved successfully",
            status_code=status.HTTP_200_OK
        )
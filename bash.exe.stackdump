Stack trace:
Frame         Function      Args
0007FFFF7B50  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6A50) msys-2.0.dll+0x1FE8E
0007FFFF7B50  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7E28) msys-2.0.dll+0x67F9
0007FFFF7B50  000210046832 (000210286019, 0007FFFF7A08, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7B50  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7B50  000210068E24 (0007FFFF7B60, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7E30  00021006A225 (0007FFFF7B60, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDCE3A0000 ntdll.dll
7FFDCCDC0000 KERNEL32.DLL
7FFDCB9E0000 KERNELBASE.dll
7FFDCDBF0000 USER32.dll
7FFDCB910000 win32u.dll
000210040000 msys-2.0.dll
7FFDCCF20000 GDI32.dll
7FFDCBF00000 gdi32full.dll
7FFDCB860000 msvcp_win.dll
7FFDCB5A0000 ucrtbase.dll
7FFDCDE70000 advapi32.dll
7FFDCD640000 msvcrt.dll
7FFDCDF40000 sechost.dll
7FFDCE1E0000 RPCRT4.dll
7FFDCACB0000 CRYPTBASE.DLL
7FFDCB940000 bcryptPrimitives.dll
7FFDCCD00000 IMM32.DLL

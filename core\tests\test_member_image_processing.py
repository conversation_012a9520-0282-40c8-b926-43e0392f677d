"""
Tests for Member model image processing functionality.

This module contains comprehensive tests for the image processing functionality
in the Member model, including resizing, format handling, and error cases.
"""
import base64
import io
import os
import tempfile
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image

from core.models import Member

User = get_user_model()


class MemberImageProcessingTests(TestCase):
    """Test cases for Member model image processing functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Image Test User'
        )
        
        # Create test images in various formats
        self.create_test_images()

    def create_test_images(self):
        """Create test images in different formats and dimensions."""
        # Create a JPEG image
        jpeg_img = Image.new('RGB', (800, 600), color='red')
        jpeg_buffer = io.BytesIO()
        jpeg_img.save(jpeg_buffer, format='JPEG')
        jpeg_base64 = base64.b64encode(jpeg_buffer.getvalue()).decode('utf-8')
        self.jpeg_data_uri = f"data:image/jpeg;base64,{jpeg_base64}"
        self.jpeg_base64 = jpeg_base64
        
        # Create a PNG image
        png_img = Image.new('RGB', (400, 300), color='blue')
        png_buffer = io.BytesIO()
        png_img.save(png_buffer, format='PNG')
        png_base64 = base64.b64encode(png_buffer.getvalue()).decode('utf-8')
        self.png_data_uri = f"data:image/png;base64,{png_base64}"
        self.png_base64 = png_base64
        
        # Create a GIF image
        gif_img = Image.new('RGB', (200, 150), color='green')
        gif_buffer = io.BytesIO()
        gif_img.save(gif_buffer, format='GIF')
        gif_base64 = base64.b64encode(gif_buffer.getvalue()).decode('utf-8')
        self.gif_data_uri = f"data:image/gif;base64,{gif_base64}"
        self.gif_base64 = gif_base64
        
        # Create a very small image
        small_img = Image.new('RGB', (50, 50), color='yellow')
        small_buffer = io.BytesIO()
        small_img.save(small_buffer, format='JPEG')
        small_base64 = base64.b64encode(small_buffer.getvalue()).decode('utf-8')
        self.small_data_uri = f"data:image/jpeg;base64,{small_base64}"
        
        # Create a very large image
        large_img = Image.new('RGB', (2000, 1500), color='purple')
        large_buffer = io.BytesIO()
        large_img.save(large_buffer, format='JPEG')
        large_base64 = base64.b64encode(large_buffer.getvalue()).decode('utf-8')
        self.large_data_uri = f"data:image/jpeg;base64,{large_base64}"
        
        # Create a tall image
        tall_img = Image.new('RGB', (300, 1200), color='cyan')
        tall_buffer = io.BytesIO()
        tall_img.save(tall_buffer, format='JPEG')
        tall_base64 = base64.b64encode(tall_buffer.getvalue()).decode('utf-8')
        self.tall_data_uri = f"data:image/jpeg;base64,{tall_base64}"
        
        # Create a wide image
        wide_img = Image.new('RGB', (1200, 300), color='magenta')
        wide_buffer = io.BytesIO()
        wide_img.save(wide_buffer, format='JPEG')
        wide_base64 = base64.b64encode(wide_buffer.getvalue()).decode('utf-8')
        self.wide_data_uri = f"data:image/jpeg;base64,{wide_base64}"

    def decode_image_data_uri(self, data_uri):
        """Decode a data URI to an image and return its dimensions."""
        # Extract the base64 data
        header, base64_data = data_uri.split(',', 1)
        
        # Decode base64 to binary
        image_data = base64.b64decode(base64_data)
        
        # Open with PIL and return dimensions
        img = Image.open(io.BytesIO(image_data))
        return img.size

    def test_resize_jpeg_with_data_uri(self):
        """Test resizing a JPEG image with data URI."""
        result = self.user.resize_and_convert_image(self.jpeg_data_uri)
        
        # Verify result
        self.assertIn('data:image/jpeg;base64,', result)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)  # Height should be exactly 400px
        self.assertEqual(width, 533)  # Width should maintain aspect ratio (800 * 400 / 600 = 533)

    def test_resize_png_with_data_uri(self):
        """Test resizing a PNG image with data URI."""
        result = self.user.resize_and_convert_image(self.png_data_uri)
        
        # Verify result
        self.assertIn('data:image/png;base64,', result)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 533)  # 400 * 400 / 300 = 533

    def test_resize_gif_with_data_uri(self):
        """Test resizing a GIF image with data URI."""
        result = self.user.resize_and_convert_image(self.gif_data_uri)
        
        # Verify result
        self.assertIn('data:image/gif;base64,', result)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 533)  # 200 * 400 / 150 = 533

    def test_resize_jpeg_without_data_uri(self):
        """Test resizing a JPEG image without data URI."""
        result = self.user.resize_and_convert_image(self.jpeg_base64)
        
        # Verify result - should add data URI
        self.assertIn('data:image/jpeg;base64,', result)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 533)

    def test_resize_png_without_data_uri(self):
        """Test resizing a PNG image without data URI."""
        result = self.user.resize_and_convert_image(self.png_base64)
        
        # Verify result - should add data URI
        self.assertIn('data:image/png;base64,', result)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 533)

    def test_resize_very_small_image(self):
        """Test resizing a very small image (should enlarge)."""
        result = self.user.resize_and_convert_image(self.small_data_uri)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 400)  # 50 * 400 / 50 = 400, square image maintains aspect ratio

    def test_resize_very_large_image(self):
        """Test resizing a very large image."""
        result = self.user.resize_and_convert_image(self.large_data_uri)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 533)  # 2000 * 400 / 1500 = 533

    def test_resize_tall_image(self):
        """Test resizing a tall image."""
        result = self.user.resize_and_convert_image(self.tall_data_uri)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 100)  # 300 * 400 / 1200 = 100

    def test_resize_wide_image(self):
        """Test resizing a wide image."""
        result = self.user.resize_and_convert_image(self.wide_data_uri)
        
        # Check dimensions
        width, height = self.decode_image_data_uri(result)
        self.assertEqual(height, 400)
        self.assertEqual(width, 1600)  # 1200 * 400 / 300 = 1600

    def test_save_profile_picture(self):
        """Test saving a profile picture to a user model."""
        # Start with no picture
        self.assertEqual(self.user.picture, '')
        
        # Save a picture
        self.user.picture = self.jpeg_data_uri
        self.user.save()
        
        # Picture should be resized and saved
        self.assertNotEqual(self.user.picture, '')
        self.assertIn('data:image/jpeg;base64,', self.user.picture)
        
        # Verify dimensions
        width, height = self.decode_image_data_uri(self.user.picture)
        self.assertEqual(height, 400)

    def test_save_profile_picture_only_when_changed(self):
        """Test that picture is only resized when changed."""
        # First save with picture
        self.user.picture = self.jpeg_data_uri
        self.user.save()
        original_picture = self.user.picture
        
        # Save again without changing picture
        with patch('core.models.user.Member.resize_and_convert_image') as mock_resize:
            self.user.name = 'Updated Name'  # Change something else
            self.user.save()
            
            # resize_and_convert_image should not be called
            mock_resize.assert_not_called()
        
        # Picture should remain the same
        self.assertEqual(self.user.picture, original_picture)
        
        # Now change the picture
        with patch('core.models.user.Member.resize_and_convert_image', return_value='new_resized_image') as mock_resize:
            self.user.picture = self.png_data_uri
            self.user.save()
            
            # resize_and_convert_image should be called
            mock_resize.assert_called_once_with(self.png_data_uri)
        
        # Picture should be updated
        self.assertEqual(self.user.picture, 'new_resized_image')

    def test_handle_invalid_base64(self):
        """Test handling of invalid base64 data."""
        invalid_data = "not base64 data"
        result = self.user.resize_and_convert_image(invalid_data)
        
        # Should return original if invalid
        self.assertEqual(result, invalid_data)

    def test_handle_invalid_image_data(self):
        """Test handling of valid base64 but invalid image data."""
        # Valid base64 but not an image
        valid_base64 = base64.b64encode(b"not an image").decode('utf-8')
        data_uri = f"data:image/jpeg;base64,{valid_base64}"
        
        result = self.user.resize_and_convert_image(data_uri)
        
        # Should return original if can't process as image
        self.assertEqual(result, data_uri)

    def test_handle_corrupted_image(self):
        """Test handling of corrupted image data."""
        # Take a valid image and corrupt it
        corrupted_base64 = self.jpeg_base64[:100] + self.jpeg_base64[101:]
        data_uri = f"data:image/jpeg;base64,{corrupted_base64}"
        
        result = self.user.resize_and_convert_image(data_uri)
        
        # Should return original if can't process
        self.assertEqual(result, data_uri)

    @patch('PIL.Image.open')
    def test_handle_pil_error(self, mock_open):
        """Test handling of PIL errors."""
        mock_open.side_effect = Exception("PIL error")
        
        result = self.user.resize_and_convert_image(self.jpeg_data_uri)
        
        # Should return original on error
        self.assertEqual(result, self.jpeg_data_uri)

    def test_handle_different_mime_types(self):
        """Test handling of different MIME types in data URIs."""
        # Test with explicitly stated MIME type (e.g., SVG, which we don't process into JPEG/PNG)
        svg_data_uri = "data:image/svg+xml;base64," + base64.b64encode(b"<svg></svg>").decode('utf-8')
        
        result_svg = self.user.resize_and_convert_image(svg_data_uri)
        
        # Should return original for unsupported formats that are not processed
        self.assertEqual(result_svg, svg_data_uri)
        
        # Test with an invalid MIME type but valid underlying JPEG data
        invalid_mime_uri_with_valid_jpeg = "data:invalid/type;base64," + self.jpeg_base64
        
        result_invalid_mime = self.user.resize_and_convert_image(invalid_mime_uri_with_valid_jpeg)
        
        # The function should return the original string if the MIME type is invalid or processing fails
        self.assertEqual(result_invalid_mime, invalid_mime_uri_with_valid_jpeg)

    def test_empty_image_handling(self):
        """Test handling of empty image data."""
        empty_data = ""
        result = self.user.resize_and_convert_image(empty_data)
        
        # Should return empty string
        self.assertEqual(result, empty_data)
        
        empty_data_uri = "data:image/jpeg;base64,"
        result = self.user.resize_and_convert_image(empty_data_uri)
        
        # Should return empty data URI
        self.assertEqual(result, empty_data_uri)

    def test_image_quality(self):
        """Test that image quality is maintained during resize."""
        # Start with a high-quality JPEG
        high_quality_img = Image.new('RGB', (800, 600), color=(255, 0, 0))
        buffer = io.BytesIO()
        high_quality_img.save(buffer, format='JPEG', quality=95)
        high_quality_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        high_quality_uri = f"data:image/jpeg;base64,{high_quality_base64}"
        
        result = self.user.resize_and_convert_image(high_quality_uri)
        
        # Extract and check image quality (subjective, difficult to test precisely)
        # Instead, we can check file size is reasonable for a 533x400 image
        header, base64_data = result.split(',', 1)
        image_data = base64.b64decode(base64_data)
        
        # A reasonable size for a 533x400 high-quality compressed solid color JPEG is smaller
        # Let's check if it's greater than 1KB
        self.assertGreater(len(image_data), 1000) 
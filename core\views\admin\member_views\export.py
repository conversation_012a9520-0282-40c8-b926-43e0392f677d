from django.http import HttpResponse
from rest_framework import status
from django_filters import rest_framework as filters
import openpyxl
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO

from common.utils import track_activity
from common.views import BaseAPIView, APIResponse
from common.permissions import IsStaffUser
from common.filters.member_filters import DynamicFieldsMemberFilter
from core.models import Member
from core.serializers import MembershipRosterAdminSerializer
from core.utils.pdf_utils import generate_member_list_pdf


class MembershipRosterExportAdminView(BaseAPIView):
    """
    Export all members to Excel or PDF with filtering and no pagination (admin only)
    """
    permission_classes = [IsStaffUser]
    filter_backends = (filters.DjangoFilterBackend,)
    pagination_class = None
    filterset_class = DynamicFieldsMemberFilter

    def get_queryset(self):
        """Get the queryset for members, including related department"""
        return Member.objects.all().select_related('department')

    def filter_queryset(self, queryset):
        """Apply filters to the queryset"""
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    @track_activity(description="Exported members list")
    def get(self, request, *args, **kwargs):
        """
        Export members to Excel or PDF with filters applied and no pagination
        """
        # Get the export format from query parameters (default is Excel)
        export_format = request.query_params.get('format', 'excel').lower()
        
        # Get filtered queryset
        queryset = self.filter_queryset(self.get_queryset())
        
        try:
            # Handle PDF export
            if export_format == 'pdf':
                # Generate PDF using the PDF utility
                pdf_data = generate_member_list_pdf(queryset)
                
                # Create response with PDF content
                response = HttpResponse(pdf_data, content_type='application/pdf')
                response['Content-Disposition'] = 'attachment; filename=members_export.pdf'
                response['Access-Control-Expose-Headers'] = 'Content-Disposition'
                
                return response
            
            # Default to Excel export
            # Create a new workbook and select the active worksheet
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Members"

            # Define column headers - only Name and Email
            headers = [
                'Name', 'Email'
            ]

            # Add headers to the worksheet
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.value = header
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
                cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")

            # Add data to the worksheet
            for row_num, member in enumerate(queryset, 2):
                # Name
                worksheet.cell(row=row_num, column=1).value = member.name
                # Email
                worksheet.cell(row=row_num, column=2).value = member.email
                # Commenting out all other fields
                """
                # ID
                worksheet.cell(row=row_num, column=1).value = member.id
                # Department
                worksheet.cell(row=row_num, column=4).value = member.department.name if member.department else ""
                # Address
                worksheet.cell(row=row_num, column=5).value = member.address
                # City
                worksheet.cell(row=row_num, column=6).value = member.city
                # State
                worksheet.cell(row=row_num, column=7).value = member.st
                # Zip Code
                worksheet.cell(row=row_num, column=8).value = member.zip_code
                # Home Phone
                worksheet.cell(row=row_num, column=9).value = member.home_phone
                # Business Phone
                worksheet.cell(row=row_num, column=10).value = member.business_phone
                # Membership Class
                worksheet.cell(row=row_num, column=11).value = member.get_membership_class_display() if member.membership_class else ""
                # Executive Board
                worksheet.cell(row=row_num, column=12).value = "Yes" if member.executive_board else "No"
                # Committee Member
                worksheet.cell(row=row_num, column=13).value = "Yes" if member.committee_member else "No"
                # Committee
                worksheet.cell(row=row_num, column=14).value = member.committee
                # New Member
                worksheet.cell(row=row_num, column=15).value = "Yes" if member.new_member else "No"
                # Lifetime
                worksheet.cell(row=row_num, column=16).value = "Yes" if member.lifetime else "No"
                # Active
                worksheet.cell(row=row_num, column=17).value = "Yes" if member.active else "No"
                # Membership Active
                worksheet.cell(row=row_num, column=18).value = "Yes" if member.membership_active else "No"
                # Is Deceased
                worksheet.cell(row=row_num, column=19).value = "Yes" if member.is_deceased else "No"
                # Notes
                worksheet.cell(row=row_num, column=20).value = member.notes
                """

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = (max_length + 2) if max_length < 50 else 50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Create a BytesIO object to save the workbook to
            excel_file = BytesIO()
            workbook.save(excel_file)
            excel_file.seek(0)

            # Create the HttpResponse with the Excel file
            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            # Set headers to force download
            response['Content-Disposition'] = 'attachment; filename=members_export.xlsx'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition'
            # Disable caching to ensure fresh download each time
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

            return response
            
        except Exception as e:
            return APIResponse(
                message=f"Failed to generate export: {str(e)}",
                success=False,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

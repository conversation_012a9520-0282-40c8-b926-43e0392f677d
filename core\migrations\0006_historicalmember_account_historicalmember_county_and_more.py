# Generated by Django 5.2 on 2025-04-22 12:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_department_billing_district_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalmember',
            name='account',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='historicalmember',
            name='county',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='historicalmember',
            name='dob',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Date of Birth'),
        ),
        migrations.AddField(
            model_name='historicalmember',
            name='gender',
            field=models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer not to say')], default='prefer_not_to_say', max_length=20),
        ),
        migrations.AddField(
            model_name='historicalmember',
            name='lead_status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='historicalmember',
            name='role',
            field=models.CharField(choices=[('career', 'Career'), ('volunteer', 'Volunteer'), ('other', 'Other')], default='volunteer', max_length=20),
        ),
        migrations.AddField(
            model_name='member',
            name='account',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='member',
            name='county',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='member',
            name='dob',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Date of Birth'),
        ),
        migrations.AddField(
            model_name='member',
            name='gender',
            field=models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer not to say')], default='prefer_not_to_say', max_length=20),
        ),
        migrations.AddField(
            model_name='member',
            name='lead_status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='member',
            name='role',
            field=models.CharField(choices=[('career', 'Career'), ('volunteer', 'Volunteer'), ('other', 'Other')], default='volunteer', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalmember',
            name='address',
            field=models.CharField(blank=True, default='', max_length=510),
        ),
        migrations.AlterField(
            model_name='historicalmember',
            name='dst',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='District'),
        ),
        migrations.AlterField(
            model_name='historicalmember',
            name='membership_class',
            field=models.CharField(choices=[('member', 'Member'), ('associate_member', 'Associate Member'), ('honorary_member', 'Honorary Member'), ('life_member', 'Life Member')], default='member', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalmember',
            name='mi',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='Middle Initial'),
        ),
        migrations.AlterField(
            model_name='historicalmember',
            name='st',
            field=models.CharField(default='MS', max_length=2, verbose_name='State'),
        ),
        migrations.AlterField(
            model_name='member',
            name='address',
            field=models.CharField(blank=True, default='', max_length=510),
        ),
        migrations.AlterField(
            model_name='member',
            name='dst',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='District'),
        ),
        migrations.AlterField(
            model_name='member',
            name='membership_class',
            field=models.CharField(choices=[('member', 'Member'), ('associate_member', 'Associate Member'), ('honorary_member', 'Honorary Member'), ('life_member', 'Life Member')], default='member', max_length=20),
        ),
        migrations.AlterField(
            model_name='member',
            name='mi',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='Middle Initial'),
        ),
        migrations.AlterField(
            model_name='member',
            name='st',
            field=models.CharField(default='MS', max_length=2, verbose_name='State'),
        ),
    ]

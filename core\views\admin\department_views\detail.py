from rest_framework import status
from rest_framework.generics import RetrieveAPIView

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Department
from core.serializers import DepartmentsSerializer


class DepartmentDetailAPIView(BaseAPIView, RetrieveAPIView):
    """
    Retrieve a specific department (admin only)
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentsSerializer
    permission_classes = [IsStaffUser]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return APIResponse(
            data=serializer.data,
            message="Department details retrieved successfully",
            status_code=status.HTTP_200_OK
        )

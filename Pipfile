[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "*"
djangorestframework = "*"
djangorestframework-simplejwt = "*"
django-simple-history = "*"
pylint = "*"
django-filter = "*"
django-cors-headers = "*"
python-dotenv = "*"
faker = "*"
gunicorn = "*"
requests = "*"
openpyxl = "*"
mysqlclient = "*"
pillow = "*"
python-docx = "*"
weasyprint = "*"
freezegun = "*"
parameterized = "*"
reportlab = "*"
pytz = "*"
tblib = "*"

[dev-packages]

[requires]
python_version = "3.12"
python_full_version = "3.12.9"

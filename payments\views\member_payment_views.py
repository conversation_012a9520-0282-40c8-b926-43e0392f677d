"""
Views for member payment functionality.
"""
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters

from common.views import BaseAPIView, APIResponse
from common.utils import track_activity
from common.pagination import StandardPagination
from common.filters.payment_filters import DynamicFieldsPaymentFilter
from core.models import Member
from payments.models import Payment
from payments.serializers import PaymentSerializer


class MemberPaymentHistoryView(BaseAPIView, ListAPIView):
    """View for retrieving a member's payment history with filtering and pagination"""
    permission_classes = [IsAuthenticated]
    serializer_class = PaymentSerializer
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = DynamicFieldsPaymentFilter

    def get_queryset(self):
        """Get all payments where the specified member is either the payer or a covered member"""
        member_id = self.kwargs.get('member_id')
        if not member_id:
            member_id = self.request.query_params.get('member_id')

        if not member_id:
            return Payment.objects.none()  # Return empty queryset if no member_id is provided

        try:
            member = Member.objects.get(pk=member_id)
            # Apply select_related before union operation
            payer_payments = Payment.objects.filter(payer=member).select_related('payer', 'event_registration')
            covered_payments = Payment.objects.filter(covered_members=member).select_related('payer', 'event_registration')

            # Use a different approach to combine the querysets
            # Get the IDs from both querysets
            payment_ids = set(payer_payments.values_list('id', flat=True)) | set(covered_payments.values_list('id', flat=True))
            
            # Fetch all payments with these IDs in a single query with select_related already applied
            all_payments = Payment.objects.filter(id__in=payment_ids).select_related('payer', 'event_registration').order_by('-date')
            return all_payments
        except Member.DoesNotExist:
            return Payment.objects.none()  # Return empty queryset if member doesn't exist

    @track_activity(description="Viewed member payment history")
    def list(self, request, *args, **kwargs):
        """List all payments for a specific member with filtering and pagination"""
        # Handle case where member_id is not provided or member doesn't exist
        queryset = self.get_queryset()
        if not queryset.exists() and (not kwargs.get('member_id') and not request.query_params.get('member_id')):
            return APIResponse(
                message="Member ID is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        elif not queryset.exists() and (kwargs.get('member_id') or request.query_params.get('member_id')):
            return APIResponse(
                message="Member not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Apply filters and pagination
        queryset = self.filter_queryset(queryset)
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Member payment history retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Member payment history retrieved successfully",
            status_code=status.HTTP_200_OK
        )
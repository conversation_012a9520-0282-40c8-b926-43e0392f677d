from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from core.models import Member, Department
from common.pagination import StandardPagination

class StandardPaginationTests(TestCase):
    """Test cases for StandardPagination"""

    def setUp(self):
        """Set up test data"""
        # Import UserActivity here to ensure it's available for cleanup
        from common.models import UserActivity
        
        self.client = APIClient()
        self.department = Department.objects.create(name="Test Department")
        
        # Create 25 test members (more than default page size of 20)
        self.members = []
        for i in range(25):
            member = Member.objects.create(
                name=f"Test Member {i}",
                email=f"test{i}@example.com",
                department=self.department,
                membership_class=Member.MembershipStatus.MEMBER,
                lead_status=Member.LeadStatus.ACTIVE,
                role=Member.Role.VOLUNTEER,
                gender=Member.Gender.PREFER_NOT_TO_SAY,
                is_staff=True,
                is_active=True
            )
            self.members.append(member)
        
        # Create a staff user for authentication
        self.staff_user = Member.objects.create(
            email="<EMAIL>",
            name="Staff User",
            is_staff=True,
            is_active=True,
            membership_class=Member.MembershipStatus.MEMBER,
            lead_status=Member.LeadStatus.ACTIVE,
            role=Member.Role.CAREER,
            gender=Member.Gender.PREFER_NOT_TO_SAY
        )
        self.client.force_authenticate(user=self.staff_user)
        
    def tearDown(self):
        """Clean up after tests"""
        # Clean up UserActivity records first to avoid foreign key constraint errors
        from common.models import UserActivity
        UserActivity.objects.all().delete()

    def test_default_pagination(self):
        """Test default pagination settings"""
        response = self.client.get(reverse('core:admin-member-list'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response structure
        self.assertIn('message', response.data)
        self.assertIn('success', response.data)
        self.assertIn('data', response.data)
        
        # Check pagination structure inside data
        self.assertIn('count', response.data['data'])
        self.assertIn('next', response.data['data'])
        self.assertIn('previous', response.data['data'])
        self.assertIn('results', response.data['data'])
        
        # Check default page size
        self.assertEqual(len(response.data['data']['results']), StandardPagination.page_size)
        self.assertEqual(response.data['data']['count'], 26)  # 25 test members + 1 staff user
        
        # Check next page exists (since we have more than page_size items)
        self.assertIsNotNone(response.data['data']['next'])
        self.assertIsNone(response.data['data']['previous'])

    def test_custom_page_size(self):
        """Test custom page size parameter"""
        # Test with smaller page size
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 10)
        
        # Test with larger page size (but within max_page_size)
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': 50})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # The API seems to return all members (26) instead of limiting to max_page_size
        self.assertEqual(len(response.data['data']['results']), 26)
        
        # Test with page size larger than max_page_size
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': 200})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # The API seems to return all members (26) instead of limiting to max_page_size
        self.assertEqual(len(response.data['data']['results']), 26)

    def test_page_navigation(self):
        """Test navigation between pages"""
        # Get first page
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 10)
        next_page = response.data['data']['next']
        
        # Get second page
        response = self.client.get(next_page)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 10)
        self.assertIsNotNone(response.data['data']['previous'])
        next_page = response.data['data']['next']
        
        # Get last page
        response = self.client.get(next_page)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), 6)  # Remaining items
        self.assertIsNotNone(response.data['data']['previous'])
        self.assertIsNone(response.data['data']['next'])

    def test_invalid_page(self):
        """Test invalid page numbers"""
        # Test non-existent page
        response = self.client.get(reverse('core:admin-member-list'), {'page': 999})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Test invalid page number format
        response = self.client.get(reverse('core:admin-member-list'), {'page': 'invalid'})
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_invalid_page_size(self):
        """Test invalid page size parameters"""
        # Test negative page size
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': -1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), StandardPagination.page_size)
        
        # Test zero page size
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': 0})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), StandardPagination.page_size)
        
        # Test invalid page size format
        response = self.client.get(reverse('core:admin-member-list'), {'page_size': 'invalid'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']['results']), StandardPagination.page_size)

    def test_empty_results(self):
        """Test pagination with no results"""
        # Instead of deleting all members, create a new test client with a filter that returns no results
        response = self.client.get(reverse('core:admin-member-list'), {'name': 'NonExistentMember'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['count'], 0)
        self.assertEqual(len(response.data['data']['results']), 0)
        self.assertIsNone(response.data['data']['next'])
        self.assertIsNone(response.data['data']['previous'])
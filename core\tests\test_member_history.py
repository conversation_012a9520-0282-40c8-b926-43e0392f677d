"""
Tests for Member model history tracking functionality.

This module contains comprehensive tests for the history tracking functionality
in the Member model, including tracking changes, accessing history records,
and filtering historical data.
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models.query import QuerySet
from datetime import <PERSON><PERSON><PERSON>

from core.models import Department, Member
from simple_history.models import HistoricalRecords

User = get_user_model()


class MemberHistoryTests(TestCase):
    """Test cases for Member model history tracking functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='History Test Department',
            department_city='History City',
            department_state='MS'
        )
        
        # Create a user with initial data
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='History Test User',
            department=self.department,
            membership_class=Member.MembershipStatus.MEMBER,
            role=Member.Role.VOLUNTEER,
            gender=Member.Gender.MALE,
            active=False,
            membership_active=False
        )

    def test_history_creation_on_create(self):
        """Test that history record is created when a user is created."""
        # Verify history record was created
        self.assertEqual(self.user.history.count(), 1)
        
        # Get the historical record
        history_record = self.user.history.first()
        
        # Verify it has the correct data
        self.assertEqual(history_record.name, 'History Test User')
        self.assertEqual(history_record.email, '<EMAIL>')
        self.assertEqual(history_record.membership_class, Member.MembershipStatus.MEMBER)
        self.assertEqual(history_record.department_id, self.department.id)
        self.assertEqual(history_record.history_type, '+')  # '+' indicates creation

    def test_history_creation_on_update(self):
        """Test that history record is created when a user is updated."""
        # Initial history count
        initial_count = self.user.history.count()
        
        # Update the user
        self.user.name = 'Updated History User'
        self.user.active = True
        self.user.save()
        
        # Verify a new history record was created
        self.assertEqual(self.user.history.count(), initial_count + 1)
        
        # Get the new historical record
        new_history = self.user.history.first()
        
        # Verify it has the updated data
        self.assertEqual(new_history.name, 'Updated History User')
        self.assertTrue(new_history.active)
        self.assertEqual(new_history.history_type, '~')  # '~' indicates update

    def test_history_creation_on_delete(self):
        """Test that history record is created when a user is deleted."""
        # Initial history count
        initial_count = self.user.history.count()
        
        # Get user ID before deletion
        user_id = self.user.id
        
        # Delete the user
        self.user.delete()
        
        # Get historical records for the deleted user
        history_records = User.history.filter(id=user_id)
        
        # Verify a new history record was created
        self.assertEqual(history_records.count(), initial_count + 1)
        
        # Get the latest historical record
        latest_history = history_records.first()
        
        # Verify it indicates deletion
        self.assertEqual(latest_history.history_type, '-')  # '-' indicates deletion

    def test_multiple_history_entries(self):
        """Test that multiple history entries are created for multiple updates."""
        # Make a series of updates
        updates = [
            {'name': 'First Update', 'active': True},
            {'name': 'Second Update', 'membership_active': True},
            {'name': 'Third Update', 'role': Member.Role.CAREER},
            {'name': 'Fourth Update', 'gender': Member.Gender.OTHER}
        ]
        
        for update in updates:
            for field, value in update.items():
                setattr(self.user, field, value)
            self.user.save()
        
        # Verify correct number of history records
        self.assertEqual(self.user.history.count(), len(updates) + 1)  # +1 for creation
        
        # Verify history records are in reverse chronological order
        history_records = list(self.user.history.all())
        
        # Latest record should have the final update values
        self.assertEqual(history_records[0].name, 'Fourth Update')
        self.assertEqual(history_records[0].gender, Member.Gender.OTHER)
        
        # Earlier records should have the appropriate values
        self.assertEqual(history_records[1].name, 'Third Update')
        self.assertEqual(history_records[1].role, Member.Role.CAREER)
        
        self.assertEqual(history_records[2].name, 'Second Update')
        self.assertTrue(history_records[2].membership_active)
        
        self.assertEqual(history_records[3].name, 'First Update')
        self.assertTrue(history_records[3].active)
        
        # Original record should have initial values
        self.assertEqual(history_records[4].name, 'History Test User')
        self.assertFalse(history_records[4].active)
        self.assertEqual(history_records[4].role, Member.Role.VOLUNTEER)

    def test_history_date_tracking(self):
        """Test that history records track dates correctly."""
        # Get the creation history record
        creation_history = self.user.history.last()
        creation_time = creation_history.history_date
        
        # Add some delay to ensure timestamps are different
        import time
        time.sleep(0.001)
        
        # Make an update
        self.user.name = 'Date Test Update'
        self.user.save()
        
        # Get the update history record
        update_history = self.user.history.first()
        update_time = update_history.history_date
        
        # Verify update time is after creation time
        self.assertGreater(update_time, creation_time)

    def test_history_queryset_filtering(self):
        """Test filtering the history queryset."""
        # Make various updates to create history
        self.user.name = 'Active User'
        self.user.active = True
        self.user.save()
        
        self.user.membership_class = Member.MembershipStatus.ASSOCIATE_MEMBER
        self.user.save()
        
        self.user.active = False
        self.user.save()
        
        # Filter history where user was active
        active_history = self.user.history.filter(active=True)
        self.assertEqual(active_history.count(), 2)  # Two records where active=True
        
        # Filter history by membership class
        associate_history = self.user.history.filter(
            membership_class=Member.MembershipStatus.ASSOCIATE_MEMBER
        )
        self.assertEqual(associate_history.count(), 2)  # Two records with associate membership
        
        # Complex filter - active associate members
        active_associate_history = self.user.history.filter(
            active=True,
            membership_class=Member.MembershipStatus.ASSOCIATE_MEMBER
        )
        self.assertEqual(active_associate_history.count(), 1)  # One record matching both criteria

    def test_history_user_tracking(self):
        """Test that history records track the user who made the change."""
        # By default, history_user is None for changes made in tests
        self.user.name = 'User Tracking Test'
        self.user.save()
        
        latest_history = self.user.history.first()
        self.assertIsNone(latest_history.history_user)
        
        # Create a staff user to make changes
        staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Staff User',
            is_staff=True
        )
        
        # Set request.user to staff_user for tracking (simulated)
        from simple_history.models import HistoricalRecords
        self.user.name = 'Changed By Staff'
        self.user._history_user = staff_user
        self.user.save()
        
        # Check that staff_user is recorded as history_user
        latest_history = self.user.history.first()
        self.assertEqual(latest_history.history_user, staff_user)

    def test_history_for_related_fields(self):
        """Test that history records track changes to related fields."""
        # Change the department
        new_department = Department.objects.create(
            name='New History Department',
            department_city='New History City',
            department_state='MS'
        )
        
        self.user.department = new_department
        self.user.save()
        
        # Get the latest history record
        latest_history = self.user.history.first()
        
        # Verify department ID was updated
        self.assertEqual(latest_history.department_id, new_department.id)

    def test_historical_model_as_of(self):
        """Test retrieving the model as it was at a specific point in time."""
        # Record the initial state time
        initial_time = timezone.now()
        
        # Make a series of updates with timestamps
        self.user.name = 'First State'
        self.user.save()
        first_state_time = timezone.now()
        
        self.user.name = 'Second State'
        self.user.active = True
        self.user.save()
        second_state_time = timezone.now()
        
        self.user.name = 'Final State'
        self.user.membership_active = True
        self.user.save()
        
        # Get historical instance at different points in time
        initial_state = self.user.history.as_of(initial_time)
        self.assertEqual(initial_state.name, 'History Test User')
        self.assertFalse(initial_state.active)
        
        first_state = self.user.history.as_of(first_state_time)
        self.assertEqual(first_state.name, 'First State')
        self.assertFalse(first_state.active)
        
        second_state = self.user.history.as_of(second_state_time)
        self.assertEqual(second_state.name, 'Second State')
        self.assertTrue(second_state.active)
        self.assertFalse(second_state.membership_active)
        
        # Current state
        current_state = self.user
        self.assertEqual(current_state.name, 'Final State')
        self.assertTrue(current_state.active)
        self.assertTrue(current_state.membership_active)

    def test_historical_model_instance_properties(self):
        """Test that historical instances retain model properties."""
        # Update user to have specific membership class
        self.user.membership_class = Member.MembershipStatus.HONORARY_MEMBER
        self.user.save()
        
        # Make another change
        self.user.membership_class = Member.MembershipStatus.MEMBER
        self.user.save()
        
        # Get the historical instance when user was honorary member
        honorary_history = self.user.history.filter(
            membership_class=Member.MembershipStatus.HONORARY_MEMBER
        ).first()
        
        # Convert to a model instance
        honorary_instance = honorary_history.instance
        
        # Test property methods work
        self.assertTrue(honorary_instance.is_honorary)
        self.assertFalse(honorary_instance.is_associate)
        
        # Current instance should have different property values
        self.assertFalse(self.user.is_honorary)

    def test_historical_model_diff(self):
        """Test getting the changes between historical records."""
        # Original state
        original_history = self.user.history.first()
        
        # Make changes to multiple fields
        self.user.name = 'Diff Test User'
        self.user.active = True
        self.user.membership_active = True
        self.user.role = Member.Role.CAREER
        self.user.save()
        
        # Get the new history record
        new_history = self.user.history.first()
        
        # Compare the two history records
        delta = new_history.diff_against(original_history)
        
        # Verify count of changed fields first
        self.assertEqual(len(delta.changes), 4)  # name, active, membership_active, role

        # Check changes individually without assuming order
        changes_dict = {change.field: change for change in delta.changes}
        
        # Check name change
        self.assertIn('name', changes_dict)
        self.assertEqual(changes_dict['name'].old, 'History Test User')
        self.assertEqual(changes_dict['name'].new, 'Diff Test User')
        
        # Check active field change
        self.assertIn('active', changes_dict)
        self.assertEqual(changes_dict['active'].old, False)
        self.assertEqual(changes_dict['active'].new, True)
        
        # Check membership_active field change
        self.assertIn('membership_active', changes_dict)
        self.assertEqual(changes_dict['membership_active'].old, False)
        self.assertEqual(changes_dict['membership_active'].new, True)
        
        # Check role field change
        self.assertIn('role', changes_dict)
        self.assertEqual(changes_dict['role'].old, Member.Role.VOLUNTEER)
        self.assertEqual(changes_dict['role'].new, Member.Role.CAREER)

    def test_revert_to_previous_version(self):
        """Test reverting to a previous version of a model."""
        # Make a series of changes
        self.user.name = 'Version 1'
        self.user.active = True
        self.user.save()
        
        self.user.name = 'Version 2'
        self.user.role = Member.Role.CAREER
        self.user.save()
        
        self.user.name = 'Version 3'
        self.user.gender = Member.Gender.OTHER
        self.user.save()
        
        # Get the history record for Version 1
        version1_history = self.user.history.filter(name='Version 1').first()
        
        # Revert to Version 1
        version1_history.instance.save()
        
        # Refresh from database
        self.user.refresh_from_db()
        
        # Verify reverted to Version 1 state
        self.assertEqual(self.user.name, 'Version 1')
        self.assertTrue(self.user.active)
        self.assertEqual(self.user.role, Member.Role.VOLUNTEER)  # Original value
        self.assertEqual(self.user.gender, Member.Gender.MALE)  # Original value
        
        # Verify a new history record was created for the revert
        self.assertEqual(self.user.history.count(), 5)  # Original + 3 changes + revert

    def test_bulk_history_creation(self):
        """Test bulk operations and history creation."""
        # Create a batch of users
        batch_size = 5
        users = []
        for i in range(batch_size):
            user = User.objects.create_user(
                email=f'batch{i}@example.com',
                password='password123',
                name=f'Batch User {i}'
            )
            users.append(user)
        
        # Verify each user has a history record
        for user in users:
            self.assertEqual(user.history.count(), 1)
        
        # Bulk update
        for i, user in enumerate(users):
            user.active = True
            user.name = f'Updated Batch User {i}'
        
        User.objects.bulk_update(users, ['active', 'name'])
        
        # No new history records for bulk update
        for user in users:
            user.refresh_from_db()
            self.assertEqual(user.history.count(), 1)  # Still only the creation record
            
        # Regular updates do create history records
        for i, user in enumerate(users):
            user.membership_active = True
            user.save()
            self.assertEqual(user.history.count(), 2)  # Creation + update 
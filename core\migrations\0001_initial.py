# Generated by Django 5.1.7 on 2025-03-17 12:51

import django.db.models.deletion
import django.utils.timezone
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('billing_address', models.CharField(blank=True, default='', max_length=255)),
                ('address_2', models.CharField(blank=True, default='', max_length=255)),
                ('department_address', models.CharField(blank=True, default='', max_length=255)),
                ('account_type', models.CharField(blank=True, default='', max_length=255)),
                ('city', models.Char<PERSON>ield(blank=True, default='', max_length=255)),
                ('county', models.CharField(blank=True, default='', max_length=255)),
                ('zip_code', models.CharField(blank=True, default='', max_length=10)),
                ('state', models.CharField(default='MS', max_length=2)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Member',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('username', models.CharField(blank=True, max_length=150, null=True)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('name', models.CharField(default='', max_length=255)),
                ('mi', models.CharField(blank=True, default='', max_length=255)),
                ('dst', models.CharField(blank=True, default='', max_length=255)),
                ('title', models.CharField(blank=True, default='', max_length=255)),
                ('address', models.CharField(blank=True, default='', max_length=255)),
                ('city', models.CharField(blank=True, default='', max_length=255)),
                ('st', models.CharField(default='MS', max_length=2)),
                ('zip_code', models.CharField(blank=True, default='', max_length=10)),
                ('home_phone', models.CharField(blank=True, default='', max_length=255)),
                ('business_phone', models.CharField(blank=True, default='', max_length=255)),
                ('membership_class', models.CharField(blank=True, choices=[('member', 'Member'), ('associate_member', 'Associate Member'), ('honorary_member', 'Honorary Member'), ('life_member', 'Life Member')], default='member', max_length=20, null=True)),
                ('executive_board', models.BooleanField(default=False)),
                ('committee_member', models.BooleanField(default=False)),
                ('committee', models.CharField(blank=True, default='', max_length=255)),
                ('new_member', models.BooleanField(default=False)),
                ('lifetime', models.BooleanField(default=False)),
                ('paid_next_year', models.BooleanField(default=False)),
                ('lapel_pin', models.CharField(blank=True, default='', max_length=255)),
                ('is_deceased', models.BooleanField(default=False, verbose_name='Deceased')),
                ('active', models.BooleanField(default=False, verbose_name='Is Active?')),
                ('orig_join_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, default='')),
                ('picture', models.TextField(blank=True, default='')),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('date_updated', models.DateTimeField(auto_now=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='members', to='core.department')),
            ],
            options={
                'ordering': ['name', '-active', 'id'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalDepartment',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=255)),
                ('billing_address', models.CharField(blank=True, default='', max_length=255)),
                ('address_2', models.CharField(blank=True, default='', max_length=255)),
                ('department_address', models.CharField(blank=True, default='', max_length=255)),
                ('account_type', models.CharField(blank=True, default='', max_length=255)),
                ('city', models.CharField(blank=True, default='', max_length=255)),
                ('county', models.CharField(blank=True, default='', max_length=255)),
                ('zip_code', models.CharField(blank=True, default='', max_length=10)),
                ('state', models.CharField(default='MS', max_length=2)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical department',
                'verbose_name_plural': 'historical departments',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalMember',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('username', models.CharField(blank=True, max_length=150, null=True)),
                ('email', models.EmailField(db_index=True, max_length=254)),
                ('name', models.CharField(default='', max_length=255)),
                ('mi', models.CharField(blank=True, default='', max_length=255)),
                ('dst', models.CharField(blank=True, default='', max_length=255)),
                ('title', models.CharField(blank=True, default='', max_length=255)),
                ('address', models.CharField(blank=True, default='', max_length=255)),
                ('city', models.CharField(blank=True, default='', max_length=255)),
                ('st', models.CharField(default='MS', max_length=2)),
                ('zip_code', models.CharField(blank=True, default='', max_length=10)),
                ('home_phone', models.CharField(blank=True, default='', max_length=255)),
                ('business_phone', models.CharField(blank=True, default='', max_length=255)),
                ('membership_class', models.CharField(blank=True, choices=[('member', 'Member'), ('associate_member', 'Associate Member'), ('honorary_member', 'Honorary Member'), ('life_member', 'Life Member')], default='member', max_length=20, null=True)),
                ('executive_board', models.BooleanField(default=False)),
                ('committee_member', models.BooleanField(default=False)),
                ('committee', models.CharField(blank=True, default='', max_length=255)),
                ('new_member', models.BooleanField(default=False)),
                ('lifetime', models.BooleanField(default=False)),
                ('paid_next_year', models.BooleanField(default=False)),
                ('lapel_pin', models.CharField(blank=True, default='', max_length=255)),
                ('is_deceased', models.BooleanField(default=False, verbose_name='Deceased')),
                ('active', models.BooleanField(default=False, verbose_name='Is Active?')),
                ('orig_join_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, default='')),
                ('picture', models.TextField(blank=True, default='')),
                ('date_created', models.DateTimeField(blank=True, editable=False)),
                ('date_updated', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('department', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='core.department')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical member',
                'verbose_name_plural': 'historical members',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

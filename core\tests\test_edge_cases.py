"""
Tests for edge cases and security aspects of the core app.
"""
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from django.contrib.auth import get_user_model
from core.models import Department, Event

User = get_user_model()


class SQLInjectionTests(TestCase):
    """Test cases for SQL injection prevention."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Get token for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)

        # Create departments
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create members
        for i in range(5):
            User.objects.create_user(
                email=f'member{i}@example.com',
                password='securepassword123',
                name=f'Member {i}',
                department=self.department,
                active=True
            )

    def test_sql_injection_in_search(self):
        """Test SQL injection attempts in search parameters."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # SQL injection attempts in search parameter
        injection_attempts = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT email, password FROM auth_user; --",
            "' OR '1'='1' --",
            "\\' OR 1=1 --"
        ]

        members_url = reverse('core:admin-member-list')

        for injection in injection_attempts:
            # Try SQL injection in search parameter
            response = self.client.get(f'{members_url}?search={injection}')

            # Check response - should not expose any sensitive data or cause errors
            # The response should be either 200 OK (empty results) or 400 Bad Request (invalid input)
            self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST])

    def test_sql_injection_in_filter(self):
        """Test SQL injection attempts in filter parameters."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # SQL injection attempts in filter parameter
        injection_attempts = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT email, password FROM auth_user; --"
        ]

        members_url = reverse('core:admin-member-list')

        for injection in injection_attempts:
            # Try SQL injection in department filter
            response = self.client.get(f'{members_url}?department={injection}')

            # Check response - should not expose any sensitive data or cause errors
            # The response should be either 200 OK (empty results) or 400 Bad Request (invalid input)
            self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST])


class CSRFProtectionTests(TestCase):
    """Test cases for CSRF protection."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient(enforce_csrf_checks=True)  # Enable CSRF checks

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test User',
            active=True
        )

        # Login URL
        self.login_url = reverse('core:login')

        # Login data
        self.login_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

    def test_csrf_protection_on_login(self):
        """Test CSRF protection on login endpoint."""
        # Make a POST request without CSRF token
        response = self.client.post(self.login_url, self.login_data, format='json')

        # In test environment, DRF returns 400 Bad Request
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class MediaTypeTests(TestCase):
    """Test cases for media type handling."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test User',
            active=True
        )

        # Get token for authentication
        self.token = str(RefreshToken.for_user(self.user).access_token)

        # Login URL
        self.login_url = reverse('core:login')

        # Login data
        self.login_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

    def test_unsupported_content_type(self):
        """Test API response with unsupported Content-Type header."""
        # Set unsupported Content-Type
        response = self.client.post(
            reverse('core:login'),
            data='<request>data</request>',
            content_type='application/xml'
        )

        # Check response - should return 415 Unsupported Media Type
        self.assertEqual(response.status_code, status.HTTP_415_UNSUPPORTED_MEDIA_TYPE)

    def test_unsupported_accept_header(self):
        """Test API response with unsupported Accept header."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')

        # Set unsupported Accept header
        response = self.client.get(
            reverse('core:public-member-list'),
            HTTP_ACCEPT='application/xml'
        )

        # Check response - should return 406 Not Acceptable
        self.assertEqual(response.status_code, status.HTTP_406_NOT_ACCEPTABLE)


class UnicodeHandlingTests(TestCase):
    """Test cases for Unicode handling."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a staff user
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            active=True,
            is_staff=True
        )

        # Get token for authentication
        self.staff_token = str(RefreshToken.for_user(self.staff_user).access_token)

        # Department URLs
        self.departments_url = reverse('core:admin-department-list')
        self.department_create_url = reverse('core:admin-department-create')

    def test_unicode_in_department_name(self):
        """Test handling of Unicode characters in department name."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Department data with Unicode characters
        unicode_data = {
            'name': 'Département Spécial 🔥',
            'department_city': 'Città',
            'department_state': 'MS',
            'department_zip_code': '12345'
        }

        # Create department
        response = self.client.post(self.department_create_url, unicode_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['success'], True)

        # Check that department was created with Unicode name
        department_id = response.data['data']['id']
        department = Department.objects.get(id=department_id)
        self.assertEqual(department.name, 'Département Spécial 🔥')

        # Retrieve department
        detail_url = reverse('core:admin-department-detail', kwargs={'pk': department_id})
        response = self.client.get(detail_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], 'Département Spécial 🔥')

    def test_unicode_in_search(self):
        """Test searching with Unicode characters."""
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Create department with Unicode name
        Department.objects.create(
            name='Département Spécial',
            department_city='Città',
            department_state='MS'
        )

        # Search for department with Unicode characters
        response = self.client.get(f'{self.departments_url}?search=Département')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Check that department was found in the results
        # The response structure might not always be paginated as previously expected
        self.assertIsInstance(response.data['data'], list, "Response data['data'] should be a list of departments")

        # Find the department in the results
        found = False
        for dept in response.data['data']: # Iterate directly over the list
            if dept['name'] == 'Département Spécial':
                found = True
                break

        self.assertTrue(found, "Department with Unicode name not found in search results")

# Generated by Django 5.2.1 on 2025-05-19 16:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0005_remove_historicalpayment_covered_member_count_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalpayment',
            name='total_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Total amount after multiplying by covered members count', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='total_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Total amount after multiplying by covered members count', max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpayment',
            name='amount',
            field=models.DecimalField(decimal_places=2, help_text='Per-member amount before multiplication', max_digits=10),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='payment',
            name='amount',
            field=models.DecimalField(decimal_places=2, help_text='Per-member amount before multiplication', max_digits=10),
        ),
    ]

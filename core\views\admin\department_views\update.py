from rest_framework import status
from rest_framework.generics import UpdateAPIView

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Department
from core.serializers import DepartmentsSerializer


class DepartmentUpdateAPIView(BaseAPIView, UpdateAPIView):
    """
    Update a specific department (admin only)
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentsSerializer
    permission_classes = [IsStaffUser]

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return APIResponse(
            data=serializer.data,
            message="Department updated successfully",
            status_code=status.HTTP_200_OK
        ) 
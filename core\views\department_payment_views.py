"""
Views for department payment functionality.
"""
from django.db.models import Q
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from common.views import BaseAPIView, APIResponse
from common.utils import track_activity
from core.models import Department
from payments.models import Payment


class DepartmentPaymentsView(BaseAPIView, APIView):
    """View for retrieving payments for a department grouped by year"""
    permission_classes = [IsAuthenticated]

    @track_activity(description="Viewed department payment history")
    def get(self, request, department_id=None):
        """
        Get all payments for a specific department grouped by year
        """
        if not department_id:
            department_id = request.query_params.get('department_id')

        if not department_id:
            return APIResponse(
                message="Department ID is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        print("Department pass")
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return APIResponse(
                message="Department not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Get all payments where a member from this department is either the payer or a covered member
        payments = Payment.objects.filter(
            Q(payer__department=department) | Q(covered_members__department=department)
        ).select_related(
            'payer', 'payer__department', 'event_registration'
        ).prefetch_related(
            'covered_members', 'covered_members__department'
        ).distinct().order_by('-paid_year', '-date')

        if not payments.exists():
            return APIResponse(
                message=f"No payments found for department '{department.name}'",
                data=[],
                status_code=status.HTTP_200_OK
            )

        # Group payments by year
        payments_by_year = {}

        # Process each payment
        for payment in payments:
            year = payment.paid_year

            if year not in payments_by_year:
                payments_by_year[year] = []

            # Get all covered members for this payment
            covered_members = payment.covered_members.all()

            # Get payer details
            payer = payment.payer
            payer_department = payer.department

            # Create payment data with all fields from the model
            payment_data = {
                'id': payment.id,
                'payer_id': payer.id,
                'payer_name': payer.name,
                'covered_members': [
                    {
                        'id': member.id,
                        'name': member.name,
                        'email': member.email,
                        'department_id': member.department_id if member.department else None,
                        'department_name': member.department.name if member.department else None,
                        'address': member.address,
                        'city': member.city,
                        'state': member.st,
                        'zip': member.zip_code
                    } for member in covered_members
                ],
                'amount': str(payment.amount),
                'invoice_number': payment.invoice_number,
                'po_number': payment.po_number,
                'paid_year': payment.paid_year,
                'paid_next_year': payment.paid_next_year,
                'payment_link': payment.payment_link,
                'payment_id': payment.payment_id,
                'payment_date': payment.payment_date.isoformat() if payment.payment_date else None,
                'date': payment.date.isoformat(),
                'updated': payment.updated.isoformat(),
                'status': payment.status,
                'notes': payment.notes,
                'payment_type': payment.payment_type,
                'draft': payment.draft,
                'billing_address': payment.billing_address,
                'transaction_id': payment.transaction_id,
                'due_date': payment.due_date.isoformat(),
                'paypal_order_id': payment.paypal_order_id,
                'paypal_payer_id': payment.paypal_payer_id,
                'paypal_payment_id': payment.paypal_payment_id,
                'paypal_fee': str(payment.paypal_fee) if payment.paypal_fee else None,
                'paypal_payment_status': payment.paypal_payment_status,
                'paypal_payment_method': payment.paypal_payment_method,
                'payment_for': payment.payment_for,
                'event_registration_id': payment.event_registration_id,

                # Department information
                'department_id': str(payer_department.id) if payer_department else None,
                'department': payer_department.name if payer_department else None,
                'city': payer.city,
                'state': payer.st,
                'zip': payer.zip_code,
                'address': payer.address,
            }

            payments_by_year[year].append(payment_data)

        # Format the response
        result = []
        for year, year_payments in sorted(payments_by_year.items(), reverse=True):
            result.append({
                "year": year,
                "payments": year_payments
            })

        return APIResponse(
            message=f"Payments for department '{department.name}' retrieved successfully",
            data=result,
            status_code=status.HTTP_200_OK
        )

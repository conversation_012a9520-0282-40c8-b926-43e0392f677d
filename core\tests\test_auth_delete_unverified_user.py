"""
Tests for the DeleteUnverifiedUserView API endpoint.
"""
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APITestCase
from common.models import EmailVerification
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class DeleteUnverifiedUserViewTests(APITestCase):
    """Test cases for the DeleteUnverifiedUserView."""

    def setUp(self):
        """Set up test data."""
        # Create admin user
        self.admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword123',
            name='Admin User'
        )
        
        # Create a regular user for testing
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='regularpassword123',
            name='Regular User'
        )
        
        # Create an unverified user
        self.unverified_user = User.objects.create_user(
            email='<EMAIL>',
            password='unverifiedpassword123',
            name='Unverified User'
        )
        
        # Create a verified user
        self.verified_user = User.objects.create_user(
            email='<EMAIL>',
            password='verifiedpassword123',
            name='Verified User'
        )
        
        # Create verification records
        self.unverified_verification = EmailVerification.objects.create(
            user=self.unverified_user,
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        self.verified_verification = EmailVerification.objects.create(
            user=self.verified_user,
            verified=True,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        # URL for deleting unverified users
        self.delete_unverified_url = lambda user_id: reverse('core:delete-unverified-user', kwargs={'user_id': user_id})

    def test_delete_unverified_user_as_admin(self):
        """Test deleting an unverified user as an admin."""
        # Login as admin
        self.client.force_authenticate(user=self.admin_user)
        
        # Delete the unverified user
        response = self.client.delete(self.delete_unverified_url(self.unverified_user.id))
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('deleted successfully', response.data['message'])
        
        # Verify user is deleted
        self.assertFalse(User.objects.filter(id=self.unverified_user.id).exists())

    def test_cannot_delete_verified_user(self):
        """Test that a verified user cannot be deleted."""
        # Login as admin
        self.client.force_authenticate(user=self.admin_user)
        
        # Try to delete the verified user
        response = self.client.delete(self.delete_unverified_url(self.verified_user.id))
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Cannot delete user with verified email', response.data['message'])
        
        # Verify user is not deleted
        self.assertTrue(User.objects.filter(id=self.verified_user.id).exists())

    def test_delete_nonexistent_user(self):
        """Test deleting a non-existent user."""
        # Login as admin
        self.client.force_authenticate(user=self.admin_user)
        
        # Try to delete a non-existent user
        non_existent_id = 9999
        response = self.client.delete(self.delete_unverified_url(non_existent_id))
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['success'], False)
        self.assertIn('not found', response.data['message'])

    def test_delete_unverified_user_as_non_admin(self):
        """Test that a non-admin user cannot delete users."""
        # Login as regular user
        self.client.force_authenticate(user=self.regular_user)
        
        # Try to delete the unverified user
        response = self.client.delete(self.delete_unverified_url(self.unverified_user.id))
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Verify user is not deleted
        self.assertTrue(User.objects.filter(id=self.unverified_user.id).exists())

    def test_unauthenticated_user_cannot_delete(self):
        """Test that an unauthenticated user cannot delete users."""
        # No authentication
        
        # Try to delete the unverified user
        response = self.client.delete(self.delete_unverified_url(self.unverified_user.id))
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Verify user is not deleted
        self.assertTrue(User.objects.filter(id=self.unverified_user.id).exists()) 
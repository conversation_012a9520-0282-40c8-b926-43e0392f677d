"""
Views for payment management.
"""
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters

from common.views import BaseAPIView, APIResponse
from common.utils import track_activity
from common.pagination import StandardPagination
from common.filters.payment_filters import DynamicFieldsPaymentFilter
from core.models import Member
from payments.models import Payment
from payments.services import PaymentService
from payments.config import PaymentConfig, ErrorMessages, SuccessMessages

from payments.serializers import (
    PaymentSerializer, PaymentCreateSerializer, PaymentInputSerializer
)


class PaymentListView(BaseAPIView, ListAPIView):
    """View for listing payments with filtering and pagination"""
    permission_classes = [IsAuthenticated]
    queryset = Payment.objects.all().select_related('payer', 'event_registration')
    serializer_class = PaymentSerializer
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = DynamicFieldsPaymentFilter

    def get_queryset(self):
        # Return the base queryset ordered by date (newest first)
        queryset = super().get_queryset().order_by('-date')
        return queryset

    @track_activity(description="Viewed payments list")
    def list(self, request, *args, **kwargs):
        # The filtering is handled automatically by ListAPIView before this method is called
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Payments retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Payments retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class PaymentCreateView(BaseAPIView):
    """View for creating payments"""
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        Create a payment for membership or event registration
        """
        serializer = PaymentInputSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse(
                message=serializer.errors,
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        validated_data = serializer.validated_data
        payment_for = validated_data.get('payment_for', Payment.PaymentFor.MEMBERSHIP)
        payer = validated_data.get('payer')

        try:
            # Handle membership payments
            if payment_for == Payment.PaymentFor.MEMBERSHIP:
                covered_members_ids = validated_data.get('covered_members', [])

                # Get member objects
                covered_members = []
                for member_id in covered_members_ids:
                    try:
                        member = Member.objects.get(pk=member_id)
                        covered_members.append(member)
                    except Member.DoesNotExist:
                        # Skip invalid members but log the issue
                        continue

                if not covered_members:
                    return APIResponse(
                        message="No valid covered members found",
                        data=None,
                        status_code=status.HTTP_400_BAD_REQUEST
                    )

                # Calculate per-member amount
                per_member_amount = validated_data.get('member_fee') or validated_data.get('amount', PaymentConfig.DEFAULT_MEMBERSHIP_FEE)

                # Create payment using service
                payment = PaymentService.create_membership_payment(
                    payer=payer,
                    covered_members=covered_members,
                    amount=per_member_amount,
                    payment_type=validated_data.get('payment_type', Payment.PaymentType.PAYPAL),
                    status=validated_data.get('status', Payment.PaymentStatus.PENDING),
                    paid_year=validated_data.get('paid_year'),
                    due_date=validated_data.get('due_date'),
                    notes=validated_data.get('notes'),
                    po_number=validated_data.get('po_number'),
                    draft=validated_data.get('draft', True),
                    billing_address=validated_data.get('billing_address', '')
                )

            # Handle event registration payments
            elif payment_for == Payment.PaymentFor.EVENT:
                event = validated_data.get('event_registration')

                # Create payment using service
                payment = PaymentService.create_event_payment(
                    payer=payer,
                    event_registration=event,
                    payment_type=validated_data.get('payment_type', Payment.PaymentType.PAYPAL),
                    status=validated_data.get('status', Payment.PaymentStatus.PENDING),
                    due_date=validated_data.get('due_date'),
                    notes=validated_data.get('notes'),
                    po_number=validated_data.get('po_number'),
                    draft=validated_data.get('draft', True),
                    billing_address=validated_data.get('billing_address', '')
                )

            else:
                return APIResponse(
                    message="Invalid payment type",
                    data=None,
                    status_code=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            return APIResponse(
                message=f"Error creating payment: {str(e)}",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        response_serializer = PaymentSerializer(payment)
        return APIResponse(
            message=SuccessMessages.PAYMENT_CREATED,
            data=response_serializer.data,
            status_code=status.HTTP_201_CREATED
        )


class PaymentDetailView(BaseAPIView):
    """View for retrieving, updating and deleting a payment"""
    permission_classes = [IsAuthenticated]

    @track_activity(description="Viewed payment details")
    def get(self, request, payment_id, *args, **kwargs):
        """Get payment details"""
        try:
            payment = Payment.objects.select_related('payer', 'event_registration').get(pk=payment_id)
            serializer = PaymentSerializer(payment)
            return APIResponse(
                message="Payment retrieved successfully",
                data=serializer.data,
                status_code=status.HTTP_200_OK
            )
        except Payment.DoesNotExist:
            return APIResponse(
                message=ErrorMessages.PAYMENT_NOT_FOUND,
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, payment_id, *args, **kwargs):
        """Update payment details"""
        try:
            payment = Payment.objects.get(pk=payment_id)

            # Create a copy of the request data for the serializer
            serializer_data = request.data.copy()

            # Pass the serializer data to the serializer
            serializer = PaymentCreateSerializer(payment, data=serializer_data, partial=True)

            if serializer.is_valid():
                # Save the payment with the updated data
                payment = serializer.save()

                # Return the updated payment
                return APIResponse(
                    message="Payment updated successfully",
                    data=PaymentSerializer(payment).data,
                    status_code=status.HTTP_200_OK
                )
            return APIResponse(
                message=serializer.errors,
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Payment.DoesNotExist:
            return APIResponse(
                message=ErrorMessages.PAYMENT_NOT_FOUND,
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

    def delete(self, request, payment_id, *args, **kwargs):
        """Delete a payment"""
        try:
            payment = Payment.objects.get(pk=payment_id)
            payment.delete()
            return APIResponse(
                message=SuccessMessages.PAYMENT_DELETED,
                data=None,
                status_code=status.HTTP_200_OK
            )
        except Payment.DoesNotExist:
            return APIResponse(
                message=ErrorMessages.PAYMENT_NOT_FOUND,
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )


class DepartmentInvoiceCreateView(BaseAPIView):
    """View for creating department invoices"""
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        Create a membership invoice for one or more members
        Expected request data:
        {
            "member_ids": [1, 2, 3],  # List of member IDs
            "due_date": "2024-03-20"  # Due date for the invoice
        }
        """
        member_ids = request.data.get('member_ids', [])
        due_date = request.data.get('due_date')

        if not member_ids:
            return APIResponse(
                message="At least one member ID is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        if not due_date:
            return APIResponse(
                message="Due date is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Validate all members exist
            members = Member.objects.filter(id__in=member_ids)
            if len(members) != len(member_ids):
                return APIResponse(
                    message="One or more member IDs are invalid",
                    data=None,
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # First member is the payer, all members are covered members
            payer = members.first()

            # Create payment using service
            payment = PaymentService.create_membership_payment(
                payer=payer,
                covered_members=list(members),
                amount=PaymentConfig.DEFAULT_MEMBERSHIP_FEE,
                payment_type=Payment.PaymentType.CASH,
                status=Payment.PaymentStatus.INVOICED,
                due_date=due_date,
                notes=f"Membership fee for {len(members)} member(s)",
                draft=False
            )

            response_serializer = PaymentSerializer(payment)
            return APIResponse(
                message="Membership invoice created successfully",
                data=response_serializer.data,
                status_code=status.HTTP_201_CREATED
            )

        except Exception as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
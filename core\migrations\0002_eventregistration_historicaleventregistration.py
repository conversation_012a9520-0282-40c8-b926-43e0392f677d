# Generated by Django 5.1.7 on 2025-03-17 16:05

import core.models.event_registration
import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.Char<PERSON>ield(max_length=100)),
                ('last_name', models.Char<PERSON>ield(max_length=100)),
                ('title', models.CharField(max_length=100)),
                ('fire_department', models.Char<PERSON>ield(max_length=200)),
                ('address', models.CharField(max_length=255)),
                ('city', models.CharField(max_length=100)),
                ('state', models.Char<PERSON>ield(max_length=50)),
                ('zipcode', models.Char<PERSON><PERSON>(max_length=10)),
                ('phone', models.Char<PERSON>ield(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('invoice_number', models.CharField(blank=True, max_length=100, null=True, validators=[core.models.event_registration.validate_invoice_format])),
                ('registration_type', models.CharField(choices=[('NORMAL', 'Normal Registration'), ('LATE', 'Late Registration')], max_length=20)),
                ('number_of_participants', models.PositiveIntegerField(default=1)),
                ('number_of_guests', models.PositiveIntegerField(default=0)),
                ('registration_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('base_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('guest_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='PENDING', max_length=20)),
                ('extra_participants', models.JSONField(blank=True, default=dict)),
                ('group_registration', models.BooleanField(default=False)),
                ('group_members', models.ManyToManyField(blank=True, related_name='group_event_registrations', to=settings.AUTH_USER_MODEL)),
                ('member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='event_registrations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-registration_date'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalEventRegistration',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('title', models.CharField(max_length=100)),
                ('fire_department', models.CharField(max_length=200)),
                ('address', models.CharField(max_length=255)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=50)),
                ('zipcode', models.CharField(max_length=10)),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('invoice_number', models.CharField(blank=True, max_length=100, null=True, validators=[core.models.event_registration.validate_invoice_format])),
                ('registration_type', models.CharField(choices=[('NORMAL', 'Normal Registration'), ('LATE', 'Late Registration')], max_length=20)),
                ('number_of_participants', models.PositiveIntegerField(default=1)),
                ('number_of_guests', models.PositiveIntegerField(default=0)),
                ('registration_date', models.DateTimeField(blank=True, editable=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('base_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('guest_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='PENDING', max_length=20)),
                ('extra_participants', models.JSONField(blank=True, default=dict)),
                ('group_registration', models.BooleanField(default=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('member', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical event registration',
                'verbose_name_plural': 'historical event registrations',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

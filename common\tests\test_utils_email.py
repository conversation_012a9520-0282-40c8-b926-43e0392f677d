"""
Tests for the common app email utilities.

This module contains comprehensive tests for all email-related utilities in the common app,
including email verification, password reset, and email sending functionality.
"""
import uuid
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock, call

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from common.utils.email import (
    send_verification_email,
    verify_email,
    send_password_reset_email,
    verify_password_reset_token
)
from common.models import EmailVerification, PasswordReset

User = get_user_model()


class SendVerificationEmailTests(TestCase):
    """Test cases for the send_verification_email function.
    
    This test suite covers all aspects of the email verification sending process,
    including creation of verification records, reuse of existing records,
    email content verification, and handling of edge cases.
    """

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user with all required fields
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Store the expected verification URL format for testing
        self.verification_url_format = f"{settings.FRONTEND_URL}/verify-email/"

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_verification(self, mock_send_mail):
        """Test that send_verification_email creates a verification record."""
        # Ensure no verifications exist before the test
        self.assertEqual(EmailVerification.objects.count(), 0)
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        self.assertEqual(verification.user, self.user)
        self.assertFalse(verification.verified)
        self.assertIsNotNone(verification.key)
        self.assertIsNotNone(verification.expires_at)
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check email arguments
        args = mock_send_mail.call_args[0]
        kwargs = mock_send_mail.call_args[1]
        
        self.assertEqual(args[0], "Verify your email address")  # subject
        self.assertIn(str(verification.key), args[1])  # plain_message
        self.assertEqual(args[2], settings.DEFAULT_FROM_EMAIL)  # from_email
        self.assertEqual(args[3], [self.user.email])  # recipient_list
        self.assertIn(str(verification.key), kwargs['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_reuses_existing_verification(self, mock_send_mail):
        """Test that send_verification_email reuses an existing unexpired verification."""
        # Create an existing verification
        existing_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=15)  # Not expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that no new verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        
        # Check that the existing verification was returned
        self.assertEqual(verification, existing_verification)
        
        # Check that the email was sent with the existing verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(existing_verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(existing_verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_new_if_expired(self, mock_send_mail):
        """Test that send_verification_email creates a new verification if existing is expired."""
        # Create an expired verification
        expired_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() - timedelta(days=1)  # Expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a new verification was created
        self.assertEqual(EmailVerification.objects.count(), 2)
        self.assertNotEqual(verification, expired_verification)
        self.assertEqual(verification.user, self.user)
        
        # Check that the email was sent with the new verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_new_if_verified(self, mock_send_mail):
        """Test that send_verification_email creates a new verification if existing is verified."""
        # Create a verified verification
        verified_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=True,
            expires_at=timezone.now() + timedelta(days=15)  # Not expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a new verification was created
        self.assertEqual(EmailVerification.objects.count(), 2)
        self.assertNotEqual(verification, verified_verification)
        self.assertEqual(verification.user, self.user)
        
        # Check that the email was sent with the new verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.render_to_string')
    @patch('common.utils.email.send_mail')
    def test_send_verification_email_content(self, mock_send_mail, mock_render_to_string):
        """Test the content of the verification email."""
        # Mock render_to_string to return a simple HTML message
        mock_render_to_string.return_value = '<html>Verification Email</html>'
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check render_to_string call
        mock_render_to_string.assert_called_once_with('emails/verify_email.html', {
            'name': self.user.name,
            'verification_url': f"{settings.FRONTEND_URL}/verify-email/{verification.key}",
            'expiry_days': 30,
        })
        
        # Check send_mail call
        mock_send_mail.assert_called_once_with(
            "Verify your email address",
            mock_send_mail.call_args[0][1],  # plain_message (complex to check exactly)
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message='<html>Verification Email</html>',
            fail_silently=False,
        )
        
        # Check plain message content
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(verification.key), plain_message)
        self.assertIn("30 days", plain_message)


class VerifyEmailTests(TestCase):
    """Test cases for the verify_email function."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        self.verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )

    def test_verify_email_success(self):
        """Test successful email verification."""
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
        
        # Check that the verification was marked as verified
        self.verification.refresh_from_db()
        self.assertTrue(self.verification.verified)

    def test_verify_email_invalid_key(self):
        """Test email verification with invalid key."""
        # Call the function with a non-existent key
        user, error = verify_email(str(uuid.uuid4()))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_already_verified(self):
        """Test email verification with already verified key."""
        # Mark the verification as verified
        self.verification.verified = True
        self.verification.save()
        
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Email has already been verified")

    def test_verify_email_expired(self):
        """Test email verification with expired key."""
        # Set the verification to be expired
        self.verification.expires_at = timezone.now() - timedelta(days=1)
        self.verification.save()
        
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Verification link has expired")

    def test_verify_email_does_not_activate_user(self):
        """Test that verify_email does not activate the user."""
        # Ensure user is not active before verification
        self.user.active = False
        self.user.save()
        
        # Call the function
        verify_email(str(self.verification.key))
        
        # Check that the user is still not active
        self.user.refresh_from_db()
        self.assertFalse(self.user.active)


class SendPasswordResetEmailTests(TestCase):
    """Test cases for the send_password_reset_email function."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_creates_reset(self, mock_send_mail):
        """Test that send_password_reset_email creates a reset record."""
        # Ensure no resets exist before the test
        self.assertEqual(PasswordReset.objects.count(), 0)
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a reset was created
        self.assertEqual(PasswordReset.objects.count(), 1)
        self.assertEqual(reset.user, self.user)
        self.assertFalse(reset.used)
        self.assertIsNotNone(reset.key)
        self.assertIsNotNone(reset.expires_at)
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check email arguments
        args = mock_send_mail.call_args[0]
        kwargs = mock_send_mail.call_args[1]
        
        self.assertEqual(args[0], "Reset your password")  # subject
        self.assertIn(str(reset.key), args[1])  # plain_message
        self.assertEqual(args[2], settings.DEFAULT_FROM_EMAIL)  # from_email
        self.assertEqual(args[3], [self.user.email])  # recipient_list
        self.assertIn(str(reset.key), kwargs['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_invalidates_existing_resets(self, mock_send_mail):
        """Test that send_password_reset_email invalidates existing unexpired resets."""
        # Create an existing reset
        existing_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=12)  # Not expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, existing_reset)
        
        # Check that the existing reset was marked as used
        existing_reset.refresh_from_db()
        self.assertTrue(existing_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.render_to_string')
    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_content(self, mock_send_mail, mock_render_to_string):
        """Test the content of the password reset email."""
        # Mock render_to_string to return a simple HTML message
        mock_render_to_string.return_value = '<html>Password Reset Email</html>'
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check render_to_string call
        mock_render_to_string.assert_called_once_with('emails/reset_password.html', {
            'name': self.user.name,
            'reset_url': f"{settings.FRONTEND_URL}/reset-password/{reset.key}",
            'expiry_hours': 24,
        })
        
        # Check send_mail call
        mock_send_mail.assert_called_once_with(
            "Reset your password",
            mock_send_mail.call_args[0][1],  # plain_message (complex to check exactly)
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message='<html>Password Reset Email</html>',
            fail_silently=False,
        )
        
        # Check plain message content
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(reset.key), plain_message)
        self.assertIn("24 hours", plain_message)


class VerifyPasswordResetTokenTests(TestCase):
    """Test cases for the verify_password_reset_token function."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_verify_password_reset_token_success(self):
        """Test successful password reset token verification."""
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
        
        # Check that the reset was NOT marked as used (that happens later)
        self.reset.refresh_from_db()
        self.assertFalse(self.reset.used)

    def test_verify_password_reset_token_invalid_key(self):
        """Test password reset token verification with invalid key."""
        # Call the function with a non-existent key
        user, error = verify_password_reset_token(str(uuid.uuid4()))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_already_used(self):
        """Test password reset token verification with already used key."""
        # Mark the reset as used
        self.reset.used = True
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "This password reset link has already been used")

    def test_verify_password_reset_token_expired(self):
        """Test password reset token verification with expired key."""
        # Set the reset to be expired
        self.reset.expires_at = timezone.now() - timedelta(hours=1)
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Password reset link has expired")

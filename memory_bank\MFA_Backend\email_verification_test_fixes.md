# Email Verification Test Fixes

## Issues Fixed

1. **EmailVerification Model Issues**
   - The EmailVerification model had a non-nullable expires_at field but no default value
   - Fixed by making the field nullable (null=True, blank=True) and ensuring it's set in the save method

2. **User Model is_email_verified Setter Issues**
   - The is_email_verified setter was trying to create an EmailVerification object before the user was saved
   - This caused a "Model instances passed to related filters must be saved" error
   - Fixed by storing the value in a temporary attribute and processing it after the user is saved

3. **Member Model Field Structure**
   - Tests were using first_name and last_name fields, but the Member model uses a single name field
   - Updated all test cases to use the correct field structure

4. **Activity Tracking Test Issues**
   - Tests were using mock user objects that didn't have the required _state attribute
   - Fixed by using actual user objects instead of mocks

## Implementation Details

1. **EmailVerification Model Changes**
   ```python
   class EmailVerification(models.Model):
       # Changed expires_at to allow null values
       expires_at = models.DateTimeField(null=True, blank=True)
       
       def save(self, *args, **kwargs):
           # Simplified check to set expires_at if not already set
           if not self.expires_at:
               self.expires_at = timezone.now() + timedelta(days=30)
           return super().save(*args, **kwargs)
   ```

2. **Member Model is_email_verified Setter Changes**
   ```python
   @is_email_verified.setter
   def is_email_verified(self, value):
       """
       Setter for backward compatibility with tests
       """
       # Store the value to be processed after the user is saved
       self._is_email_verified_value = value
   ```

3. **Member Model save Method Changes**
   ```python
   def save(self, *args, **kwargs):
       # Existing code...
       super().save(*args, **kwargs)
       
       # Process email verification after the user is saved
       if hasattr(self, '_is_email_verified_value') and self._is_email_verified_value is True:
           # Import here to avoid circular import
           from common.models import EmailVerification
           # Get or create a verification record
           verification, created = EmailVerification.objects.get_or_create(
               user=self, 
               defaults={'verified': True}
           )
           if not verification.verified:
               verification.verified = True
               verification.save()
   ```

4. **Test Case Fixes**
   - Updated all test cases to use the correct field structure (name instead of first_name/last_name)
   - Used actual user objects instead of mocks in activity tracking tests

## Lessons Learned

1. **Model Relationships in Django**
   - When creating related objects, ensure the parent object is saved first
   - Use post-save processing for operations that require the object to be saved

2. **Field Structure Consistency**
   - Ensure tests use the correct field structure that matches the model definition
   - Be careful when using custom user models with different field structures

3. **Mock Objects Limitations**
   - Mock objects may not have all the attributes required by Django's ORM
   - For database operations, it's often better to use actual model instances

4. **Nullable Fields in Models**
   - Make fields nullable when they might not be set at creation time
   - Provide default values or set them in the save method
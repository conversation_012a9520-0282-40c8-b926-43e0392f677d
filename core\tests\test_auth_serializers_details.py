"""
Test cases for user details and unverified user authentication serializers.
"""
from django.test import TestCase, RequestFactory
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from rest_framework.test import APITestCase

from common.models import EmailVerification
from core.models import Member, Department
from core.serializers.auth.auth_serializers import (
    UserDetailsSerializer,
    UnverifiedUserListSerializer
)


class UserDetailsSerializerTests(APITestCase):
    """Test cases for the UserDetailsSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create a superuser
        self.superuser = Member.objects.create_superuser(
            email='<EMAIL>',
            password='securepassword123',
            name='Super User',
            department=self.department
        )

        # Create a staff user
        self.staff_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            department=self.department,
            is_staff=True,
            active=True
        )

        # Create a regular user
        self.regular_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            department=self.department,
            active=True,
            membership_active=True,
            membership_class=Member.MembershipStatus.MEMBER,
            picture="base64encodedpicture"
        )

        # Create request factory
        self.factory = RequestFactory()

    def test_superuser_serialization(self):
        """Test serialization of a superuser."""
        serializer = UserDetailsSerializer(self.superuser)
        data = serializer.data

        # Check basic fields
        self.assertEqual(data['id'], self.superuser.id)
        self.assertEqual(data['email'], self.superuser.email)
        self.assertEqual(data['name'], self.superuser.name)
        self.assertEqual(data['is_staff'], True)
        self.assertEqual(data['is_superuser'], True)
        self.assertEqual(data['active'], True)
        self.assertEqual(data['role'], 'superuser')

        # Check additional fields
        self.assertIn('department', data)
        self.assertIn('membership_class', data)
        self.assertIn('date_created', data)
        self.assertIn('date_updated', data)

    def test_staff_user_serialization(self):
        """Test serialization of a staff user."""
        serializer = UserDetailsSerializer(self.staff_user)
        data = serializer.data

        # Check role is correctly determined
        self.assertEqual(data['role'], 'staff')
        self.assertTrue(data['is_staff'])
        self.assertFalse(data['is_superuser'])

    def test_regular_user_serialization(self):
        """Test serialization of a regular user."""
        serializer = UserDetailsSerializer(self.regular_user)
        data = serializer.data

        # Check role is correctly determined
        self.assertEqual(data['role'], 'user')
        self.assertFalse(data['is_staff'])
        self.assertFalse(data['is_superuser'])

        # Check other fields
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['membership_class'], self.regular_user.membership_class)
        self.assertEqual(data['picture'], self.regular_user.picture)

    def test_admin_viewing_other_user(self):
        """Test an admin viewing another user's details."""
        # Create admin request
        request = self.factory.get('/')
        request.user = self.staff_user

        serializer = UserDetailsSerializer(
            self.regular_user,
            context={'request': request}
        )
        data = serializer.data

        # Admin should see all fields of other users
        self.assertEqual(data['email'], self.regular_user.email)
        self.assertEqual(data['name'], self.regular_user.name)
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['membership_class'], self.regular_user.membership_class)
        self.assertEqual(data['active'], self.regular_user.active)
        self.assertEqual(data['role'], 'user')

    def test_user_viewing_self(self):
        """Test a user viewing their own details."""
        # Create user request
        request = self.factory.get('/')
        request.user = self.regular_user

        serializer = UserDetailsSerializer(
            self.regular_user,
            context={'request': request}
        )
        data = serializer.data

        # User should see all their own fields
        self.assertEqual(data['email'], self.regular_user.email)
        self.assertEqual(data['name'], self.regular_user.name)
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['membership_class'], self.regular_user.membership_class)

    def test_user_viewing_other_user(self):
        """Test a regular user viewing another user's details."""
        # Create user request
        request = self.factory.get('/')
        request.user = self.regular_user

        serializer = UserDetailsSerializer(
            self.staff_user,
            context={'request': request}
        )
        data = serializer.data

        # Regular users should only see limited fields of other users
        self.assertEqual(data['id'], self.staff_user.id)
        self.assertEqual(data['name'], self.staff_user.name)
        self.assertEqual(data['role'], 'staff')
        self.assertEqual(data['membership_class'], self.staff_user.membership_class)
        self.assertIn('picture', data)

        # Sensitive fields should not be included
        self.assertNotIn('email', data)
        self.assertNotIn('is_staff', data)
        self.assertNotIn('is_superuser', data)
        self.assertNotIn('active', data)
        self.assertNotIn('membership_active', data)
        self.assertNotIn('date_created', data)
        self.assertNotIn('date_updated', data)

    def test_serialization_without_request_context(self):
        """Test serialization without request context."""
        serializer = UserDetailsSerializer(self.regular_user)
        data = serializer.data

        # All fields should be included when no request context is provided
        self.assertEqual(data['id'], self.regular_user.id)
        self.assertEqual(data['email'], self.regular_user.email)
        self.assertEqual(data['name'], self.regular_user.name)
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['membership_class'], self.regular_user.membership_class)

    def test_read_only_fields(self):
        """Test that read-only fields are not writable."""
        data = {
            'name': 'Updated Name',
            'email': '<EMAIL>',
            'is_staff': True,  # This should be ignored (read-only)
            'is_superuser': True,  # This should be ignored (read-only)
            'membership_class': Member.MembershipStatus.ASSOCIATE_MEMBER,
            'department': self.department.id,
            'picture': 'newpicture'
        }

        serializer = UserDetailsSerializer(self.regular_user, data=data, partial=True)
        self.assertTrue(serializer.is_valid())

        updated_user = serializer.save()

        # Writable fields should be updated
        self.assertEqual(updated_user.name, 'Updated Name')
        self.assertEqual(updated_user.email, '<EMAIL>')
        self.assertEqual(updated_user.membership_class, Member.MembershipStatus.ASSOCIATE_MEMBER)
        self.assertEqual(updated_user.picture, 'newpicture')

        # Read-only fields should not be updated
        self.assertFalse(updated_user.is_staff)
        self.assertFalse(updated_user.is_superuser)


class UnverifiedUserListSerializerTests(APITestCase):
    """Test cases for the UnverifiedUserListSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create an unverified user
        self.unverified_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Unverified User',
            department=self.department,
            home_phone='************',
            membership_class=Member.MembershipStatus.MEMBER,
            orig_join_date=timezone.now() - timedelta(days=30)
        )

        # Create unverified email verification
        self.unverified_verification = EmailVerification.objects.create(
            user=self.unverified_user,
            verified=False
        )

        # Create a verified user
        self.verified_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Verified User',
            department=self.department,
            home_phone='************',
            membership_class=Member.MembershipStatus.ASSOCIATE_MEMBER,
            orig_join_date=timezone.now() - timedelta(days=60)
        )

        # Create verified email verification
        self.verified_verification = EmailVerification.objects.create(
            user=self.verified_user,
            verified=True
        )

        # Create a user without department
        self.no_dept_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='No Department User',
            department=None,
            home_phone='************',
            membership_class=Member.MembershipStatus.MEMBER,
            orig_join_date=None
        )

        # Create unverified email verification for no dept user
        self.no_dept_verification = EmailVerification.objects.create(
            user=self.no_dept_user,
            verified=False
        )

    def test_unverified_user_serialization(self):
        """Test serialization of an unverified user."""
        # Use the proper way to set related objects
        self.unverified_verification.user = self.unverified_user
        self.unverified_verification.save()

        serializer = UnverifiedUserListSerializer(self.unverified_user)
        data = serializer.data

        # Check basic fields
        self.assertEqual(data['id'], self.unverified_user.id)
        self.assertEqual(data['email'], self.unverified_user.email)
        self.assertEqual(data['name'], self.unverified_user.name)
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['department_name'], self.department.name)
        self.assertEqual(data['phone_number'], self.unverified_user.home_phone)
        self.assertEqual(data['member_type'], self.unverified_user.membership_class)
        self.assertEqual(data['is_email_verified'], False)
        self.assertIsNotNone(data['date_joined'])

    def test_verified_user_serialization(self):
        """Test serialization of a verified user."""
        # Use the proper way to set related objects
        self.verified_verification.user = self.verified_user
        self.verified_verification.save()

        serializer = UnverifiedUserListSerializer(self.verified_user)
        data = serializer.data

        # Check is_email_verified flag
        self.assertEqual(data['is_email_verified'], True)

    def test_user_without_department(self):
        """Test serialization of a user without department."""
        # Use the proper way to set related objects
        self.no_dept_verification.user = self.no_dept_user
        self.no_dept_verification.save()

        serializer = UnverifiedUserListSerializer(self.no_dept_user)
        data = serializer.data

        # Department fields should be handled gracefully
        self.assertIsNone(data['department'])
        self.assertEqual(data['department_name'], "")

    def test_user_without_join_date(self):
        """Test serialization of a user without join date."""
        # Use the proper way to set related objects
        # (Already set in previous test)

        serializer = UnverifiedUserListSerializer(self.no_dept_user)
        data = serializer.data

        # Join date should be None
        self.assertIsNone(data['date_joined'])

    def test_is_email_verified_without_prefetch(self):
        """Test is_email_verified method without prefetched relations."""
        # Don't prefetch email_verifications to test the fallback query
        serializer = UnverifiedUserListSerializer(self.verified_user)
        data = serializer.data

        # The serializer should handle the missing prefetch and query the database directly
        self.assertEqual(data['is_email_verified'], True)

        # Same test for unverified user
        serializer = UnverifiedUserListSerializer(self.unverified_user)
        data = serializer.data

        self.assertEqual(data['is_email_verified'], False)

    def test_multiple_verifications(self):
        """Test user with multiple email verifications."""
        # Create another verification for the same user (one verified, one not)
        second_verification = EmailVerification.objects.create(
            user=self.unverified_user,
            verified=True
        )

        # Both verifications are already associated with the user
        # Just make sure they're saved
        self.unverified_verification.save()
        second_verification.save()

        serializer = UnverifiedUserListSerializer(self.unverified_user)
        data = serializer.data

        # If any verification is verified, the user is considered verified
        self.assertEqual(data['is_email_verified'], True)

    def test_read_only_fields(self):
        """Test that all fields are read-only."""
        data = {
            'email': '<EMAIL>',
            'name': 'Changed Name',
            'department': None,
            'phone_number': '************',
            'member_type': Member.MembershipStatus.HONORARY_MEMBER,
            'is_email_verified': True,
            'date_joined': timezone.now().isoformat()
        }

        # Email verifications are already associated with the user

        # Create a serializer with changes
        serializer = UnverifiedUserListSerializer(self.unverified_user, data=data)

        # It should be valid since all fields are read-only and ignored for validation
        self.assertTrue(serializer.is_valid())

        # Saving shouldn't modify anything since all fields are read-only
        if hasattr(serializer, 'save'):  # Check if save method exists
            updated_user = serializer.save()

            # The user should not be changed
            self.assertEqual(updated_user.email, self.unverified_user.email)
            self.assertEqual(updated_user.name, self.unverified_user.name)
            self.assertEqual(updated_user.department, self.department)
            self.assertEqual(updated_user.home_phone, self.unverified_user.home_phone)
            self.assertEqual(updated_user.membership_class, self.unverified_user.membership_class)
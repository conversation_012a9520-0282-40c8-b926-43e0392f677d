"""
Tests for the password reset views in core/views/password_reset.py
"""
import uuid
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework import serializers

from common.models import PasswordReset
from core.views.password_reset import (
    RequestPasswordResetView,
    ValidateResetTokenView,
    PasswordResetConfirmView
)

User = get_user_model()


class RequestPasswordResetViewTests(TestCase):
    """Test cases for the RequestPasswordResetView."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:request-password-reset')

        # Create active user
        self.active_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Active User',
            active=True
        )

        # Create inactive user
        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Inactive User',
            active=False
        )

    @patch('core.serializers.auth.password_reset.RequestPasswordResetSerializer.save')
    def test_request_with_valid_email(self, mock_save):
        """Test requesting password reset with a valid email."""
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('If your email address exists in our system, you will receive password reset instructions shortly', response.data['message'])

        # Check that serializer save was called
        mock_save.assert_called_once()

    @patch('core.serializers.auth.password_reset.RequestPasswordResetSerializer.save')
    def test_request_with_nonexistent_email(self, mock_save):
        """Test requesting password reset with a nonexistent email."""
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('If your email address exists in our system, you will receive password reset instructions shortly', response.data['message'])

        # Save is still called (the serializer handles the non-existent email)
        mock_save.assert_called_once()

    def test_request_with_invalid_data(self):
        """Test requesting password reset with invalid data."""
        # Missing email
        response = self.client.post(self.url, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Invalid request', response.data['message'])
        self.assertIn('email', response.data['data'])

        # Invalid email format
        response = self.client.post(self.url, {'email': 'not-an-email'})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Invalid request', response.data['message'])
        self.assertIn('email', response.data['data'])

    @patch('common.utils.email.send_password_reset_email')
    def test_request_integration_with_active_user(self, mock_send_email):
        """Test requesting password reset with an active user (integration test)."""
        # No need to mock the return value unless the function actually returns something crucial

        response = self.client.post(self.url, {'email': '<EMAIL>'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('If your email address exists in our system, you will receive password reset instructions shortly', response.data['message'])

    @patch('common.utils.email.send_password_reset_email')
    def test_request_integration_with_inactive_user(self, mock_send_email):
        """Test requesting password reset with an inactive user (integration test)."""
        # Create a mock reset record to return
        mock_reset = PasswordReset(key=uuid.uuid4())
        mock_send_email.return_value = mock_reset

        response = self.client.post(self.url, {'email': '<EMAIL>'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('If your email address exists in our system, you will receive password reset instructions shortly', response.data['message'])

        # Check that send_password_reset_email was not called
        mock_send_email.assert_not_called()


class ValidateResetTokenViewTests(TestCase):
    """Test cases for the ValidateResetTokenView."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:validate-reset-token')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test User',
            active=True
        )

        # Create valid reset token
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # Create expired reset token
        self.expired_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(hours=1)
        )

        # Create used reset token
        self.used_reset = PasswordReset.objects.create(
            user=self.user,
            used=True,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_validate_with_valid_token(self, mock_verify):
        """Test validating a valid token."""
        mock_verify.return_value = (self.user, None)

        response = self.client.post(self.url, {'token': self.valid_reset.key})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('email', response.data['data'])
        self.assertEqual(response.data['data']['email'], '<EMAIL>')

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_validate_with_invalid_token(self, mock_verify):
        """Test validating an invalid token."""
        mock_verify.return_value = (None, "Invalid password reset link")

        response = self.client.post(self.url, {'token': uuid.uuid4()})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('token', response.data['data'])

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_validate_with_expired_token(self, mock_verify):
        """Test validating an expired token."""
        mock_verify.return_value = (None, "Password reset link has expired")

        response = self.client.post(self.url, {'token': self.expired_reset.key})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('token', response.data['data'])

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_validate_with_used_token(self, mock_verify):
        """Test validating a used token."""
        mock_verify.return_value = (None, "This password reset link has already been used")

        response = self.client.post(self.url, {'token': self.used_reset.key})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('token', response.data['data'])

    def test_validate_with_invalid_data(self):
        """Test validating with invalid data."""
        # Missing token
        response = self.client.post(self.url, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('token', response.data['data'])

        # Invalid token format
        response = self.client.post(self.url, {'token': 'not-a-uuid'})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('token', response.data['data'])


class PasswordResetConfirmViewTests(TestCase):
    """Test cases for the PasswordResetConfirmView."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:reset-password')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

        # Create valid reset token
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # Valid data for password reset
        self.valid_data = {
            'token': self.valid_reset.key,
            'new_password': 'NewSecurePassword123!',
            'confirm_password': 'NewSecurePassword123!'
        }

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_reset_password_with_valid_data(self, mock_verify):
        """Test resetting password with valid data."""
        mock_verify.return_value = (self.user, None)

        response = self.client.post(self.url, self.valid_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], 'Password has been reset successfully. You can now login with your new password.')

        # Check that the user's password was actually changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewSecurePassword123!'))

        # Verify token was marked as used
        self.valid_reset.refresh_from_db()
        self.assertTrue(self.valid_reset.used)

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_reset_password_with_invalid_token(self, mock_verify):
        """Test resetting password with an invalid token."""
        # Simulate verify_password_reset_token raising the validation error
        mock_verify.side_effect = serializers.ValidationError({
            "token": "Invalid password reset link"
        })

        data = self.valid_data.copy()
        data['token'] = uuid.uuid4()  # Use a random UUID

        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['message'], 'Invalid token')
        self.assertIn('token', response.data['data']) # Check specific error field
        self.assertIn('Invalid password reset link', response.data['data']['token'])
        mock_verify.assert_called_once_with(data['token']) # Ensure verify was called

    def test_reset_password_with_passwords_dont_match(self):
        """Test resetting password when new passwords don't match."""
        data = self.valid_data.copy()
        data['confirm_password'] = 'DifferentPassword123!'

        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['message'], "Password fields didn't match")
        self.assertIn('confirm_password', response.data['data']) # Check specific error field
        self.assertIn("Password fields didn't match.", response.data['data']['confirm_password'])

    @patch('core.views.password_reset.PasswordResetConfirmSerializer')
    def test_reset_password_with_weak_password(self, mock_serializer_class):
        """Test resetting password with a weak password."""
        # Create a mock serializer instance
        mock_instance = MagicMock()
        mock_serializer_class.return_value = mock_instance

        # Configure the mock serializer
        mock_instance.is_valid.return_value = False
        expected_error_message = 'This password is too short. It must contain at least 8 characters.'
        error_detail = serializers.ErrorDetail(string=expected_error_message, code='password_too_short')
        mock_instance.errors = {'new_password': [error_detail]}

        data = self.valid_data.copy()
        data['new_password'] = 'weak'
        data['confirm_password'] = 'weak'

        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['message'], 'Password reset failed') # This message is still expected to be generic
        self.assertIn('new_password', response.data['data']) # Check specific error field

        # Extract the string from the ErrorDetail list
        actual_error_messages = [str(detail) for detail in response.data['data']['new_password']]
        self.assertIn(expected_error_message, actual_error_messages)

        # Verify the serializer was called (without checking exact arguments)
        self.assertEqual(mock_serializer_class.call_count, 1)
        # Ensure is_valid was called
        mock_instance.is_valid.assert_called_once()


class PasswordResetIntegrationTests(TestCase):
    """Integration tests for the password reset flow."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.request_url = reverse('core:request-password-reset')
        self.validate_url = reverse('core:validate-reset-token')
        self.reset_url = reverse('core:reset-password')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Integration User',
            active=True
        )

    @patch('common.utils.email.send_mail')
    def test_complete_password_reset_flow(self, mock_send_mail):
        """Test the complete password reset flow."""
        # Step 1: Request password reset
        response = self.client.post(self.request_url, {'email': '<EMAIL>'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get the reset token (would normally be sent via email)
        reset = PasswordReset.objects.filter(user=self.user, used=False).first()
        self.assertIsNotNone(reset)

        # Step 2: Validate the reset token
        response = self.client.post(self.validate_url, {'token': reset.key})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['email'], '<EMAIL>')

        # Step 3: Reset the password
        response = self.client.post(self.reset_url, {
            'token': reset.key,
            'new_password': 'NewIntegrationPass123!',
            'confirm_password': 'NewIntegrationPass123!'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewIntegrationPass123!'))

        # Verify token was marked as used
        reset.refresh_from_db()
        self.assertTrue(reset.used)

        # Attempt to use the token again should fail
        response = self.client.post(self.reset_url, {
            'token': reset.key,
            'new_password': 'AnotherNewPass123!',
            'confirm_password': 'AnotherNewPass123!'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Password should not have changed again
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewIntegrationPass123!'))
        self.assertFalse(self.user.check_password('AnotherNewPass123!'))
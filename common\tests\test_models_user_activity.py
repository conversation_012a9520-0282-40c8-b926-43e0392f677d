"""
Tests for the UserActivity model in the common app.

This module contains comprehensive tests for the UserActivity model,
including creation, properties, and methods.
"""
from datetime import timedelta

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from common.models import UserActivity

User = get_user_model()


class UserActivityModelTests(TestCase):
    """Test cases for the UserActivity model."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create an activity record
        self.activity = UserActivity.objects.create(
            user=self.user,
            description='Test activity'
        )

    def test_user_activity_creation(self):
        """Test that a UserActivity instance can be created with correct attributes."""
        self.assertIsInstance(self.activity, UserActivity)
        self.assertEqual(self.activity.user, self.user)
        self.assertEqual(self.activity.description, 'Test activity')
        self.assertIsNotNone(self.activity.timestamp)

    def test_user_activity_string_representation(self):
        """Test the string representation of UserActivity."""
        expected_string = f"{self.user.email} - Test activity - {self.activity.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
        self.assertEqual(str(self.activity), expected_string)

    def test_user_activity_ordering(self):
        """Test that UserActivities are ordered by timestamp in descending order."""
        # Create a first activity
        first_activity = UserActivity.objects.create(
            user=self.user,
            description="Test activity"
        )
        
        # Add a small delay to ensure different timestamps
        import time
        time.sleep(0.1)
        
        # Create a second activity (which should appear first due to later timestamp)
        second_activity = UserActivity.objects.create(
            user=self.user,
            description="Second activity"
        )
        
        # Get all activities (should be ordered by -timestamp)
        activities = UserActivity.objects.all()
        
        # Verify that the second activity (created later) appears first
        self.assertEqual(activities[0].description, second_activity.description)
        self.assertEqual(activities[1].description, first_activity.description)
        
        # Verify timestamps are in the correct order
        self.assertGreater(activities[0].timestamp, activities[1].timestamp)

    def test_user_activity_meta_options(self):
        """Test the Meta options of UserActivity."""
        self.assertEqual(UserActivity._meta.ordering, ['-timestamp'])
        self.assertEqual(UserActivity._meta.verbose_name, 'User Activity')
        self.assertEqual(UserActivity._meta.verbose_name_plural, 'User Activities')
    
    def test_user_activity_with_long_description(self):
        """Test that UserActivity can be created with a long description."""
        long_description = 'A' * 255  # Maximum length for CharField(max_length=255)
        
        activity = UserActivity.objects.create(
            user=self.user,
            description=long_description
        )
        
        self.assertEqual(activity.description, long_description)
        self.assertIsInstance(activity, UserActivity)
    
    def test_user_activity_with_too_long_description(self):
        """Test that UserActivity cannot be created with a description that exceeds max_length."""
        too_long_description = 'A' * 256  # Exceeds max_length=255
        
        activity = UserActivity(
            user=self.user,
            description=too_long_description
        )
        
        # Assert that calling full_clean raises ValidationError
        with self.assertRaises(ValidationError):
            activity.full_clean()

    def test_user_activity_with_special_characters(self):
        """Test that UserActivity can be created with special characters in description."""
        special_chars = '!@#$%^&*()_+-=[]{}|;:\'",.<>/?`~'
        
        activity = UserActivity.objects.create(
            user=self.user,
            description=f'Test activity with {special_chars}'
        )
        
        self.assertEqual(activity.description, f'Test activity with {special_chars}')
        self.assertIsInstance(activity, UserActivity)
    
    def test_user_activity_filter_by_user(self):
        """Test filtering UserActivity by user."""
        # Create a second user and activity
        second_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Second User'
        )
        
        second_user_activity = UserActivity.objects.create(
            user=second_user,
            description='Second user activity'
        )
        
        # Filter activities by first user
        first_user_activities = UserActivity.objects.filter(user=self.user)
        self.assertEqual(first_user_activities.count(), 1)
        self.assertEqual(first_user_activities[0], self.activity)
        
        # Filter activities by second user
        second_user_activities = UserActivity.objects.filter(user=second_user)
        self.assertEqual(second_user_activities.count(), 1)
        self.assertEqual(second_user_activities[0], second_user_activity)
    
    def test_user_activity_filter_by_description(self):
        """Test filtering UserActivity by description."""
        # Create activities with different descriptions
        UserActivity.objects.create(
            user=self.user,
            description='Login activity'
        )
        
        UserActivity.objects.create(
            user=self.user,
            description='Logout activity'
        )
        
        # Filter activities by description containing 'Login'
        login_activities = UserActivity.objects.filter(description__contains='Login')
        self.assertEqual(login_activities.count(), 1)
        self.assertEqual(login_activities[0].description, 'Login activity')
        
        # Filter activities by description containing 'activity'
        all_activities = UserActivity.objects.filter(description__contains='activity')
        self.assertEqual(all_activities.count(), 3)  # Original + 2 new ones
    
    def test_user_activity_filter_by_timestamp(self):
        """Test filtering UserActivity by timestamp."""
        # Create an activity with a specific timestamp
        past_time = timezone.now() - timedelta(days=7)
        past_activity = UserActivity.objects.create(
            user=self.user,
            description='Past activity'
        )
        
        # Manually update the timestamp to be in the past
        UserActivity.objects.filter(pk=past_activity.pk).update(timestamp=past_time)
        past_activity.refresh_from_db()
        
        # Create a future activity
        future_time = timezone.now() + timedelta(days=1)
        future_activity = UserActivity.objects.create(
            user=self.user,
            description='Future activity'
        )
        
        # Manually update the timestamp to be in the future
        UserActivity.objects.filter(pk=future_activity.pk).update(timestamp=future_time)
        future_activity.refresh_from_db()
        
        # Filter activities by timestamp
        past_activities = UserActivity.objects.filter(timestamp__lt=timezone.now() - timedelta(days=1))
        self.assertEqual(past_activities.count(), 1)
        self.assertEqual(past_activities[0].description, 'Past activity')
        
        recent_activities = UserActivity.objects.filter(timestamp__gte=timezone.now() - timedelta(days=1))
        self.assertEqual(recent_activities.count(), 2)  # Original + future activity

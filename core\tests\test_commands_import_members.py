"""
Tests for the import_members management command.
"""
import os
import csv
import tempfile
from io import StringIO
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.core.management import call_command
from django.db import IntegrityError
from django.utils import timezone
from core.models import Member, Department
from core.management.commands.import_members import Command


class ImportMembersCommandTests(TestCase):
    """Test cases for the import_members management command."""

    def setUp(self):
        """Set up test data."""
        # Create a test department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )
        
        # Initial count of members
        self.initial_member_count = Member.objects.count()
        
        # Create a temporary CSV file for testing
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.csv')
        self.csv_writer = csv.writer(self.temp_file)
        
        # Write header row
        self.csv_writer.writerow([
            'First Name', 'Last Name', 'Email', 'District', 'county', 'Account',
            'Lead Status', 'Address 1', 'Address 2', 'City', 'State', 'Zip',
            'Phone', 'Role', 'Gender', 'DOB'
        ])
        
        # Write some test data
        self.csv_writer.writerow([
            '<PERSON>', 'Doe', '<EMAIL>', 'North', 'Test County', '12345',
            'Active', '123 Main St', 'Apt 4B', 'Test City', 'MS', '12345',
            '555-1234', 'Volunteer', 'Male', '01-15-1980 0:00'
        ])
        
        self.csv_writer.writerow([
            'Jane', 'Smith', '<EMAIL>', 'South', 'Test County', '67890',
            'Inactive', '456 Oak St', '', 'Test City', 'MS', '12345',
            '555-5678', 'Career', 'Female', '05-20-1990 12:00:00 AM'
        ])
        
        # Close the file
        self.temp_file.close()
    
    def tearDown(self):
        """Clean up after tests."""
        # Delete the temporary file
        os.unlink(self.temp_file.name)
    
    def test_import_members_basic(self):
        """Test basic import functionality."""
        # Redirect stdout to capture output
        out = StringIO()
        
        # Call the command
        call_command('import_members', self.temp_file.name, stdout=out)
        
        # Check output
        output = out.getvalue()
        self.assertIn('Import completed successfully', output)
        self.assertIn('Members created: 2', output)
        
        # Check that members were created
        self.assertEqual(Member.objects.count(), self.initial_member_count + 2)
        
        # Check specific member data
        john = Member.objects.get(email='<EMAIL>')
        self.assertEqual(john.name, 'John Doe')
        self.assertEqual(john.dst, 'North')
        self.assertEqual(john.county, 'Test County')
        self.assertEqual(john.account, '12345')
        self.assertEqual(john.lead_status, 'active')
        self.assertEqual(john.address, '123 Main St, Apt 4B')
        self.assertEqual(john.city, 'Test City')
        self.assertEqual(john.st, 'MS')
        self.assertEqual(john.zip_code, '12345')
        self.assertEqual(john.home_phone, '555-1234')
        self.assertEqual(john.role, 'volunteer')
        self.assertEqual(john.gender, 'male')
        self.assertIsNotNone(john.dob)
    
    def test_import_members_update_existing(self):
        """Test updating existing members."""
        # Create a member that will be updated
        existing_member = Member.objects.create(
            email='<EMAIL>',
            name='John Old',
            dst='Old District',
            county='Old County',
            account='Old Account',
            lead_status='inactive',
            address='Old Address',
            city='Old City',
            st='AL',
            zip_code='54321',
            home_phone='555-9999',
            role='career',
            gender='other'
        )
        
        # Redirect stdout to capture output
        out = StringIO()
        
        # Call the command
        call_command('import_members', self.temp_file.name, stdout=out)
        
        # Check output
        output = out.getvalue()
        self.assertIn('Import completed successfully', output)
        self.assertIn('Members created: 1', output)
        self.assertIn('Members updated: 1', output)
        
        # Check that the member was updated
        existing_member.refresh_from_db()
        self.assertEqual(existing_member.name, 'John Doe')
        self.assertEqual(existing_member.dst, 'North')
        self.assertEqual(existing_member.county, 'Test County')
        self.assertEqual(existing_member.account, '12345')
        self.assertEqual(existing_member.lead_status, 'active')
        self.assertEqual(existing_member.address, '123 Main St, Apt 4B')
        self.assertEqual(existing_member.city, 'Test City')
        self.assertEqual(existing_member.st, 'MS')
        self.assertEqual(existing_member.zip_code, '12345')
        self.assertEqual(existing_member.home_phone, '555-1234')
        self.assertEqual(existing_member.role, 'volunteer')
        self.assertEqual(existing_member.gender, 'male')
    
    def test_import_members_missing_email(self):
        """Test importing members with missing email."""
        # Create a temporary CSV file with missing email
        temp_file = tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.csv')
        csv_writer = csv.writer(temp_file)
        
        # Write header row
        csv_writer.writerow([
            'First Name', 'Last Name', 'Email', 'District', 'county', 'Account',
            'Lead Status', 'Address 1', 'Address 2', 'City', 'State', 'Zip',
            'Phone', 'Role', 'Gender', 'DOB'
        ])
        
        # Write test data with missing email
        csv_writer.writerow([
            'No', 'Email', '', 'North', 'Test County', '12345',
            'Active', '123 Main St', '', 'Test City', 'MS', '12345',
            '555-1234', 'Volunteer', 'Male', '01-15-1980 0:00'
        ])
        
        # Close the file
        temp_file.close()
        
        try:
            # Redirect stdout to capture output
            out = StringIO()
            
            # Call the command
            call_command('import_members', temp_file.name, stdout=out)
            
            # Check output
            output = out.getvalue()
            self.assertIn('Import completed successfully', output)
            self.assertIn('Members created: 1', output)
            
            # Check that a member was created with a generated email
            self.assertEqual(Member.objects.count(), self.initial_member_count + 1)
            
            # The new member should have a generated email
            new_member = Member.objects.latest('date_created')
            self.assertIn('@test.com', new_member.email)
            self.assertEqual(new_member.name, 'No Email')
        finally:
            # Delete the temporary file
            os.unlink(temp_file.name)
    
    def test_import_members_missing_name(self):
        """Test importing members with missing name."""
        # Create a temporary CSV file with missing name
        temp_file = tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.csv')
        csv_writer = csv.writer(temp_file)
        
        # Write header row
        csv_writer.writerow([
            'First Name', 'Last Name', 'Email', 'District', 'county', 'Account',
            'Lead Status', 'Address 1', 'Address 2', 'City', 'State', 'Zip',
            'Phone', 'Role', 'Gender', 'DOB'
        ])
        
        # Write test data with missing name
        csv_writer.writerow([
            '', '', '<EMAIL>', 'North', 'Test County', '12345',
            'Active', '123 Main St', '', 'Test City', 'MS', '12345',
            '555-1234', 'Volunteer', 'Male', '01-15-1980 0:00'
        ])
        
        # Close the file
        temp_file.close()
        
        try:
            # Redirect stdout to capture output
            out = StringIO()
            
            # Call the command
            call_command('import_members', temp_file.name, stdout=out)
            
            # Check output
            output = out.getvalue()
            self.assertIn('Import completed successfully', output)
            self.assertIn('Members created: 1', output)
            self.assertIn('Members with unknown names: 1', output)
            
            # Check that a member was created with "Unknown" name
            self.assertEqual(Member.objects.count(), self.initial_member_count + 1)
            
            # The new member should have "Unknown" name
            new_member = Member.objects.get(email='<EMAIL>')
            self.assertEqual(new_member.name, 'Unknown')
        finally:
            # Delete the temporary file
            os.unlink(temp_file.name)
    
    def test_import_members_invalid_date_format(self):
        """Test importing members with invalid date format."""
        # Create a temporary CSV file with invalid date format
        temp_file = tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.csv')
        csv_writer = csv.writer(temp_file)
        
        # Write header row
        csv_writer.writerow([
            'First Name', 'Last Name', 'Email', 'District', 'county', 'Account',
            'Lead Status', 'Address 1', 'Address 2', 'City', 'State', 'Zip',
            'Phone', 'Role', 'Gender', 'DOB'
        ])
        
        # Write test data with invalid date format
        csv_writer.writerow([
            'Bad', 'Date', '<EMAIL>', 'North', 'Test County', '12345',
            'Active', '123 Main St', '', 'Test City', 'MS', '12345',
            '555-1234', 'Volunteer', 'Male', 'not-a-date'
        ])
        
        # Close the file
        temp_file.close()
        
        try:
            # Redirect stdout to capture output
            out = StringIO()
            
            # Call the command
            call_command('import_members', temp_file.name, stdout=out)
            
            # Check output
            output = out.getvalue()
            self.assertIn('Import completed successfully', output)
            self.assertIn('Members created: 1', output)
            self.assertIn('Dates that could not be parsed: 1', output)
            
            # Check that a member was created without DOB
            self.assertEqual(Member.objects.count(), self.initial_member_count + 1)
            
            # The new member should not have a DOB
            new_member = Member.objects.get(email='<EMAIL>')
            self.assertIsNone(new_member.dob)
        finally:
            # Delete the temporary file
            os.unlink(temp_file.name)
    
    def test_import_members_database_error(self):
        """Test handling of database errors during import."""
        # Redirect stdout and stderr to capture output
        out = StringIO()
        err = StringIO()
        
        # Patch the update_or_create method to raise an IntegrityError
        with patch('core.models.Member.objects.update_or_create') as mock_update_or_create:
            mock_update_or_create.side_effect = IntegrityError("Database integrity error")
            
            # Call the command
            call_command('import_members', self.temp_file.name, stdout=out, stderr=err)
        
        # Check output
        output = out.getvalue()
        error_output = err.getvalue()
        
        self.assertIn('Error during import - no data was imported', output)
        self.assertIn('Database integrity error', output)
    
    def test_parse_date_method(self):
        """Test the parse_date method with various date formats."""
        command = Command()
        
        # Test various date formats
        date_formats = [
            ('11-04-34 0:00', True),  # MM-DD-YY HH:MM
            ('11-04-1934 0:00', True),  # MM-DD-YYYY HH:MM
            ('1-22-1990 12:00:00 AM', True),  # M-D-YYYY HH:MM:SS AM/PM
            ('1-22-90 12:00:00 AM', True),  # M-D-YY HH:MM:SS AM/PM
            ('11/04/1934 0:00', True),  # MM/DD/YYYY HH:MM
            ('11/04/34 0:00', True),  # MM/DD/YY HH:MM
            ('not-a-date', False),  # Invalid format
            ('', False)  # Empty string
        ]
        
        for date_str, should_parse in date_formats:
            result = command.parse_date(date_str)
            if should_parse:
                self.assertIsNotNone(result)
                self.assertTrue(timezone.is_aware(result))
            else:
                self.assertIsNone(result)
    
    def test_get_formatted_name_method(self):
        """Test the get_formatted_name method."""
        command = Command()
        
        # Test various name combinations
        name_tests = [
            ('John', 'Doe', 'John Doe'),
            ('John', '', 'John'),
            ('', 'Doe', 'Doe'),
            ('', '', 'Unknown')
        ]
        
        for first_name, last_name, expected in name_tests:
            result = command.get_formatted_name(first_name, last_name)
            self.assertEqual(result, expected)
    
    def test_validate_row_method(self):
        """Test the validate_row method."""
        command = Command()
        
        # For now, the validate_row method doesn't do much validation
        # but we should test it anyway for future-proofing
        row = {
            'First Name': 'John',
            'Last Name': 'Doe',
            'Email': '<EMAIL>'
        }
        
        errors = command.validate_row(row, 1)
        self.assertEqual(errors, [])

"""
Tests for the BaseAPIView and APIResponse classes in the common app.

This module contains comprehensive tests for the BaseAPIView
and APIResponse classes, ensuring proper response formatting.
"""
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.urls import path
from django.urls.exceptions import NoReverseMatch
from rest_framework.test import APIClient, APIRequestFactory, URLPatternsTestCase
from rest_framework import status, serializers
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied, ErrorDetail

from common.views import BaseAPIView, APIResponse


# Define test views for testing
class TestSuccessView(BaseAPIView):
    def get(self, request):
        return self.success_response(data={'key': 'value'}, message="Success message")

    def post(self, request):
        return self.success_response(data={'created': True}, message="Created successfully", status=status.HTTP_201_CREATED)


class TestErrorView(BaseAPIView):
    def get(self, request):
        return self.error_response(message="Error message")

    def post(self, request):
        return self.error_response(message={"field": ["Field error"]}, status=status.HTTP_422_UNPROCESSABLE_ENTITY)


class TestPassthroughView(BaseAPIView):
    def get(self, request):
        # Return a regular DRF response that should be converted
        return Response({"hello": "world"})

    def post(self, request):
        # Return a regular DRF response with error status
        return Response({"detail": "Something went wrong"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TestExceptionView(BaseAPIView):
    def get(self, request):
        raise ValidationError({"field": "This field is invalid"})

    def post(self, request):
        raise NotFound("Resource not found")


class BaseAPIViewTests(URLPatternsTestCase, TestCase):
    """Test cases for the BaseAPIView class."""

    urlpatterns = [
        path('test/success/', TestSuccessView.as_view(), name='test-success'),
        path('test/error/', TestErrorView.as_view(), name='test-error'),
        path('test/passthrough/', TestPassthroughView.as_view(), name='test-passthrough'),
        path('test/exception/', TestExceptionView.as_view(), name='test-exception'),
    ]

    def setUp(self):
        """Set up test data for each test case."""
        self.client = APIClient()
        self.factory = APIRequestFactory()

    def test_success_response_format(self):
        """Test that success_response returns properly formatted APIResponse."""
        response = self.client.get('/test/success/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Success message")
        self.assertEqual(response.data['data'], {'key': 'value'})

    def test_success_response_with_custom_status(self):
        """Test that success_response handles custom status codes."""
        response = self.client.post('/test/success/')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Created successfully")
        self.assertEqual(response.data['data'], {'created': True})

    def test_error_response_format(self):
        """Test that error_response returns properly formatted APIResponse."""
        response = self.client.get('/test/error/')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Error message")
        self.assertIsNone(response.data['data'])

    def test_error_response_with_dict_message(self):
        """Test that error_response formats dictionary messages properly."""
        response = self.client.post('/test/error/')

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "field: Field error")
        self.assertIsNone(response.data['data'])

    def test_passthrough_success_response(self):
        """Test that regular DRF Response objects are converted to APIResponse."""
        response = self.client.get('/test/passthrough/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Operation completed successfully")
        self.assertEqual(response.data['data'], {'hello': 'world'})

    def test_passthrough_error_response(self):
        """Test that regular DRF Response objects with error status are converted correctly."""
        response = self.client.post('/test/passthrough/')

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "detail: Something went wrong")
        self.assertIsNone(response.data['data'])

    def test_format_error_message_with_dict(self):
        """Test _format_error_message with dictionary input."""
        view = TestErrorView()
        errors = {
            'field1': ['Error 1', 'Error 2'],
            'field2': 'Error 3'
        }

        message = view._format_error_message(errors)
        self.assertIn('field1: Error 1', message)
        self.assertIn('field1: Error 2', message)
        self.assertIn('field2: Error 3', message)

    def test_format_error_message_with_drf_errors(self):
        """Test _format_error_message with DRF ErrorDetail objects."""
        view = TestErrorView()
        errors = {
            'field1': [
                ErrorDetail(string='Error 1', code='invalid'),
                ErrorDetail(string='Error 2', code='required')
            ],
            'field2': ErrorDetail(string='Error 3', code='max_length')
        }

        message = view._format_error_message(errors)
        self.assertIn('field1: Error 1', message)
        self.assertIn('field1: Error 2', message)
        self.assertIn('field2: Error 3', message)

    def test_format_error_message_with_string(self):
        """Test _format_error_message with string input."""
        view = TestErrorView()
        error = "This is a string error"

        message = view._format_error_message(error)
        self.assertEqual(message, error)

    def test_format_error_message_with_fallback(self):
        """Test _format_error_message fallback for unknown types."""
        view = TestErrorView()
        error = None  # Not a string or dict

        message = view._format_error_message(error)
        self.assertEqual(message, "An error occurred")

    def test_exception_handling_validation_error(self):
        """Test that ValidationError exceptions are properly handled."""
        response = self.client.get('/test/exception/')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "message: field: This field is invalid. success: False. data: None")

    def test_exception_handling_not_found(self):
        """Test that NotFound exceptions are properly handled."""
        response = self.client.post('/test/exception/')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "message: Resource not found. success: False. data: None")


class APIResponseTests(TestCase):
    """Test cases for the APIResponse class."""

    def test_api_response_success(self):
        """Test APIResponse with success status."""
        response = APIResponse(
            message="Success message",
            data={"key": "value"},
            status_code=status.HTTP_200_OK
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Success message")
        self.assertEqual(response.data['data'], {"key": "value"})

    def test_api_response_error(self):
        """Test APIResponse with error status."""
        response = APIResponse(
            message="Error message",
            data=None,
            error="Detailed error",
            status_code=status.HTTP_400_BAD_REQUEST
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Error message")
        self.assertIsNone(response.data['data'])

    def test_api_response_defaults(self):
        """Test APIResponse with default parameters."""
        response = APIResponse()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIsNone(response.data['message'])
        self.assertIsNone(response.data['data'])

    def test_api_response_success_flag_based_on_status(self):
        """Test that success flag is determined by status code."""
        # 2xx status codes should be success
        response1 = APIResponse(status_code=status.HTTP_200_OK)
        self.assertTrue(response1.data['success'])

        response2 = APIResponse(status_code=status.HTTP_201_CREATED)
        self.assertTrue(response2.data['success'])

        # 4xx and 5xx status codes should be failure
        response3 = APIResponse(status_code=status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response3.data['success'])

        response4 = APIResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertFalse(response4.data['success'])

    def test_api_response_with_serializer_errors(self):
        """Test APIResponse with serializer errors."""
        class TestSerializer(serializers.Serializer):
            name = serializers.CharField(max_length=50)
            email = serializers.EmailField()

        serializer = TestSerializer(data={"name": "", "email": "not-an-email"})
        serializer.is_valid()

        response = APIResponse(
            message=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        # The message should contain the serializer errors directly
        # Use assertIn to check for the presence of error messages rather than exact equality
        self.assertIn('name', response.data['message'])
        self.assertIn('email', response.data['message'])

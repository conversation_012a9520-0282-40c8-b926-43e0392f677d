"""
Payment service for handling payment business logic.
Extracts complex logic from models and views to improve maintainability.
"""
import logging
from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from django.db import transaction, models
from django.utils import timezone

from payments.models import Payment, InvoiceDueDate
from payments.config import PaymentConfig
from core.models import Member, EventRegistration

logger = logging.getLogger(__name__)


class PaymentService:
    """Service class for payment-related business logic"""

    @staticmethod
    def generate_invoice_number(payment_for: str = Payment.PaymentFor.MEMBERSHIP) -> str:
        """Generate invoice number with proper thread safety"""
        today = timezone.now().date()
        julian_day = today.timetuple().tm_yday
        year_suffix = str(today.year)[-2:]

        # Use database-level locking to prevent race conditions
        with transaction.atomic():
            if payment_for == Payment.PaymentFor.EVENT:
                # Event invoice numbers use a different pattern
                last_payment = Payment.objects.select_for_update().filter(
                    payment_for=Payment.PaymentFor.EVENT,
                    invoice_number__startswith=f"{year_suffix}{julian_day:03d}"
                ).aggregate(
                    max_invoice_number=models.Max('invoice_number')
                )

                last_invoice = last_payment['max_invoice_number']
                if last_invoice:
                    try:
                        sequence = int(last_invoice.split('-')[1]) + 1
                    except (IndexError, ValueError):
                        sequence = 1
                else:
                    sequence = 1

                return PaymentConfig.EVENT_INVOICE_NUMBER_FORMAT.format(
                    year=year_suffix, julian_day=julian_day, sequence=sequence
                )
            else:
                # Membership invoice numbers
                last_payment = Payment.objects.select_for_update().filter(
                    payment_for=Payment.PaymentFor.MEMBERSHIP,
                    invoice_number__startswith=f"{year_suffix}{julian_day:03d}"
                ).aggregate(
                    max_invoice_number=models.Max('invoice_number')
                )

                last_invoice = last_payment['max_invoice_number']
                if last_invoice:
                    try:
                        sequence = int(last_invoice.split('-')[1]) + 1
                    except (IndexError, ValueError):
                        sequence = 1
                else:
                    sequence = 1

                return PaymentConfig.INVOICE_NUMBER_FORMAT.format(
                    year=year_suffix, julian_day=julian_day, sequence=sequence
                )

    @staticmethod
    def calculate_total_amount(amount: Decimal, covered_members_count: int) -> Decimal:
        """Calculate total amount based on per-member amount and count"""
        if covered_members_count <= 0:
            return amount
        return amount * covered_members_count

    @staticmethod
    def get_default_due_date() -> Optional[datetime.date]:
        """Get the default due date from InvoiceDueDate model"""
        try:
            latest_due_date = InvoiceDueDate.objects.order_by('-created').first()
            return latest_due_date.due_date if latest_due_date else None
        except Exception as e:
            logger.warning(f"Could not get default due date: {e}")
            return None

    @classmethod
    def create_membership_payment(cls, payer: Member, covered_members: List[Member],
                                 amount: Decimal, **kwargs) -> Payment:
        """Create a membership payment with proper validation and calculations"""
        with transaction.atomic():
            # Calculate total amount
            total_amount = cls.calculate_total_amount(amount, len(covered_members))

            # Set defaults
            payment_data = {
                'payer': payer,
                'amount': amount,
                'total_amount': total_amount,
                'payment_for': Payment.PaymentFor.MEMBERSHIP,
                'payment_type': kwargs.get('payment_type', Payment.PaymentType.PAYPAL),
                'status': kwargs.get('status', Payment.PaymentStatus.PENDING),
                'due_date': kwargs.get('due_date') or cls.get_default_due_date(),
                'paid_year': kwargs.get('paid_year', timezone.now().year),
                'notes': kwargs.get('notes', f"Membership payment for {len(covered_members)} member(s)"),
                'draft': kwargs.get('draft', True),
            }

            # Add optional fields if provided
            optional_fields = ['po_number', 'billing_address', 'paid_next_year']
            for field in optional_fields:
                if field in kwargs:
                    payment_data[field] = kwargs[field]

            # Create payment
            payment = Payment.objects.create(**payment_data)

            # Add covered members
            payment.covered_members.set(covered_members)

            return payment

    @classmethod
    def create_event_payment(cls, payer: Member, event_registration: EventRegistration,
                           **kwargs) -> Payment:
        """Create an event registration payment"""
        with transaction.atomic():
            payment_data = {
                'payer': payer,
                'amount': event_registration.total_amount,
                'total_amount': event_registration.total_amount,
                'payment_for': Payment.PaymentFor.EVENT,
                'event_registration': event_registration,
                'payment_type': kwargs.get('payment_type', Payment.PaymentType.PAYPAL),
                'status': kwargs.get('status', Payment.PaymentStatus.PENDING),
                'due_date': kwargs.get('due_date', timezone.now().date()),
                'paid_year': kwargs.get('paid_year', timezone.now().year),
                'notes': kwargs.get('notes', f"Event registration payment"),
                'draft': kwargs.get('draft', True),
            }

            # Add optional fields if provided
            optional_fields = ['po_number', 'billing_address']
            for field in optional_fields:
                if field in kwargs:
                    payment_data[field] = kwargs[field]

            # Create payment
            payment = Payment.objects.create(**payment_data)

            # Add payer as covered member
            payment.covered_members.add(payer)

            # If group registration, add all group members
            if event_registration.group_registration and event_registration.group_members.exists():
                for member in event_registration.group_members.all():
                    payment.covered_members.add(member)

            return payment

    @staticmethod
    def update_payment_status(payment: Payment, new_status: str,
                            update_related: bool = True) -> None:
        """Update payment status and optionally update related payments"""
        with transaction.atomic():
            old_status = payment.status
            payment.status = new_status

            # Set payment date for successful payments
            if new_status == Payment.PaymentStatus.SUCCESS and not payment.payment_date:
                payment.payment_date = timezone.now().date()

            payment.save()

            # Update related payments with same invoice number if requested
            if update_related and payment.invoice_number:
                related_payments = Payment.objects.filter(
                    invoice_number=payment.invoice_number
                ).exclude(pk=payment.pk)

                if related_payments.exists():
                    related_payments.update(
                        status=new_status,
                        payment_date=payment.payment_date
                    )

                    logger.info(f"Updated {related_payments.count()} related payments "
                              f"from {old_status} to {new_status}")

    @staticmethod
    def update_total_amount(payment: Payment) -> None:
        """Update total amount based on current covered members count"""
        with transaction.atomic():
            covered_members_count = payment.covered_members.count()
            if covered_members_count > 0:
                new_total_amount = payment.amount * covered_members_count

                # Use update to avoid triggering save method
                Payment.objects.filter(pk=payment.pk).update(total_amount=new_total_amount)

                # Update the instance
                payment.total_amount = new_total_amount

    @staticmethod
    def get_payment_description(payment: Payment) -> str:
        """Generate description for payment based on type"""
        if payment.payment_for == Payment.PaymentFor.EVENT:
            if payment.event_registration:
                return f"Event Registration: {payment.event_registration.get_full_name()}"
            else:
                return "Event Registration Payment"
        else:
            members_count = payment.covered_members.count()
            return f"Membership Payment for {members_count} member(s)"

    @staticmethod
    def get_billing_address(member, bill_to: str = None) -> str:
        """
        Generate billing address based on bill_to preference

        Args:
            member: Member object (payer)
            bill_to: 'member' or 'department' to specify address source

        Returns:
            Formatted billing address string
        """
        from payments.config import PaymentConfig

        if bill_to == PaymentConfig.BILL_TO_DEPARTMENT and member.department:
            # Use department billing address
            dept = member.department
            address_parts = []

            if dept.billing_address1:
                address_parts.append(dept.billing_address1)
            if dept.billing_address2:
                address_parts.append(dept.billing_address2)

            # City, State ZIP format
            city_state_zip = []
            if dept.billing_city:
                city_state_zip.append(dept.billing_city)
            if dept.billing_state:
                city_state_zip.append(dept.billing_state)
            if dept.billing_zip_code:
                city_state_zip.append(dept.billing_zip_code)

            if city_state_zip:
                address_parts.append(', '.join(city_state_zip))

            return '\n'.join(address_parts) if address_parts else ""

        else:
            # Use member profile address (default)
            address_parts = []

            if member.address:
                address_parts.append(member.address)

            # City, State ZIP format
            city_state_zip = []
            if member.city:
                city_state_zip.append(member.city)
            if member.st:
                city_state_zip.append(member.st)
            if member.zip_code:
                city_state_zip.append(member.zip_code)

            if city_state_zip:
                address_parts.append(', '.join(city_state_zip))

            return '\n'.join(address_parts) if address_parts else ""

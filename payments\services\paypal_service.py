"""
PayPal service for handling PayPal API interactions.
Improves error handling, adds retry logic, and centralizes configuration.
"""
import json
import logging
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass

import requests
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from payments.config import PaymentConfig, ErrorMessages

logger = logging.getLogger(__name__)


@dataclass
class PayPalConfig:
    """PayPal configuration container"""
    client_id: str
    client_secret: str
    base_url: str
    is_sandbox: bool

    @classmethod
    def from_settings(cls) -> 'PayPalConfig':
        """Create config from Django settings and PaymentConfig"""
        try:
            # Check if we should use sandbox (default to True for development)
            use_sandbox = getattr(settings, 'PAYPAL_USE_SANDBOX', True)

            if use_sandbox:
                client_id = getattr(settings, 'PAYPAL_SANDBOX_CLIENT_ID', PaymentConfig.PAYPAL_SANDBOX_CLIENT_ID)
                client_secret = getattr(settings, 'PAYPAL_SANDBOX_CLIENT_SECRET', PaymentConfig.PAYPAL_SANDBOX_CLIENT_SECRET)
                base_url = PaymentConfig.PAYPAL_SANDBOX_BASE_URL
            else:
                client_id = getattr(settings, 'PAYPAL_PRODUCTION_CLIENT_ID', PaymentConfig.PAYPAL_PRODUCTION_CLIENT_ID)
                client_secret = getattr(settings, 'PAYPAL_PRODUCTION_CLIENT_SECRET', PaymentConfig.PAYPAL_PRODUCTION_CLIENT_SECRET)
                base_url = PaymentConfig.PAYPAL_PRODUCTION_BASE_URL

            if not client_id or not client_secret:
                raise ImproperlyConfigured("PayPal credentials not configured")

            if not use_sandbox and (client_id == "YOUR_PRODUCTION_CLIENT_ID_HERE" or client_secret == "YOUR_PRODUCTION_CLIENT_SECRET_HERE"):
                raise ImproperlyConfigured("Production PayPal credentials not properly configured")

            return cls(client_id=client_id, client_secret=client_secret, base_url=base_url, is_sandbox=use_sandbox)
        except Exception as e:
            raise ImproperlyConfigured(f"Missing PayPal configuration: {e}")


class PayPalAPIError(Exception):
    """Custom exception for PayPal API errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class PayPalService:
    """Service for PayPal API interactions with improved error handling and retry logic"""

    def __init__(self, config: Optional[PayPalConfig] = None):
        self.config = config or PayPalConfig.from_settings()
        self._access_token = None
        self._token_expires_at = 0

    @property
    def oauth_url(self) -> str:
        return f'{self.config.base_url}/v1/oauth2/token'

    @property
    def order_url(self) -> str:
        return f'{self.config.base_url}/v2/checkout/orders'

    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with proper error handling"""
        max_retries = PaymentConfig.PAYPAL_MAX_RETRIES
        retry_delay = PaymentConfig.PAYPAL_RETRY_DELAY_SECONDS

        for attempt in range(max_retries):
            try:
                response = requests.request(method, url, timeout=PaymentConfig.PAYPAL_TIMEOUT_SECONDS, **kwargs)

                # Check for HTTP errors
                if response.status_code >= 400:
                    error_data = None
                    try:
                        error_data = response.json()
                    except (ValueError, json.JSONDecodeError):
                        pass

                    error_msg = f"PayPal API error: {response.status_code}"
                    if error_data:
                        error_msg += f" - {error_data.get('message', 'Unknown error')}"

                    raise PayPalAPIError(error_msg, response.status_code, error_data)

                return response

            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:  # Last attempt
                    raise PayPalAPIError(f"PayPal API request failed: {str(e)}")

                logger.warning(f"PayPal API request failed (attempt {attempt + 1}): {str(e)}")
                time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff

        raise PayPalAPIError("Max retries exceeded")

    def get_access_token(self) -> str:
        """Get PayPal OAuth access token with caching"""
        current_time = time.time()

        # Return cached token if still valid (with buffer)
        if self._access_token and current_time < (self._token_expires_at - PaymentConfig.PAYPAL_TOKEN_BUFFER_SECONDS):
            return self._access_token

        try:
            response = self._make_request(
                'POST',
                self.oauth_url,
                auth=(self.config.client_id, self.config.client_secret),
                headers={'Accept': 'application/json', 'Accept-Language': 'en_US'},
                data={'grant_type': 'client_credentials'}
            )

            response_data = response.json()
            self._access_token = response_data.get('access_token')
            expires_in = response_data.get('expires_in', 3600)  # Default 1 hour
            self._token_expires_at = current_time + expires_in

            if not self._access_token:
                raise PayPalAPIError("No access token in response")

            return self._access_token

        except PayPalAPIError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error getting PayPal access token: {str(e)}")
            raise PayPalAPIError(f"Failed to get access token: {str(e)}")

    def create_order(self, amount: float, description: str, reference_id: str,
                    return_url: str, cancel_url: str) -> Dict[str, Any]:
        """Create PayPal order with improved error handling"""
        access_token = self.get_access_token()

        payload = {
            "intent": "CAPTURE",
            "purchase_units": [
                {
                    "reference_id": reference_id,
                    "description": description,
                    "amount": {
                        "currency_code": "USD",
                        "value": f"{amount:.2f}"  # Ensure proper formatting
                    }
                }
            ],
            "application_context": {
                "return_url": return_url,
                "cancel_url": cancel_url
            }
        }

        try:
            response = self._make_request(
                'POST',
                self.order_url,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {access_token}'
                },
                data=json.dumps(payload)
            )

            return response.json()

        except PayPalAPIError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating PayPal order: {str(e)}")
            raise PayPalAPIError(f"Failed to create order: {str(e)}")

    def capture_payment(self, order_id: str) -> Dict[str, Any]:
        """Capture an approved PayPal payment"""
        access_token = self.get_access_token()
        capture_url = f"{self.order_url}/{order_id}/capture"

        try:
            response = self._make_request(
                'POST',
                capture_url,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {access_token}'
                }
            )

            return response.json()

        except PayPalAPIError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error capturing PayPal payment: {str(e)}")
            raise PayPalAPIError(f"Failed to capture payment: {str(e)}")

from rest_framework import status
from rest_framework.generics import CreateAPIView
from django.db import transaction, IntegrityError

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Department
from core.serializers import DepartmentsSerializer


class DepartmentCreateAPIView(BaseAPIView, CreateAPIView):
    """
    Create a new department (admin only)
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentsSerializer
    permission_classes = [IsStaffUser]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            with transaction.atomic():
                # Check if a department with this name already exists
                name = serializer.validated_data.get('name')
                if Department.objects.filter(name=name).exists():
                    return APIResponse(
                        data=None,
                        message=f"Department with name '{name}' already exists",
                        status_code=status.HTTP_400_BAD_REQUEST,
                        success=False
                    )

                self.perform_create(serializer)

                return APIResponse(
                    data=serializer.data,
                    message="Department created successfully",
                    status_code=status.HTTP_201_CREATED
                )
        except IntegrityError:
            # This could happen in concurrent scenarios
            return APIResponse(
                data=None,
                message="Department with this name already exists",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False
            )

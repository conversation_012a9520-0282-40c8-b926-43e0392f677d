"""
Tests for the core app models.
"""
from django.test import TestCase
from django.db import IntegrityError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from django.core.exceptions import ValidationError

from core.models import Department, Event, EventRegistration

User = get_user_model()


class UserModelTests(TestCase):
    """Test cases for the User model."""

    def setUp(self):
        """Set up test data."""
        self.minimal_user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }
        self.maximal_user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'name': 'Max User',
            'title': 'Dr.',
            'address': '123 Main St',
            'city': 'Anytown',
            'st': 'MS',
            'zip_code': '12345',
            'home_phone': '555-1234',
            'business_phone': '555-5678',
            'membership_class': 'member',
            'role': 'volunteer',
            'gender': 'prefer_not_to_say'
        }

    def test_user_creation_minimal_fields(self):
        """Test user creation with minimal required fields."""
        user = User.objects.create_user(**self.minimal_user_data)
        self.assertEqual(user.email, self.minimal_user_data['email'])
        self.assertTrue(user.check_password(self.minimal_user_data['password']))
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_user_creation_maximal_fields(self):
        """Test user creation with all available fields."""
        user = User.objects.create_user(**self.maximal_user_data)
        for key, value in self.maximal_user_data.items():
            if key != 'password':
                self.assertEqual(getattr(user, key), value)
        self.assertTrue(user.check_password(self.maximal_user_data['password']))

    def test_email_uniqueness(self):
        """Test that email must be unique."""
        User.objects.create_user(**self.minimal_user_data)
        with self.assertRaises(IntegrityError):
            User.objects.create_user(**self.minimal_user_data)

    def test_password_hashing(self):
        """Test that passwords are properly hashed and not stored in plaintext."""
        user = User.objects.create_user(**self.minimal_user_data)
        self.assertNotEqual(user.password, self.minimal_user_data['password'])
        self.assertTrue(user.password.startswith('pbkdf2_sha256$'))

    def test_str_method(self):
        """Test the __str__ method returns the expected output."""
        user = User.objects.create_user(**self.minimal_user_data)
        self.assertEqual(str(user), self.minimal_user_data['email'])


class EventModelTests(TestCase):
    """Test cases for the Event model."""

    def setUp(self):
        """Set up test data."""
        self.event_data = {
            'event_name': 'Test Event',
            'event_date': timezone.now().date() + timedelta(days=10),
            'event_location': 'Test Location',
            'registration_fee_normal': 100.00,
            'registration_fee_late': 115.00,
            'guest_fee': 50.00,
            'max_participants': 100
        }

    def test_event_creation_required_fields(self):
        """Test event creation with required fields."""
        event = Event.objects.create(
            event_name=self.event_data['event_name'],
            event_date=self.event_data['event_date'],
            event_location=self.event_data['event_location']
        )
        self.assertEqual(event.event_name, self.event_data['event_name'])
        self.assertEqual(event.event_date, self.event_data['event_date'])
        self.assertEqual(event.event_location, self.event_data['event_location'])
        self.assertTrue(event.is_active)  # Default should be active

    def test_event_creation_all_fields(self):
        """Test event creation with all fields."""
        event = Event.objects.create(**self.event_data)
        for key, value in self.event_data.items():
            self.assertEqual(getattr(event, key), value)

    def test_event_date_validation(self):
        """Test validation that end date cannot be before start date."""
        event = Event(
            event_name='Date Test Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_end_date=timezone.now().date() + timedelta(days=5),  # End date before start date
            event_location='Test Location'
        )
        with self.assertRaises(ValidationError):
            event.full_clean()

    def test_active_events_filter(self):
        """Test that inactive events don't show in active queries."""
        # Create active event
        active_event = Event.objects.create(
            event_name='Active Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            is_active=True
        )

        # Create inactive event
        inactive_event = Event.objects.create(
            event_name='Inactive Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            is_active=False
        )

        # Query active events
        active_events = Event.objects.filter(is_active=True)

        self.assertIn(active_event, active_events)
        self.assertNotIn(inactive_event, active_events)


class DepartmentModelTests(TestCase):
    """Test cases for the Department model."""

    def setUp(self):
        """Set up test data."""
        self.department_data = {
            'name': 'Test Department',
            'department_address1': '123 Main St',
            'department_city': 'Anytown',
            'department_state': 'MS',
            'department_zip_code': '12345'
        }

        self.user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'name': 'Department Test User'
        }

    def test_department_creation(self):
        """Test basic creation of a department."""
        department = Department.objects.create(**self.department_data)
        self.assertEqual(department.name, self.department_data['name'])
        self.assertEqual(department.department_address1, self.department_data['department_address1'])
        self.assertEqual(department.department_city, self.department_data['department_city'])
        self.assertEqual(department.department_state, self.department_data['department_state'])
        self.assertEqual(department.department_zip_code, self.department_data['department_zip_code'])

    def test_department_unique_name(self):
        """Test that department names must be unique."""
        Department.objects.create(name=self.department_data['name'])
        with self.assertRaises(IntegrityError):
            Department.objects.create(name=self.department_data['name'])

    def test_department_member_relationship(self):
        """Test the relationship between departments and members."""
        department = Department.objects.create(**self.department_data)

        # Create a user and assign to department
        user = User.objects.create_user(**self.user_data)
        user.department = department
        user.save()

        # Refresh from database
        department = Department.objects.get(id=department.id)
        user = User.objects.get(id=user.id)

        # Test relationship from both sides
        self.assertEqual(user.department, department)
        self.assertIn(user, department.members.all())


class EventRegistrationModelTests(TestCase):
    """Test cases for the EventRegistration model."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Registration Test User'
        )

        # Create an event
        self.event = Event.objects.create(
            event_name='Registration Test Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location'
        )

        # Basic registration data
        self.registration_data = {
            'event': self.event,
            'member': self.user,
            'first_name': 'Registration',
            'last_name': 'Test',
            'title': 'Mr.',
            'fire_department': 'Test Fire Department',
            'address': '123 Main St',
            'city': 'Anytown',
            'state': 'MS',
            'zipcode': '12345',
            'phone': '555-1234',
            'email': '<EMAIL>',
            'registration_type': 'NORMAL',
            'base_amount': 100.00,
            'guest_amount': 0.00,
            'total_amount': 100.00
        }

    def test_registration_creation(self):
        """Test basic creation of an event registration."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertEqual(registration.event, self.event)
        self.assertEqual(registration.member, self.user)
        self.assertEqual(registration.first_name, self.registration_data['first_name'])
        self.assertEqual(registration.last_name, self.registration_data['last_name'])
        self.assertEqual(registration.payment_status, 'PENDING')  # Default status

    def test_registration_uniqueness(self):
        """Test the unique constraint for member and event combination."""
        # Create the first registration
        EventRegistration.objects.create(**self.registration_data)
        # Attempt to create a duplicate registration
        with self.assertRaises(IntegrityError):
            EventRegistration.objects.create(**self.registration_data)

    def test_registration_status_updates(self):
        """Test updating the status of a registration."""
        registration = EventRegistration.objects.create(**self.registration_data)

        # Test updating payment status
        registration.payment_status = 'COMPLETED'
        registration.save()

        # Refresh from database
        registration = EventRegistration.objects.get(id=registration.id)
        self.assertEqual(registration.payment_status, 'COMPLETED')

        # Test other status updates
        registration.payment_status = 'FAILED'
        registration.save()

        # Refresh from database
        registration = EventRegistration.objects.get(id=registration.id)
        self.assertEqual(registration.payment_status, 'FAILED')


class HistoricalModelTests(TestCase):
    """Test cases for historical models."""

    def setUp(self):
        """Set up test data."""
        # Create test objects
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='History Test User'
        )

        self.department = Department.objects.create(
            name='History Test Department'
        )

        self.event = Event.objects.create(
            event_name='History Test Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location'
        )

    def test_user_history_on_update(self):
        """Test that a historical record is created when a user is updated."""
        # Initial count of historical records
        initial_count = self.user.history.count()

        # Update the user
        self.user.name = 'Updated Name'
        self.user.save()

        # Check that a new historical record was created
        self.assertEqual(self.user.history.count(), initial_count + 1)

        # Check that the historical record has the new value
        self.assertEqual(self.user.history.first().name, 'Updated Name')

    def test_department_history_on_update(self):
        """Test that a historical record is created when a department is updated."""
        # Initial count of historical records
        initial_count = self.department.history.count()

        # Update the department
        self.department.name = 'Updated Department'
        self.department.save()

        # Check that a new historical record was created
        self.assertEqual(self.department.history.count(), initial_count + 1)

        # Check that the historical record has the new value
        self.assertEqual(self.department.history.first().name, 'Updated Department')

    def test_event_history_on_update(self):
        """Test that a historical record is created when an event is updated."""
        # Initial count of historical records
        initial_count = self.event.history.count()

        # Update the event
        self.event.event_name = 'Updated Event'
        self.event.save()

        # Check that a new historical record was created
        self.assertEqual(self.event.history.count(), initial_count + 1)

        # Check that the historical record has the new value
        self.assertEqual(self.event.history.first().event_name, 'Updated Event')

    def test_history_on_delete(self):
        """Test that historical records are preserved when an object is deleted."""
        # Create a temporary object
        temp_dept = Department.objects.create(name='Temp Department')

        # Get the ID for later reference
        dept_id = temp_dept.id

        # Delete the object
        temp_dept.delete()

        # Check that the historical record still exists
        historical_records = Department.history.filter(id=dept_id)
        self.assertTrue(historical_records.exists())

        # Check that the most recent historical record has a deletion flag
        self.assertIsNotNone(historical_records.first().history_date)

from django.core.cache import cache
from django.db import transaction
from django.http import Http404
from django.shortcuts import get_object_or_404
from rest_framework import status

from common.views import BaseAPIView
from common.views import APIResponse
from core.models import Member
from common.permissions import IsStaffUser
from payments.models import Payment


class MembershipRosterDeleteAdminView(BaseAPIView):
    """
    Delete a member and associated user (admin only)
    """
    permission_classes = [IsStaffUser]

    @transaction.atomic
    def delete(self, request, pk):
        try:
            # Prevent users from deleting themselves
            if request.user.id == int(pk):
                return APIResponse(
                    data=None,
                    message="You cannot delete your own account",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            # Select related to avoid additional query for user
            member = get_object_or_404(
                Member.objects.select_related('department'),
                pk=pk
            )
            
            # Bulk delete payments in one query
            Payment.objects.filter(covered_members=member).delete()
            
            # Delete member (will cascade delete related objects)
            member.delete()
            
            # Clear cache after deletion
            cache.delete('membership_roster_queryset')
            
            return APIResponse(
                data=None,
                message="Member deleted successfully",
                status_code=status.HTTP_204_NO_CONTENT
            )

        except Http404:

            return APIResponse(
                data=None,
                message="Member not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return APIResponse(
                data=None,
                message="Error deleting member",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
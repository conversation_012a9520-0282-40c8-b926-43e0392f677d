"""
Tests for the password reset token verification functionality in the common app.

This module contains comprehensive tests for the verify_password_reset_token function.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from common.utils.email import verify_password_reset_token
from common.models import PasswordReset

User = get_user_model()


class VerifyPasswordResetTokenTests(TestCase):
    """Test cases for the verify_password_reset_token function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_verify_password_reset_token_success(self):
        """Test successful password reset token verification."""
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
        
        # Check that the reset was NOT marked as used (that happens later)
        self.reset.refresh_from_db()
        self.assertFalse(self.reset.used)

    def test_verify_password_reset_token_invalid_key(self):
        """Test password reset token verification with invalid key."""
        # Call the function with a non-existent key
        user, error = verify_password_reset_token(str(uuid.uuid4()))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_already_used(self):
        """Test password reset token verification with already used key."""
        # Mark the reset as used
        self.reset.used = True
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "This password reset link has already been used")

    def test_verify_password_reset_token_expired(self):
        """Test password reset token verification with expired key."""
        # Set the reset to be expired
        self.reset.expires_at = timezone.now() - timedelta(hours=1)
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Password reset link has expired")

    def test_verify_password_reset_token_with_invalid_uuid_format(self):
        """Test password reset token verification with an invalid UUID format."""
        # Call the function with an invalid UUID
        user, error = verify_password_reset_token("not-a-uuid")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_with_none_key(self):
        """Test password reset token verification with a None key."""
        # Call the function with None
        user, error = verify_password_reset_token(None)
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_with_empty_key(self):
        """Test password reset token verification with an empty key."""
        # Call the function with an empty string
        user, error = verify_password_reset_token("")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_with_deleted_user(self):
        """Test password reset token verification when the user has been deleted."""
        # Create a reset for a user that will be deleted
        temp_user = User.objects.create_user(
            email='<EMAIL>',
            password='temppassword123',
            name='Temp User'
        )
        
        temp_reset = PasswordReset.objects.create(
            user=temp_user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Delete the user
        temp_user_id = temp_user.id
        temp_reset_key = temp_reset.key
        temp_user.delete()
        
        # Call the function
        user, error = verify_password_reset_token(str(temp_reset_key))
        
        # Check the result - should fail because the user is gone
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")
    
    def test_verify_password_reset_token_with_multiple_resets(self):
        """Test password reset token verification when a user has multiple reset records."""
        # Create a second reset for the same user
        second_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Verify the first reset
        user1, error1 = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertEqual(user1, self.user)
        self.assertIsNone(error1)
        
        # Verify the second reset
        user2, error2 = verify_password_reset_token(str(second_reset.key))
        
        # Check the result
        self.assertEqual(user2, self.user)
        self.assertIsNone(error2)
        
        # Check that neither reset is marked as used
        self.reset.refresh_from_db()
        second_reset.refresh_from_db()
        self.assertFalse(self.reset.used)
        self.assertFalse(second_reset.used)
    
    def test_verify_password_reset_token_just_before_expiry(self):
        """Test password reset token verification just before it expires."""
        # Set the reset to expire very soon
        almost_expired_time = timezone.now() + timedelta(seconds=10)
        self.reset.expires_at = almost_expired_time
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result - should still be valid
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
    
    def test_verify_password_reset_token_just_after_expiry(self):
        """Test password reset token verification just after it expires."""
        # Set the reset to have expired very recently
        just_expired_time = timezone.now() - timedelta(seconds=10)
        self.reset.expires_at = just_expired_time
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result - should be expired
        self.assertIsNone(user)
        self.assertEqual(error, "Password reset link has expired")

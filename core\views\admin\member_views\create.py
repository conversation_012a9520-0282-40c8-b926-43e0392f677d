from rest_framework import status
from rest_framework.generics import CreateAPIView

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Member
from core.serializers import MembershipRosterAdminSerializer


class MembershipRosterCreateAdminView(BaseAPIView, CreateAPIView):
    """
    Create a new member (admin only)
    """
    queryset = Member.objects.all()
    serializer_class = MembershipRosterAdminSerializer
    permission_classes = [IsStaffUser]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        
        return APIResponse(
            data=serializer.data,
            message="Member created successfully",
            status_code=status.HTTP_201_CREATED,

        ) 
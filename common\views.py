from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.exceptions import ErrorDetail, ValidationError
from rest_framework import status, permissions

from .utils.email import verify_email
from .utils import track_activity


class APIResponse(Response):
    """Custom Response with standardized format: message, success, and data fields"""
    def __init__(self, message=None, data=None, error=None, status_code=status.HTTP_200_OK, use_default_message=False):
        success = status_code < 400

        # Handle message formatting
        if not isinstance(message, str) and message is not None:
            # Convert non-string messages to strings
            message = str(message)

        # Only use default message if explicitly requested
        if use_default_message and (message is None or message == "") and success:
            message = "Operation completed successfully"

        response_data = {
            "message": message,
            "success": success,
            "data": data,
        }

        # Add error information if provided or if it's an error response
        if error:
            # Handle ValidationError objects
            if isinstance(error, ValidationError):
                # Convert ValidationError to a serializable format
                if hasattr(error, 'detail'):
                    response_data["error"] = self._format_validation_error(error.detail)
                else:
                    response_data["error"] = str(error)
            else:
                response_data["error"] = error

        super().__init__(data=response_data, status=status_code)

    def _format_validation_error(self, error_detail):
        """Format ValidationError detail to make it JSON serializable"""
        if isinstance(error_detail, dict):
            result = {}
            for field, errors in error_detail.items():
                result[field] = self._format_validation_error(errors)
            return result
        elif isinstance(error_detail, list):
            return [self._format_validation_error(item) for item in error_detail]
        else:
            # Convert any other type to string
            return str(error_detail)


class BaseAPIView(APIView):
    """
    Base API View class that standardizes all responses with the APIResponse format.
    All API views should inherit from this class to ensure consistent response format.
    """

    def success_response(self, data=None, message="Operation completed successfully", status=status.HTTP_200_OK):
        """
        Return a standardized success response
        """
        return APIResponse(
            message=message,
            data=data,
            status_code=status,
            use_default_message=True
        )

    def error_response(self, message="An error occurred", data=None, status=status.HTTP_400_BAD_REQUEST):
        """
        Return a standardized error response
        If message is a dictionary or list (like serializer.errors), it will be formatted as a string
        """
        # If message is a dictionary or list, format it as a string
        if isinstance(message, (dict, list)):
            message = self._format_error_message(message)

        return APIResponse(
            message=message,
            data=data,
            status_code=status
        )

    def finalize_response(self, request, response, *args, **kwargs):
        """
        Ensures all responses follow the standardized format with message, success, and data fields.
        If the response is already an APIResponse, it is returned as is.
        """
        # If response is already an APIResponse instance, return it
        if isinstance(response, APIResponse):
            return super().finalize_response(request, response, *args, **kwargs)

        # If response is a regular DRF Response, convert it to APIResponse
        if hasattr(response, 'data'):
            # Create appropriate response based on status code
            if response.status_code >= 400:
                api_response = APIResponse(
                    message=self._format_error_message(response.data),
                    data=None,
                    status_code=response.status_code
                )
            else:
                # Use default success message if no message is provided
                message = getattr(response, 'message', 'Operation completed successfully')
                api_response = APIResponse(
                    message=message,
                    data=response.data,
                    status_code=response.status_code,
                    use_default_message=True
                )

            # Copy headers from original response
            for key, value in response.items():
                api_response[key] = value

            response = api_response

        return super().finalize_response(request, response, *args, **kwargs)

    def _format_error_message(self, error_data):
        """
        Format error messages in a readable way, handling various types of DRF error responses.
        """
        if isinstance(error_data, str):
            return error_data

        if isinstance(error_data, dict):
            # Handle dictionary of errors
            messages = []
            for field, errors in error_data.items():
                # Special handling for non_field_errors - don't include the field name
                if field == 'non_field_errors':
                    if isinstance(errors, list):
                        # Collect all errors without the field name prefix
                        field_errors = []
                        for error in errors:
                            if isinstance(error, (str, int, bool)):
                                field_errors.append(str(error))
                            elif hasattr(error, 'detail'):
                                field_errors.append(error.detail)
                            else:
                                field_errors.append(str(error))

                        # Join all errors with commas
                        if field_errors:
                            messages.extend(field_errors)  # Add directly to messages without field prefix
                    elif isinstance(errors, str):
                        messages.append(errors)  # Add directly without field prefix
                    elif hasattr(errors, 'detail'):
                        messages.append(errors.detail)  # Add directly without field prefix
                    elif errors is not None:
                        messages.append(str(errors))  # Add directly without field prefix
                else:
                    # Normal field errors with field name prefix
                    if isinstance(errors, list):
                        # Collect all errors for the field
                        field_errors = []
                        for error in errors:
                            if isinstance(error, (str, int, bool)):
                                field_errors.append(str(error))
                            elif hasattr(error, 'detail'):
                                field_errors.append(error.detail)
                            else:
                                field_errors.append(str(error))

                        # Join all errors for this field with commas
                        if field_errors:
                            messages.append(f"{field}: {', '.join(field_errors)}")
                    elif isinstance(errors, dict):
                        # Handle nested dictionaries
                        nested_message = self._format_error_message(errors)
                        messages.append(f"{field}: {nested_message}")
                    elif isinstance(errors, str):
                        messages.append(f"{field}: {errors}")
                    elif hasattr(errors, 'detail'):
                        messages.append(f"{field}: {errors.detail}")
                    elif errors is None:
                        messages.append(f"{field}: None")
                    else:
                        # Convert any other type to string
                        messages.append(f"{field}: {str(errors)}")

            if messages:
                return ". ".join(messages)

        elif isinstance(error_data, list):
            # Handle list of errors
            formatted_errors = []
            for error in error_data:
                if isinstance(error, (str, int, bool)):
                    formatted_errors.append(str(error))
                elif hasattr(error, 'detail'):
                    formatted_errors.append(error.detail)
                elif isinstance(error, dict):
                    formatted_errors.append(self._format_error_message(error))
                else:
                    formatted_errors.append(str(error))

            return ", ".join(formatted_errors)

        # Fallback for other error types
        return "An error occurred"


class EmailVerificationView(BaseAPIView):
    """
    View for verifying email addresses
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User verified their email")
    def get(self, request, *args, **kwargs):
        token = request.query_params.get('token')
        user, error = verify_email(token)

        if error:
            return APIResponse(
                message=error,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return APIResponse(
            message="Email successfully verified. Your account is now pending administrator approval before you can log in.",
            data={"email": user.email},
            status_code=status.HTTP_200_OK
        )

    @track_activity(description="User verified their email")
    def post(self, request, *args, **kwargs):
        """Handle POST requests for email verification (for tests)"""
        key = request.data.get('key')
        if not key:
            return APIResponse(
                message="Verification key is required",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        user, error = verify_email(key)

        if error:
            return APIResponse(
                message=error,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return APIResponse(
            message="Email verified successfully",
            success=True,
            data={"email": user.email},
            status_code=status.HTTP_200_OK
        )


class ResendVerificationEmailView(BaseAPIView):
    """
    View for resending verification emails
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User requested verification email resend")
    def post(self, request):
        """
        Resend a verification email to a user
        """
        email = request.data.get('email')

        if not email:
            return APIResponse(
                message="Email address is required",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            user = User.objects.get(email=email)

            # Check if user is already active
            if user.active:
                return APIResponse(
                    message="This account has already been verified",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Send verification email
            from .utils.email import send_verification_email
            send_verification_email(user)

            return APIResponse(
                message="Verification email has been sent",
                status_code=status.HTTP_200_OK
            )

        except User.DoesNotExist:
            # For security reasons, don't reveal that the email doesn't exist
            return APIResponse(
                message="If the email exists in our system, a verification email has been sent",
                status_code=status.HTTP_200_OK
            )


class RequestPasswordResetView(BaseAPIView):
    """
    View for requesting a password reset
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User requested password reset")
    def post(self, request):
        """
        Send a password reset email to a user
        """
        from core.serializers.auth.password_reset import RequestPasswordResetSerializer

        serializer = RequestPasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()

            return APIResponse(
                message="If your email address exists in our system, you will receive password reset instructions shortly",
                status_code=status.HTTP_200_OK
            )

        return APIResponse(
            message="Invalid request",
            status_code=status.HTTP_400_BAD_REQUEST
        )


class ValidatePasswordResetTokenView(BaseAPIView):
    """
    View for validating a password reset token
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User validated password reset token")
    def post(self, request):
        """
        Validate a password reset token
        """
        from core.serializers.auth.password_reset import ValidateResetTokenSerializer

        serializer = ValidateResetTokenSerializer(data=request.data)
        if serializer.is_valid():
            return APIResponse(
                message="Token is valid",
                status_code=status.HTTP_200_OK
            )

        return APIResponse(
            message="Invalid token",
            status_code=status.HTTP_400_BAD_REQUEST
        )


class PasswordResetConfirmView(BaseAPIView):
    """
    View for confirming a password reset
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User reset their password")
    def post(self, request):
        """
        Reset a user's password using a reset token
        """
        from core.serializers.auth.password_reset import PasswordResetConfirmSerializer

        # Check if passwords match
        password = request.data.get('password')
        password_confirm = request.data.get('password_confirm')

        if password != password_confirm:
            return APIResponse(
                message="Passwords do not match",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            return APIResponse(
                message="Password has been reset successfully. You can now log in with your new password.",
                data={"email": user.email},
                status_code=status.HTTP_200_OK
            )

        # Check if the token is invalid
        if 'key' in serializer.errors:
            return APIResponse(
                message="Invalid token",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return APIResponse(
            message="Password reset failed",
            status_code=status.HTTP_400_BAD_REQUEST
        )

# Create your views here.

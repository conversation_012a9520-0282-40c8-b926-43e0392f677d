"""
Tests for detailed calculations and relationships between EventRegistration, Event, and EventConfig.
This test file focuses on fee calculations, late registration logic, guest fees, and capacity tracking.
"""
from django.test import TestCase
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta, date
from decimal import Decimal
import json

from core.models.event_config import EventConfig
from core.models.event import Event
from core.models import EventRegistration, Member
from django.contrib.auth import get_user_model

User = get_user_model()


class EventRegistrationCalculationsTests(TestCase):
    """Tests for event registration fee calculations."""

    def setUp(self):
        """Set up test data for each test."""
        # Dates for testing
        self.today = timezone.now().date()
        self.tomorrow = self.today + timedelta(days=1)
        self.next_week = self.today + timedelta(days=7)
        self.yesterday = self.today - timedelta(days=1)
        self.last_week = self.today - timedelta(days=7)

        # Create user for registrations
        self.user = Member.objects.create(
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
        )

        # Create a second user for group registrations
        self.group_member = Member.objects.create(
            first_name="Group",
            last_name="Member",
            email="<EMAIL>",
        )

        # Create default event config
        self.config = EventConfig.objects.create(
            name="Test Config",
            description="Configuration for tests",
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=True
        )

        # Create test event
        self.event = Event.objects.create(
            event_name='Test Event',
            event_date=self.next_week,
            event_location='Test Location',
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            late_registration_date=self.next_week - timedelta(days=3),
            max_participants=100,
            is_active=True,
            config=self.config
        )

        # Create custom fee event
        self.custom_fee_event = Event.objects.create(
            event_name='Custom Fee Event',
            event_date=self.next_week,
            event_location='Custom Location',
            registration_fee_normal=Decimal('75.00'),  # Custom normal fee
            registration_fee_late=Decimal('90.00'),    # Custom late fee
            guest_fee=Decimal('40.00'),                # Custom guest fee
            late_registration_date=self.next_week - timedelta(days=3),
            max_participants=100,
            is_active=True
        )

        # Base registration data
        self.registration_data = {
            'event': self.event,
            'member': self.user,
            'first_name': 'Test',
            'last_name': 'User',
            'title': 'Mr.',
            'fire_department': 'Test Department',
            'address': '123 Test St',
            'city': 'Test City',
            'state': 'TS',
            'zipcode': '12345',
            'phone': '555-1234',
            'email': '<EMAIL>',
            'registration_type': 'NORMAL',
            'number_of_participants': 1,
            'number_of_guests': 0
        }

        # Extra participant data
        self.extra_participants = {
            'participants': [
                {
                    'name': 'Extra Person 1',
                    'title': 'Chief',
                    'department': 'Extra Department 1'
                },
                {
                    'name': 'Extra Person 2',
                    'title': 'Deputy',
                    'department': 'Extra Department 2'
                }
            ]
        }

    def test_normal_registration_fee_calculation(self):
        """Test calculation of normal registration fees."""
        # Create registration during normal registration period
        self.event.late_registration_date = self.tomorrow  # Not in late period yet
        self.event.save()

        registration = EventRegistration.objects.create(
            **self.registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations
        self.assertEqual(registration.base_amount, Decimal('100.00'))  # 1 participant * $100
        self.assertEqual(registration.guest_amount, Decimal('0.00'))   # 0 guests
        self.assertEqual(registration.total_amount, Decimal('100.00'))  # $100 total
        self.assertEqual(total, Decimal('100.00'))  # Function return value

    def test_late_registration_fee_calculation(self):
        """Test calculation of late registration fees."""
        # Create registration during late registration period
        self.event.late_registration_date = self.yesterday  # In late period
        self.event.save()

        registration = EventRegistration.objects.create(
            **self.registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations use the late fee
        self.assertEqual(registration.base_amount, Decimal('115.00'))  # 1 participant * $115
        self.assertEqual(registration.guest_amount, Decimal('0.00'))   # 0 guests
        self.assertEqual(registration.total_amount, Decimal('115.00'))  # $115 total
        self.assertEqual(total, Decimal('115.00'))  # Function return value

    def test_guest_fee_calculation(self):
        """Test calculation of guest fees."""
        # Create registration with guests
        registration_data = self.registration_data.copy()
        registration_data['number_of_guests'] = 2

        # Not in late period
        self.event.late_registration_date = self.tomorrow
        self.event.save()

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations
        self.assertEqual(registration.base_amount, Decimal('100.00'))  # 1 participant * $100
        self.assertEqual(registration.guest_amount, Decimal('100.00'))   # 2 guests * $50
        self.assertEqual(registration.total_amount, Decimal('200.00'))  # $200 total
        self.assertEqual(total, Decimal('200.00'))  # Function return value

    def test_custom_fee_event_calculation(self):
        """Test calculation using custom event fees."""
        # Create registration for custom fee event
        registration_data = self.registration_data.copy()
        registration_data['event'] = self.custom_fee_event
        registration_data['number_of_participants'] = 1
        registration_data['number_of_guests'] = 1

        # Not in late period
        self.custom_fee_event.late_registration_date = self.tomorrow
        self.custom_fee_event.save()

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations use the custom fee
        self.assertEqual(registration.base_amount, Decimal('75.00'))  # 1 participant * $75
        self.assertEqual(registration.guest_amount, Decimal('40.00'))  # 1 guest * $40
        self.assertEqual(registration.total_amount, Decimal('115.00'))  # $115 total
        self.assertEqual(total, Decimal('115.00'))  # Function return value

    def test_multiple_participants_calculation(self):
        """Test calculation with multiple participants."""
        # Create registration with multiple participants
        registration_data = self.registration_data.copy()
        registration_data['number_of_participants'] = 3
        registration_data['number_of_guests'] = 2

        # Not in late period
        self.event.late_registration_date = self.tomorrow
        self.event.save()

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations
        self.assertEqual(registration.base_amount, Decimal('300.00'))  # 3 participants * $100
        self.assertEqual(registration.guest_amount, Decimal('100.00'))  # 2 guests * $50
        self.assertEqual(registration.total_amount, Decimal('400.00'))  # $400 total
        self.assertEqual(total, Decimal('400.00'))  # Function return value

    def test_complex_registration_calculation(self):
        """Test calculation for complex registration scenario."""
        # Create a complex registration during late period with multiple participants and guests
        registration_data = self.registration_data.copy()
        registration_data['number_of_participants'] = 5
        registration_data['number_of_guests'] = 3
        registration_data['extra_participants'] = self.extra_participants

        # In late period
        self.event.late_registration_date = self.yesterday
        self.event.save()

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations
        self.assertEqual(registration.base_amount, Decimal('575.00'))  # 5 participants * $115 (late fee)
        self.assertEqual(registration.guest_amount, Decimal('150.00'))  # 3 guests * $50
        self.assertEqual(registration.total_amount, Decimal('725.00'))  # $725 total
        self.assertEqual(total, Decimal('725.00'))  # Function return value

    def test_automatic_late_registration_detection(self):
        """Test automatic detection of late registration based on date."""
        # Create registration with event that's in late registration period
        self.event.late_registration_date = self.yesterday  # In late period
        self.event.save()

        registration = EventRegistration.objects.create(
            **self.registration_data
        )

        # Registration should be set to late automatically based on date
        self.assertTrue(self.event.is_late_registration)
        self.assertEqual(registration.registration_type, 'NORMAL')  # Registration type doesn't change

        # But the calculation should use late fees
        total = registration.calculate_total_amount()
        self.assertEqual(registration.base_amount, Decimal('115.00'))  # Late fee
        self.assertEqual(registration.total_amount, Decimal('115.00'))

    def test_registration_before_late_date(self):
        """Test registration before late registration date."""
        # Set late registration date to tomorrow
        self.event.late_registration_date = self.tomorrow
        self.event.save()

        # Verify we're not in late registration period
        self.assertFalse(self.event.is_late_registration)

        # Create registration
        registration = EventRegistration.objects.create(
            **self.registration_data
        )

        # Should use normal fee
        total = registration.calculate_total_amount()
        self.assertEqual(registration.base_amount, Decimal('100.00'))
        self.assertEqual(registration.total_amount, Decimal('100.00'))

    def test_registration_after_late_date(self):
        """Test registration after late registration date."""
        # Set late registration date to yesterday
        self.event.late_registration_date = self.yesterday
        self.event.save()

        # Verify we're in late registration period
        self.assertTrue(self.event.is_late_registration)

        # Create registration
        registration = EventRegistration.objects.create(
            **self.registration_data
        )

        # Should use late fee
        total = registration.calculate_total_amount()
        self.assertEqual(registration.base_amount, Decimal('115.00'))
        self.assertEqual(registration.total_amount, Decimal('115.00'))

    def test_capacity_tracking(self):
        """Test tracking of event capacity and spots remaining."""
        # Create a limited capacity event
        limited_event = Event.objects.create(
            event_name='Limited Event',
            event_date=self.next_week,
            event_location='Limited Location',
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            max_participants=3,  # Only 3 spots available
            is_active=True
        )

        # Register first participant with 1 person
        registration_data = self.registration_data.copy()
        registration_data['event'] = limited_event
        registration_data['email'] = '<EMAIL>'
        # Create a unique member for this registration
        member1 = Member.objects.create(
            first_name="Member1",
            last_name="Test",
            email="<EMAIL>",
        )
        registration_data['member'] = member1
        EventRegistration.objects.create(**registration_data)

        # Refresh event and check capacity
        limited_event.refresh_from_db()
        self.assertEqual(limited_event.total_registrations, 1)
        self.assertEqual(limited_event.spots_remaining, 2)
        self.assertFalse(limited_event.is_at_capacity)

        # Register second participant with 2 people
        registration_data['email'] = '<EMAIL>'
        registration_data['number_of_participants'] = 2
        # Create a unique member for this registration
        member2 = Member.objects.create(
            first_name="Member2",
            last_name="Test",
            email="<EMAIL>",
        )
        registration_data['member'] = member2
        EventRegistration.objects.create(**registration_data)

        # Refresh event and check capacity
        limited_event.refresh_from_db()
        self.assertEqual(limited_event.total_registrations, 2)  # Two registrations
        self.assertEqual(limited_event.spots_remaining, 1)
        self.assertFalse(limited_event.is_at_capacity)

        # Register third participant
        registration_data['email'] = '<EMAIL>'
        registration_data['number_of_participants'] = 1
        # Create a unique member for this registration
        member3 = Member.objects.create(
            first_name="Member3",
            last_name="Test",
            email="<EMAIL>",
        )
        registration_data['member'] = member3
        EventRegistration.objects.create(**registration_data)

        # Refresh event and check capacity
        limited_event.refresh_from_db()
        self.assertEqual(limited_event.total_registrations, 3)
        self.assertEqual(limited_event.spots_remaining, 0)
        self.assertTrue(limited_event.is_at_capacity)

    def test_group_registration_calculation(self):
        """Test calculation for group registrations."""
        # Create a group registration
        registration_data = self.registration_data.copy()
        registration_data['group_registration'] = True
        registration_data['number_of_participants'] = 3  # Main registrant plus two others
        registration_data['number_of_guests'] = 1

        # Not in late period
        self.event.late_registration_date = self.tomorrow
        self.event.save()

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Add group members after creation
        registration.group_members.add(self.group_member)
        registration.save()

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations
        self.assertEqual(registration.base_amount, Decimal('300.00'))  # 3 participants * $100
        self.assertEqual(registration.guest_amount, Decimal('50.00'))  # 1 guest * $50
        self.assertEqual(registration.total_amount, Decimal('350.00'))  # $350 total
        self.assertEqual(total, Decimal('350.00'))  # Function return value

        # Verify group members
        self.assertEqual(registration.group_members.count(), 1)
        self.assertIn(self.group_member, registration.group_members.all())

    def test_extra_participants_data(self):
        """Test registrations with extra participants data."""
        # Create registration with extra participants data
        registration_data = self.registration_data.copy()
        registration_data['number_of_participants'] = 3  # Main registrant plus two others
        registration_data['extra_participants'] = self.extra_participants

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Verify extra participants data stored correctly
        self.assertEqual(registration.extra_participants, self.extra_participants)
        self.assertEqual(len(registration.extra_participants['participants']), 2)

        # Verify first extra participant
        self.assertEqual(registration.extra_participants['participants'][0]['name'], 'Extra Person 1')
        self.assertEqual(registration.extra_participants['participants'][0]['title'], 'Chief')

        # Calculate and verify total
        total = registration.calculate_total_amount()
        self.assertEqual(registration.base_amount, Decimal('300.00'))  # 3 participants * $100
        self.assertEqual(registration.total_amount, Decimal('300.00'))  # $300 total

    def test_config_based_calculations(self):
        """Test calculations using event config values."""
        # Create a new configuration with different fees
        new_config = EventConfig.objects.create(
            name="New Config",
            description="New test configuration",
            registration_fee_normal=Decimal('150.00'),
            registration_fee_late=Decimal('175.00'),
            guest_fee=Decimal('60.00'),
            default_max_participants=50,
            days_until_late_registration=5,
            is_active=False  # Not the active config
        )

        # Create event using the new config
        config_event = Event.objects.create(
            event_name='Config Event',
            event_date=self.next_week,
            event_location='Config Location',
            config=new_config,
            # Don't set fees directly - should use config
        )

        # Verify event has fees from config
        self.assertEqual(config_event.registration_fee_normal, new_config.registration_fee_normal)
        self.assertEqual(config_event.registration_fee_late, new_config.registration_fee_late)
        self.assertEqual(config_event.guest_fee, new_config.guest_fee)

        # Create registration for this event
        registration_data = self.registration_data.copy()
        registration_data['event'] = config_event
        registration_data['number_of_participants'] = 1
        registration_data['number_of_guests'] = 1

        # Not in late period
        config_event.late_registration_date = self.tomorrow
        config_event.save()

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations use config fees
        self.assertEqual(registration.base_amount, Decimal('150.00'))  # 1 participant * $150
        self.assertEqual(registration.guest_amount, Decimal('60.00'))  # 1 guest * $60
        self.assertEqual(registration.total_amount, Decimal('210.00'))  # $210 total
        self.assertEqual(total, Decimal('210.00'))  # Function return value

    def test_zero_fee_event(self):
        """Test calculations for an event with zero fees."""
        # Create event with zero fees
        free_event = Event.objects.create(
            event_name='Free Event',
            event_date=self.next_week,
            event_location='Free Location',
            registration_fee_normal=Decimal('0.00'),
            registration_fee_late=Decimal('0.00'),
            guest_fee=Decimal('0.00'),
            is_active=True
        )

        # Create registration for free event
        registration_data = self.registration_data.copy()
        registration_data['event'] = free_event
        registration_data['number_of_participants'] = 2
        registration_data['number_of_guests'] = 3

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify all amounts are zero
        self.assertEqual(registration.base_amount, Decimal('0.00'))
        self.assertEqual(registration.guest_amount, Decimal('0.00'))
        self.assertEqual(registration.total_amount, Decimal('0.00'))
        self.assertEqual(total, Decimal('0.00'))

    def test_registration_status_updates(self):
        """Test registration status updates and their effect on calculations."""
        # Create registration
        registration = EventRegistration.objects.create(
            **self.registration_data
        )

        # Calculate initial total
        initial_total = registration.calculate_total_amount()
        self.assertEqual(initial_total, Decimal('100.00'))

        # Update status to COMPLETED
        registration.payment_status = 'COMPLETED'
        registration.save()

        # Verify status was updated
        self.assertEqual(registration.payment_status, 'COMPLETED')

        # Calculate total again - should be unchanged
        updated_total = registration.calculate_total_amount()
        self.assertEqual(updated_total, Decimal('100.00'))

        # Update status to FAILED
        registration.payment_status = 'FAILED'
        registration.save()

        # Verify status was updated
        self.assertEqual(registration.payment_status, 'FAILED')

        # Calculate total again - should be unchanged
        final_total = registration.calculate_total_amount()
        self.assertEqual(final_total, Decimal('100.00'))

    def test_event_config_relationship(self):
        """Test the relationships between Event, EventConfig and event fees."""
        # Create a new config
        new_config = EventConfig.objects.create(
            name="Default Test Config",
            description="Default test configuration",
            registration_fee_normal=Decimal('125.00'),
            registration_fee_late=Decimal('150.00'),
            guest_fee=Decimal('55.00'),
            default_max_participants=75,
            days_until_late_registration=5,
            is_active=True  # Make this the active config
        )

        # Create event without specifying fees or config
        default_event = Event.objects.create(
            event_name='Default Config Event',
            event_date=self.next_week,
            event_location='Default Config Location'
        )

        # Verify event takes values from active config
        self.assertEqual(default_event.registration_fee_normal, new_config.registration_fee_normal)
        self.assertEqual(default_event.registration_fee_late, new_config.registration_fee_late)
        self.assertEqual(default_event.guest_fee, new_config.guest_fee)
        self.assertEqual(default_event.max_participants, new_config.default_max_participants)

        # Create registration for this event
        registration_data = self.registration_data.copy()
        registration_data['event'] = default_event
        registration_data['number_of_participants'] = 1

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees
        total = registration.calculate_total_amount()

        # Verify calculations use config fees
        self.assertEqual(registration.base_amount, Decimal('125.00'))  # 1 participant * $125
        self.assertEqual(registration.total_amount, Decimal('125.00'))
        self.assertEqual(total, Decimal('125.00'))

    def test_fallback_rates_without_event(self):
        """Test calculations using fallback rates when no event is linked."""
        # Create registration without event
        registration_data = self.registration_data.copy()
        del registration_data['event']  # Remove event reference
        registration_data['number_of_participants'] = 2
        registration_data['number_of_guests'] = 1

        registration = EventRegistration.objects.create(
            **registration_data
        )

        # Calculate fees should use fallback rates
        total = registration.calculate_total_amount()

        # Determine expected fallback rates based on current date vs hardcoded date
        fallback_late_date = timezone.datetime(2024, 12, 15, tzinfo=timezone.get_current_timezone())
        if timezone.now() > fallback_late_date:
            expected_base_rate = 115
        else:
            expected_base_rate = 100
        expected_guest_rate = 25

        # Verify calculations use fallback fees
        self.assertEqual(registration.base_amount, Decimal(str(2 * expected_base_rate)))
        self.assertEqual(registration.guest_amount, Decimal(str(1 * expected_guest_rate)))
        self.assertEqual(registration.total_amount,
                         Decimal(str(2 * expected_base_rate + 1 * expected_guest_rate)))


class EventConfigCalculationTests(TestCase):
    """Tests for calculations specifically related to EventConfig changes."""

    def setUp(self):
        """Set up test data."""
        self.today = timezone.now().date()

        # Create initial config
        self.config = EventConfig.objects.create(
            name='Initial Config',
            description='Initial test configuration',
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('130.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=14,
            is_active=True
        )

        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Config Test User'
        )

    def test_event_creation_with_and_without_config(self):
        """Test event creation with and without explicit config."""
        # Create event with explicit config
        event_with_config = Event.objects.create(
            event_name='Explicit Config Event',
            event_date=self.today + timedelta(days=30),
            event_location='Config Test Location',
            config=self.config
        )

        # Create event without explicit config
        event_without_config = Event.objects.create(
            event_name='Implicit Config Event',
            event_date=self.today + timedelta(days=30),
            event_location='Config Test Location'
        )

        # Both should have the same fee values from config
        self.assertEqual(event_with_config.registration_fee_normal, self.config.registration_fee_normal)
        self.assertEqual(event_without_config.registration_fee_normal, self.config.registration_fee_normal)

        # Both should reference same config
        self.assertEqual(event_with_config.get_effective_config, self.config)
        self.assertEqual(event_without_config.get_effective_config, self.config)

    def test_changing_active_config_effect(self):
        """Test effect of changing the active config."""
        # Create event that uses active config implicitly
        event = Event.objects.create(
            event_name='Active Config Test Event',
            event_date=self.today + timedelta(days=30),
            event_location='Config Test Location'
        )

        # Create a new config with different values
        new_config = EventConfig.objects.create(
            name='New Active Config',
            description='New active test configuration',
            registration_fee_normal=Decimal('150.00'),
            registration_fee_late=Decimal('180.00'),
            guest_fee=Decimal('75.00'),
            default_max_participants=200,
            days_until_late_registration=10,
            is_active=True  # This will make it the active config
        )

        # Original config should now be inactive
        self.config.refresh_from_db()
        self.assertFalse(self.config.is_active)

        # New event created after config change should use new config
        new_event = Event.objects.create(
            event_name='New Config Test Event',
            event_date=self.today + timedelta(days=30),
            event_location='Config Test Location'
        )

        # Original event should still use old values (values were copied at creation)
        self.assertEqual(event.registration_fee_normal, self.config.registration_fee_normal)

        # New event should use new config's values
        self.assertEqual(new_event.registration_fee_normal, new_config.registration_fee_normal)

        # But get_effective_config should return the current active config for both
        # if they don't have an explicit config reference
        self.assertEqual(event.get_effective_config, new_config)
        self.assertEqual(new_event.get_effective_config, new_config)

    def test_explicit_config_override(self):
        """Test explicit config overrides active config."""
        # Create a new inactive config
        inactive_config = EventConfig.objects.create(
            name='Inactive Config',
            description='Inactive test configuration',
            registration_fee_normal=Decimal('75.00'),
            registration_fee_late=Decimal('95.00'),
            guest_fee=Decimal('40.00'),
            default_max_participants=50,
            days_until_late_registration=5,
            is_active=False
        )

        # Create event with explicit reference to inactive config
        event = Event.objects.create(
            event_name='Inactive Config Test Event',
            event_date=self.today + timedelta(days=30),
            event_location='Config Test Location',
            config=inactive_config
        )

        # Event should use inactive config's values, not active config
        self.assertEqual(event.registration_fee_normal, inactive_config.registration_fee_normal)
        self.assertNotEqual(event.registration_fee_normal, self.config.registration_fee_normal)

        # get_effective_config should return the explicitly referenced config
        self.assertEqual(event.get_effective_config, inactive_config)

    def test_late_registration_date_calculation(self):
        """Test late registration date calculation based on config."""
        # Create event without explicit late_registration_date
        event_date = self.today + timedelta(days=30)
        event = Event.objects.create(
            event_name='Late Date Calculation Test',
            event_date=event_date,
            event_location='Late Date Test Location',
            config=self.config
        )

        # Late registration date should be calculated based on config
        expected_late_date = event_date - timedelta(days=self.config.days_until_late_registration)
        self.assertEqual(event.late_registration_date, expected_late_date)

        # Change config's days_until_late_registration
        self.config.days_until_late_registration = 21
        self.config.save()

        # Create a new event
        new_event = Event.objects.create(
            event_name='New Late Date Calculation Test',
            event_date=event_date,
            event_location='Late Date Test Location',
            config=self.config
        )

        # New event should use updated calculation
        new_expected_late_date = event_date - timedelta(days=21)
        self.assertEqual(new_event.late_registration_date, new_expected_late_date)

        # But original event's date should not have changed
        event.refresh_from_db()
        self.assertEqual(event.late_registration_date, expected_late_date)

    def test_event_capacity_calculation(self):
        """Test how event capacity is calculated from config."""
        # Create event without explicit max_participants
        event = Event.objects.create(
            event_name='Capacity Calculation Test',
            event_date=self.today + timedelta(days=30),
            event_location='Capacity Test Location',
            config=self.config
        )

        # max_participants should come from config
        self.assertEqual(event.max_participants, self.config.default_max_participants)

        # Change config's default_max_participants
        self.config.default_max_participants = 200
        self.config.save()

        # Create a new event
        new_event = Event.objects.create(
            event_name='New Capacity Calculation Test',
            event_date=self.today + timedelta(days=30),
            event_location='Capacity Test Location',
            config=self.config
        )

        # New event should use updated value
        self.assertEqual(new_event.max_participants, 200)

        # But original event's capacity should not have changed
        event.refresh_from_db()
        self.assertEqual(event.max_participants, 100)

    def test_config_deletion_effect(self):
        """Test effect of deleting a config."""
        # Create event with explicit config
        event = Event.objects.create(
            event_name='Config Deletion Test',
            event_date=self.today + timedelta(days=30),
            event_location='Deletion Test Location',
            config=self.config
        )

        # Record original values
        original_fee = event.registration_fee_normal

        # Create a new config to be the active one
        new_active_config = EventConfig.objects.create(
            name='New Active Before Deletion',
            description='New active config before deletion test',
            registration_fee_normal=Decimal('200.00'),
            is_active=True
        )

        # Original config should be inactive but still referenced by event
        self.config.refresh_from_db()
        self.assertFalse(self.config.is_active)

        # Verify config can't be deleted due to event relations by mocking the delete process
        # (In real code, the view would block this via a proper error response, not an exception)
        # Here we verify the event relationship exists, which would prevent deletion in the view
        self.assertTrue(self.config.events.exists())
        
        # Now remove the event's reference to the config and set explicit values
        event.config = None
        event.save()

        # Now delete should be possible
        self.config.delete()

        # Verify config is gone
        self.assertFalse(EventConfig.objects.filter(name=self.config.name).exists())

        # Event should still exist with its copied values
        event.refresh_from_db()
        self.assertEqual(event.registration_fee_normal, original_fee)

        # And get_effective_config should now return the active config
        self.assertEqual(event.get_effective_config, new_active_config)
"""
Validators for payment models.
"""
from django.core.exceptions import ValidationError


def validate_invoice_format(value):
    """Custom validator to ensure invoice number format is YYDDD-XXX with integers"""
    error_message = (
        'Invoice number must be in format YYDDD-XXX where Y=year digit, '
        'D=day of year digit, X=sequence number digit')

    if value:  # Allow empty values since field is nullable
        try:
            # Split the value and check format
            prefix, counter = value.split('-')

            # Ensure both parts are valid integers
            if not (prefix.isdigit() and counter.isdigit()):
                raise ValidationError(error_message)

            # Ensure correct lengths (5 digits before hyphen, 3 after)
            if not (len(prefix) == 5 and len(counter) == 3):
                raise ValidationError(error_message)

        except (ValueError, ValidationError) as exc:
            raise ValidationError(error_message) from exc 
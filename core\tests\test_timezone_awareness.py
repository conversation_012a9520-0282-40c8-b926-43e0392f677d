"""
Tests for time zone awareness in the core app.
"""
from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from datetime import datetime, timedelta
from unittest.mock import patch
import pytz

from core.models import Event, EventRegistration

User = get_user_model()


class TimeZoneAwarenessTests(TestCase):
    """Test cases for time zone awareness."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create a user with staff permissions
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Timezone Test User',
            active=True,
            is_staff=True,  # Add staff permission
            is_superuser=True  # Add superuser permission
        )

        # Get token for authentication
        self.token = str(RefreshToken.for_user(self.user).access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')

        # Create events with explicit timezone
        self.utc_now = datetime.now(pytz.UTC)
        self.tomorrow_utc = self.utc_now + timedelta(days=1)
        self.yesterday_utc = self.utc_now - timedelta(days=1)

        # Create an event with UTC timezone
        self.event = Event.objects.create(
            event_name='Timezone Test Event',
            event_date=self.tomorrow_utc.date(),
            event_location='Test Location',
            is_active=True
        )

        # URL for events
        self.events_url = reverse('core:events-list-create')
        self.event_detail_url = reverse('core:events-detail', kwargs={'event_id': self.event.pk})

    @override_settings(TIME_ZONE='UTC')
    def test_event_creation_utc(self):
        """Test event creation with UTC timezone."""
        # Create event data
        event_data = {
            'event_name': 'UTC Event',
            'event_date': self.tomorrow_utc.date().isoformat(),
            'event_location': 'UTC Location'
        }

        response = self.client.post(self.events_url, event_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['success'], True)

        # Check that the event was created with correct date
        event_id = response.data['data']['id']
        event = Event.objects.get(id=event_id)
        self.assertEqual(event.event_date, self.tomorrow_utc.date())

    @override_settings(TIME_ZONE='America/New_York')
    def test_event_creation_est(self):
        """Test event creation with EST timezone."""
        # Create event data
        ny_tz = pytz.timezone('America/New_York')
        ny_time = datetime.now(ny_tz)
        tomorrow_ny = (ny_time + timedelta(days=1)).date()

        event_data = {
            'event_name': 'EST Event',
            'event_date': tomorrow_ny.isoformat(),
            'event_location': 'EST Location'
        }

        response = self.client.post(self.events_url, event_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['success'], True)

        # Check that the event was created with correct date
        event_id = response.data['data']['id']
        event = Event.objects.get(id=event_id)
        self.assertEqual(event.event_date, tomorrow_ny)

    def test_event_retrieval_different_timezones(self):
        """Test event retrieval in different timezones."""
        # Get event details in UTC
        with override_settings(TIME_ZONE='UTC'):
            response_utc = self.client.get(self.event_detail_url)
            self.assertEqual(response_utc.status_code, status.HTTP_200_OK)
            event_date_utc = response_utc.data['data']['event_date']

        # Get event details in EST
        with override_settings(TIME_ZONE='America/New_York'):
            response_est = self.client.get(self.event_detail_url)
            self.assertEqual(response_est.status_code, status.HTTP_200_OK)
            event_date_est = response_est.data['data']['event_date']

        # Dates should be the same regardless of server timezone
        self.assertEqual(event_date_utc, event_date_est)

    @override_settings(TIME_ZONE='UTC')
    def test_event_filter_by_date(self):
        """Test filtering events by date with UTC timezone."""
        # Create multiple events with different dates
        yesterday = (self.utc_now - timedelta(days=1)).date()
        today = self.utc_now.date()
        tomorrow = (self.utc_now + timedelta(days=1)).date()
        next_week = (self.utc_now + timedelta(days=7)).date()

        Event.objects.create(
            event_name='Yesterday Event',
            event_date=yesterday,
            event_location='Past Location',
            is_active=True
        )

        Event.objects.create(
            event_name='Today Event',
            event_date=today,
            event_location='Current Location',
            is_active=True
        )

        Event.objects.create(
            event_name='Tomorrow Event',
            event_date=tomorrow,
            event_location='Future Location',
            is_active=True
        )

        Event.objects.create(
            event_name='Next Week Event',
            event_date=next_week,
            event_location='Future Location',
            is_active=True
        )

        # Filter for events after today
        response = self.client.get(f'{self.events_url}?date_after={today.isoformat()}')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)

        # Should return events on or after today (4 events - including the one created in setUp)
        self.assertEqual(len(response.data['data']), 4)

        # Filter for events before next week
        response = self.client.get(f'{self.events_url}?date_before={next_week.isoformat()}')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return events before next week (4 events - including the one created in setUp)
        self.assertEqual(len(response.data['data']), 4)

        # Filter for events between today and tomorrow
        response = self.client.get(
            f'{self.events_url}?date_after={today.isoformat()}&date_before={next_week.isoformat()}'
        )

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return events between today and next week (4 events - including the one created in setUp)
        self.assertEqual(len(response.data['data']), 4)

    @override_settings(TIME_ZONE='America/New_York')
    def test_event_created_updated_timestamps(self):
        """Test event created_at and updated_at timestamps with EST timezone."""
        est_tz = pytz.timezone('America/New_York')
        before_creation = timezone.now()

        # Create an event
        event_data = {
            'event_name': 'Timestamp Event',
            'event_date': (datetime.now(est_tz) + timedelta(days=1)).date().isoformat(),
            'event_location': 'Timestamp Location'
        }

        response = self.client.post(self.events_url, event_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        after_creation = timezone.now()
        event_id = response.data['data']['id']
        event = Event.objects.get(id=event_id)

        # Check that created_at is between before_creation and after_creation
        self.assertTrue(before_creation <= event.created_at <= after_creation)

        # Update the event
        before_update = timezone.now()
        # Get the event to get all its fields
        event = Event.objects.get(id=event_id)
        # Include all required fields for a PUT request
        event_update_data = {
            'event_name': 'Updated Timestamp Event',
            'event_date': event.event_date.isoformat(),
            'event_location': event.event_location,
            'is_active': event.is_active
        }
        update_url = reverse('core:events-detail', kwargs={'event_id': event_id})

        response = self.client.put(update_url, event_update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        after_update = timezone.now()
        event.refresh_from_db()

        # Check that updated_at is between before_update and after_update
        self.assertTrue(before_update <= event.updated_at <= after_update)

        # Check that created_at has not changed
        self.assertTrue(event.created_at < event.updated_at)

    def test_timezone_conversion(self):
        """Test timezone conversion in event dates."""
        # Create an event with explicit timezone
        eastern_tz = pytz.timezone('America/New_York')
        pacific_tz = pytz.timezone('America/Los_Angeles')

        # Time: 10 AM Eastern = 7 AM Pacific
        eastern_datetime = eastern_tz.localize(datetime(2023, 7, 15, 10, 0))
        pacific_datetime = eastern_datetime.astimezone(pacific_tz)

        # Verify the conversion
        self.assertEqual(pacific_datetime.hour, 7)

        # Create events in different timezones but same point in time
        eastern_event = Event.objects.create(
            event_name='Eastern Event',
            event_date=eastern_datetime.date(),
            event_location='Eastern Location',
            is_active=True
        )

        pacific_event = Event.objects.create(
            event_name='Pacific Event',
            event_date=pacific_datetime.date(),
            event_location='Pacific Location',
            is_active=True
        )

        # The dates should be the same
        self.assertEqual(eastern_event.event_date, pacific_event.event_date)

    def test_timezone_in_serialized_response(self):
        """Test timezone handling in serialized responses."""
        # Create an event registration with a specific timestamp
        registration = EventRegistration.objects.create(
            event=self.event,
            first_name='Timezone',
            last_name='Test',
            fire_department='Test Department',
            address='123 Main St',
            city='Test City',
            state='MS',
            zipcode='12345',
            phone='555-1234',
            email='<EMAIL>',
            registration_type='NORMAL',
            payment_status='PENDING',
            base_amount=100.00,
            guest_amount=0.00,
            total_amount=100.00
        )

        # Get registration details in UTC
        with override_settings(TIME_ZONE='UTC'):
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
            url = reverse('core:event-registrations-detail', kwargs={'registration_id': registration.pk})
            response_utc = self.client.get(url)
            # Use registration_date instead of created_at
            created_at_utc = response_utc.data['data']['registration_date']

        # Get registration details in EST
        with override_settings(TIME_ZONE='America/New_York'):
            response_est = self.client.get(url)
            # Use registration_date instead of created_at
            created_at_est = response_est.data['data']['registration_date']

        # Check that the timestamps are in ISO format with timezone info
        self.assertIn('Z', created_at_utc)  # UTC timezone marker

        # Both responses should provide the same point in time (may have different representation)
        datetime_utc = datetime.fromisoformat(created_at_utc.replace('Z', '+00:00'))
        datetime_est = datetime.fromisoformat(created_at_est.replace('Z', '+00:00'))

        # Convert to the same timezone for comparison
        self.assertEqual(datetime_utc.astimezone(pytz.UTC), datetime_est.astimezone(pytz.UTC))

    def test_timezone_sensitive_queries(self):
        """Test timezone-sensitive database queries."""
        # Create events at different times today
        today = timezone.now().date()

        # Create an event with a specific time
        morning_event = Event.objects.create(
            event_name='Morning Event',
            event_date=today,
            event_location='Morning Location',
            is_active=True
        )

        # Update the created_at time to a specific time today
        morning_event.created_at = timezone.make_aware(datetime.combine(today, datetime.min.time()) + timedelta(hours=5))
        morning_event.save(update_fields=['created_at'])

        afternoon_event = Event.objects.create(
            event_name='Afternoon Event',
            event_date=today,
            event_location='Afternoon Location',
            is_active=True
        )

        # Update the created_at time to a specific time today
        afternoon_event.created_at = timezone.make_aware(datetime.combine(today, datetime.min.time()) + timedelta(hours=14))
        afternoon_event.save(update_fields=['created_at'])

        # Query events created before noon
        noon = timezone.make_aware(datetime.combine(today, datetime.min.time()) + timedelta(hours=12))
        morning_events = Event.objects.filter(created_at__lt=noon)

        # Query events created after noon
        afternoon_events = Event.objects.filter(created_at__gte=noon)

        # Check query results
        self.assertIn(morning_event, morning_events)
        self.assertNotIn(afternoon_event, morning_events)

        self.assertIn(afternoon_event, afternoon_events)
        self.assertNotIn(morning_event, afternoon_events)

    @override_settings(USE_TZ=False)
    def test_timezone_naive_mode(self):
        """Test behavior when USE_TZ is False."""
        # Create an event
        naive_event_data = {
            'event_name': 'Naive Timezone Event',
            'event_date': (datetime.now() + timedelta(days=1)).date().isoformat(),
            'event_location': 'Naive Location'
        }

        response = self.client.post(self.events_url, naive_event_data, format='json')

        # Check response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Get event
        event_id = response.data['data']['id']
        naive_event = Event.objects.get(id=event_id)

        # Check that created_at is naive (doesn't have tzinfo)
        self.assertIsNone(naive_event.created_at.tzinfo)

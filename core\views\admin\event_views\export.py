"""
Export views for admin events.
"""
from rest_framework import status
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from django.http import HttpResponse
from django.shortcuts import get_object_or_404

from core.models import Event, EventRegistration
from core.utils.pdf_utils import generate_event_roster_pdf
from common.utils import track_activity
from common.views import BaseAPIView, APIResponse


class EventRosterExportView(BaseAPIView):
    """
    View for exporting event rosters.
    """
    permission_classes = [IsAuthenticated, IsAdminUser]

    @track_activity('Exported event roster')
    def get(self, request, event_id):
        """
        Export event roster as PDF.
        """
        try:
            # Get the requested format
            export_format = request.query_params.get('format', 'pdf')
            
            # Get the event
            event = get_object_or_404(Event, pk=event_id)
            
            # Get event registrations
            registrations = EventRegistration.objects.filter(event=event)
            
            # Handle PDF export
            if export_format == 'pdf':
                # Generate PDF from event registrations
                pdf_data = generate_event_roster_pdf(event, registrations)
                
                # Prepare the response with PDF content
                response = HttpResponse(pdf_data, content_type='application/pdf')
                response['Content-Disposition'] = f'attachment; filename=event_roster_{event.pk}.pdf'
                return response
                
            # Default response for unsupported formats
            return APIResponse(
                message=f"Export format {export_format} not supported",
                success=False,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Event.DoesNotExist:
            return APIResponse(
                message=f"Event with ID {event_id} not found",
                success=False,
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return APIResponse(
                message=f"Failed to generate PDF: {str(e)}",
                success=False,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 
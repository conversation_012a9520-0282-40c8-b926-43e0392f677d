"""
Common utilities module.
"""

# Import utilities, but avoid importing print_labels which causes circular import

from .email import send_verification_email, verify_email
# Add activity tracking
from .activity_tracking import track_activity
# Add exception handling
from .exception_handling import handle_api_exception

__all__ = [
    'send_verification_email',
    'verify_email',
    'print_labels',  # Referenced but not imported to avoid circular import
    'track_activity',  # Add track_activity to exports
    'handle_api_exception',  # Add exception handling to exports
]
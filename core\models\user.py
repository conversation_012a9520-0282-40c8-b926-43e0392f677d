from datetime import timedelta
import base64
import io
from PIL import Image

from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.utils import timezone
from simple_history.models import HistoricalRecords
from datetime import datetime
from django.db.models import Max
from django.core.exceptions import ValidationError

from .department import Department


class MemberManager(BaseUserManager):
    """
    Custom manager for the Member model
    """
    def create_user(self, email, password=None, **extra_fields):
        """
        Create and save a regular user with the given email and password
        """
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)

        # Check for existing user with same email (case-insensitive)
        if self.model.objects.filter(email__iexact=email).exists():
            # Raise IntegrityError or ValidationError? ValidationError is often preferred
            # outside of atomic transactions during cleaning/validation stages.
            # However, since this manager might be called outside a form context,
            # and the original code used IntegrityError, we'll keep it for now.
            from django.db import IntegrityError
            raise IntegrityError(f"User with email {email} (case-insensitive) already exists")

        user = self.model(email=email, **extra_fields)
        if password:
            user.set_password(password)
        else:
            user.set_unusable_password() # Explicitly set unusable if no password is provided
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        Create and save a superuser with the given email and password
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('active', True)
        extra_fields.setdefault('membership_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)


class Member(AbstractUser):
    """
    Custom User model that incorporates all fields from MembershipRoster
    """
    class MembershipStatus(models.TextChoices):
        MEMBER = 'member', 'Member'
        ASSOCIATE_MEMBER = 'associate_member', 'Associate Member'
        HONORARY_MEMBER = 'honorary_member', 'Honorary Member'
        LIFE_MEMBER = 'life_member', 'Life Member'

    class LeadStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        PENDING = 'pending', 'Pending'

    class Role(models.TextChoices):
        CAREER = 'career', 'Career'
        VOLUNTEER = 'volunteer', 'Volunteer'
        OTHER = 'other', 'Other'

    class Gender(models.TextChoices):
        MALE = 'male', 'Male'
        FEMALE = 'female', 'Female'
        OTHER = 'other', 'Other'
        PREFER_NOT_TO_SAY = 'prefer_not_to_say', 'Prefer not to say'

    # Override username field to use email instead
    username = models.CharField(max_length=150, blank=True, null=True)
    email = models.EmailField(unique=True)  # Email is required and must be unique

    # Personal information
    name = models.CharField(max_length=255, default="")  # Combined full name field
    mi = models.CharField(max_length=255, blank=True, default="", verbose_name="Middle Initial")
    dst = models.CharField(max_length=255, blank=True, default="", verbose_name="District")
    title = models.CharField(max_length=255, blank=True, default="")

    # Contact information
    address = models.CharField(max_length=510, blank=True, default="")  # Combined address field
    city = models.CharField(max_length=255, blank=True, default="")
    st = models.CharField(max_length=2, default="MS", verbose_name="State")
    zip_code = models.CharField(max_length=10, blank=True, default="")
    home_phone = models.CharField(max_length=255, blank=True, default="")  # Main phone field
    business_phone = models.CharField(max_length=255, blank=True, default="")

    # Additional fields from CSV
    county = models.CharField(max_length=255, blank=True, default="")
    account = models.CharField(max_length=255, blank=True, default="")
    lead_status = models.CharField(max_length=20, choices=LeadStatus.choices, default=LeadStatus.ACTIVE)
    role = models.CharField(max_length=20, choices=Role.choices, default=Role.VOLUNTEER)
    gender = models.CharField(max_length=20, choices=Gender.choices, default=Gender.PREFER_NOT_TO_SAY)
    dob = models.DateTimeField(null=True, blank=True, verbose_name="Date of Birth")

    # Department relationship
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True, related_name='members')

    # Membership details
    membership_class = models.CharField(max_length=20, choices=MembershipStatus.choices, default=MembershipStatus.MEMBER)
    executive_board = models.BooleanField(default=False)
    committee_member = models.BooleanField(default=False)
    committee = models.CharField(max_length=255, blank=True, default="")
    new_member = models.BooleanField(default=False)
    lifetime = models.BooleanField(default=False)
    paid_next_year = models.BooleanField(default=False)
    lapel_pin = models.CharField(max_length=255, blank=True, default="")
    is_deceased = models.BooleanField(default=False, verbose_name="Deceased")
    active = models.BooleanField(default=False, verbose_name="Is Active?")
    membership_active = models.BooleanField(default=False, verbose_name="Is Membership Active?")
    orig_join_date = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True, default="")
    picture = models.TextField(blank=True, default="")

    # Track changes
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    # Historical records
    history = HistoricalRecords()

    # Set email as the USERNAME_FIELD
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    # Use custom manager
    objects = MemberManager()

    @property
    def is_honorary(self):
        return self.membership_class == self.MembershipStatus.HONORARY_MEMBER

    @property
    def is_associate(self):
        return self.membership_class == self.MembershipStatus.ASSOCIATE_MEMBER

    @property
    def is_email_verified(self):
        """Check if user has any verified email verification records"""
        # Import here to avoid circular import
        from common.models import EmailVerification
        return EmailVerification.objects.filter(user=self, verified=True).exists()

    @is_email_verified.setter
    def is_email_verified(self, value):
        """
        Setter for backward compatibility with tests
        """
        # Store the value to be processed after the user is saved
        self._is_email_verified_value = value

        # For tests that check the property immediately after setting
        if value is True:
            # Import here to avoid circular import
            from common.models import EmailVerification
            # Create a verification record immediately if we're in a test
            if self.pk:  # Only if the user is already saved
                verification, _ = EmailVerification.objects.get_or_create(
                    user=self,
                    defaults={'verified': True}
                )
                if not verification.verified:
                    verification.verified = True
                    verification.save()

    def resize_and_convert_image(self, base64_image):
        """Resize image to 400px height while maintaining aspect ratio"""
        try:
            # Check if image already has a data URI prefix
            has_data_uri = False
            if ',' in base64_image and ';base64,' in base64_image:
                header, base64_image_data = base64_image.split(',', 1)
                has_data_uri = True
            else:
                # No header, just the base64 data
                base64_image_data = base64_image

            # Decode base64 to binary
            image_data = base64.b64decode(base64_image_data)

            # Open image with PIL
            img = Image.open(io.BytesIO(image_data))

            # Calculate new width maintaining aspect ratio
            target_height = 400
            width, height = img.size
            new_width = int((target_height / height) * width)

            # Resize image
            resized_img = img.resize((new_width, target_height), Image.LANCZOS)

            # Save resized image to buffer
            buffer = io.BytesIO()
            img_format = img.format if img.format else 'JPEG'
            
            # Ensure the format is one Pillow can save as (JPEG, PNG, GIF)
            supported_formats = ['JPEG', 'PNG', 'GIF']
            if img_format.upper() not in supported_formats:
                img_format = 'JPEG'

            save_kwargs = {}
            if img_format.upper() == 'JPEG':
                if resized_img.mode != 'RGB':
                    resized_img = resized_img.convert('RGB')
                save_kwargs['quality'] = 90
                save_kwargs['optimize'] = True
            elif img_format.upper() == 'PNG':
                save_kwargs['optimize'] = True
            
            resized_img.save(buffer, format=img_format, **save_kwargs)

            # Encode to base64
            resized_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Add appropriate data URI prefix if it wasn't there or reuse existing one
            if has_data_uri:
                return f"{header},{resized_base64}"
            else:
                # Determine MIME type based on image format
                mime_type = f"image/{img_format.lower()}"
                return f"data:{mime_type};base64,{resized_base64}"

        except Exception as e:
            # Return the original image for all errors
            return base64_image

    def clean(self):
        """
        Clean method to validate data before saving
        """
        super().clean()

        # Validate boolean fields
        boolean_fields = ['executive_board', 'committee_member', 'new_member',
                          'lifetime', 'paid_next_year', 'is_deceased',
                          'active', 'membership_active']

        for field_name in boolean_fields:
            field_value = getattr(self, field_name)
            if isinstance(field_value, str):
                if field_value.lower() in ('true', 't', 'yes', 'y', '1'):
                    setattr(self, field_name, True)
                elif field_value.lower() in ('false', 'f', 'no', 'n', '0', ''):
                    setattr(self, field_name, False)
                else:
                    raise ValidationError({field_name: f'"{field_value}" value must be either True or False.'})

    def save(self, *args, **kwargs):
        # Generate a default email if none is provided
        if not self.email:
            # Save first to get an ID if this is a new user
            if not self.id:
                # Temporarily set a placeholder email
                self.email = '<EMAIL>'
                super().save(*args, **kwargs)

                # Now generate the proper email with the ID
                email_id = f'member_{self.id}'
                base_email = f"{email_id}@test.com"
                counter = 1
                temp_email = base_email
                while Member.objects.filter(email=temp_email).exclude(id=self.id).exists():
                    temp_email = f"{email_id}_{counter}@test.com"
                    counter += 1
                self.email = temp_email

                # Continue with normal save
                return super().save(*args, **kwargs)
            else:
                # User already has an ID
                email_id = f'member_{self.id}'
                base_email = f"{email_id}@test.com"
                counter = 1
                temp_email = base_email
                while Member.objects.filter(email=temp_email).exclude(id=self.id).exists():
                    temp_email = f"{email_id}_{counter}@test.com"
                    counter += 1
                self.email = temp_email

        # Map phone to home_phone if home_phone is empty
        if not self.home_phone and hasattr(self, '_phone'):
            self.home_phone = getattr(self, '_phone')
            delattr(self, '_phone')

        # Process the image if it's been changed
        if self.picture and (not self.pk or Member.objects.get(pk=self.pk).picture != self.picture):
            self.picture = self.resize_and_convert_image(self.picture)

        super().save(*args, **kwargs)

        # Process email verification after the user is saved
        if hasattr(self, '_is_email_verified_value') and self._is_email_verified_value is True:
            # Import here to avoid circular import
            from common.models import EmailVerification
            # Get or create a verification record
            verification, _ = EmailVerification.objects.get_or_create(
                user=self,
                defaults={'verified': True}
            )
            if not verification.verified:
                verification.verified = True
                verification.save()

    def __str__(self):
        if self.name:
            return self.name
        if self.email and self.email != '<EMAIL>':
            return self.email
        return str(self.id)

    class Meta:
        ordering = ['name', '-active', 'id']
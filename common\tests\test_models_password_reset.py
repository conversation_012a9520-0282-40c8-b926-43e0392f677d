"""
Tests for the PasswordReset model in the common app.

This module contains comprehensive tests for the PasswordReset model,
including creation, properties, and methods.
"""
import uuid
from datetime import timedelta

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model

from common.models import PasswordReset

User = get_user_model()


class PasswordResetModelTests(TestCase):
    """Test cases for the PasswordReset model."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_password_reset_creation(self):
        """Test that a PasswordReset instance can be created with correct attributes."""
        self.assertIsInstance(self.reset, PasswordReset)
        self.assertEqual(self.reset.user, self.user)
        self.assertFalse(self.reset.used)
        self.assertIsNotNone(self.reset.key)
        self.assertIsNotNone(self.reset.created_at)
        self.assertIsNotNone(self.reset.expires_at)

    def test_password_reset_string_representation_unused(self):
        """Test the string representation of an unused PasswordReset."""
        expected_string = f"{self.user.email} - Unused"
        self.assertEqual(str(self.reset), expected_string)
        
    def test_password_reset_string_representation_used(self):
        """Test the string representation of a used PasswordReset."""
        # Mark as used
        self.reset.used = True
        self.reset.save()
        
        expected_string = f"{self.user.email} - Used"
        self.assertEqual(str(self.reset), expected_string)

    def test_password_reset_save_method_sets_expires_at(self):
        """Test the save method sets expires_at if not provided."""
        # Create a new reset without expires_at
        new_reset = PasswordReset(user=self.user)
        new_reset.save()
        
        # Check that expires_at was set
        self.assertIsNotNone(new_reset.expires_at)
        
        # Check that expires_at is approximately 24 hours in the future
        expected_expiry = timezone.now() + timedelta(hours=24)
        self.assertAlmostEqual(
            new_reset.expires_at.timestamp(),
            expected_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )
    
    def test_password_reset_save_method_preserves_expires_at(self):
        """Test the save method preserves expires_at if already set."""
        # Create a reset with a specific expiry date
        custom_expiry = timezone.now() + timedelta(hours=12)
        reset = PasswordReset(
            user=self.user,
            expires_at=custom_expiry
        )
        reset.save()
        
        # Check that the custom expires_at was preserved
        self.assertAlmostEqual(
            reset.expires_at.timestamp(),
            custom_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )

    def test_is_expired_property_when_used(self):
        """Test is_expired property returns True when reset is used."""
        self.reset.used = True
        self.reset.save()
        self.assertTrue(self.reset.is_expired)

    def test_is_expired_property_when_expired(self):
        """Test is_expired property returns True when expiry date has passed."""
        self.reset.expires_at = timezone.now() - timedelta(hours=1)
        self.reset.save()
        self.assertTrue(self.reset.is_expired)

    def test_is_expired_property_when_not_expired(self):
        """Test is_expired property returns False when not expired and not used."""
        self.reset.expires_at = timezone.now() + timedelta(hours=1)
        self.reset.used = False
        self.reset.save()
        self.assertFalse(self.reset.is_expired)

    def test_is_expired_property_with_none_expires_at(self):
        """Test is_expired property handles None expires_at value."""
        # Instead of testing with None (which violates the NOT NULL constraint),
        # we'll create a new instance without explicitly setting expires_at
        # and then test the is_expired property before saving
        reset = PasswordReset(
            user=self.user,
            key=uuid.uuid4(),
            used=False
        )
        # Temporarily set expires_at to None for testing the property
        # (without saving to the database)
        reset.expires_at = None
        # The is_expired property should handle None gracefully
        self.assertFalse(reset.is_expired)
    
    def test_multiple_resets_for_same_user(self):
        """Test that multiple reset records can exist for the same user."""
        # Create a second reset for the same user
        second_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Check that both resets exist and are different
        resets = PasswordReset.objects.filter(user=self.user)
        self.assertEqual(resets.count(), 2)
        self.assertNotEqual(self.reset.key, second_reset.key)
    
    def test_unique_key_constraint(self):
        """Test that reset keys must be unique."""
        # Try to create a second reset with the same key
        with self.assertRaises(Exception):  # Should raise an integrity error
            PasswordReset.objects.create(
                user=self.user,
                key=self.reset.key,  # Same key as existing reset
                used=False,
                expires_at=timezone.now() + timedelta(hours=24)
            )
    
    def test_reset_with_far_future_expiry(self):
        """Test reset with a far future expiry date."""
        # Create a reset with a far future expiry date
        far_future = timezone.now() + timedelta(days=365)
        reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=far_future
        )
        
        # Check that the reset is not expired
        self.assertFalse(reset.is_expired)
        
        # Check that the expiry date was preserved
        self.assertAlmostEqual(
            reset.expires_at.timestamp(),
            far_future.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )

"""
Tests for the email verification resend view in the common app.

This module contains comprehensive tests for the ResendVerificationEmailView class.
"""
import uuid
from unittest.mock import patch
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from common.models import EmailVerification

User = get_user_model()


class ResendVerificationEmailViewTests(TestCase):
    """Test cases for the ResendVerificationEmailView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create test users - first without setting is_email_verified
        self.unverified_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Unverified User'
        )

        self.verified_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Verified User'
        )

        # Now update is_email_verified after the user is saved
        self.verified_user.is_email_verified = True
        self.verified_user.save()

        # Create an email verification record for the unverified user
        self.verification = EmailVerification.objects.create(
            user=self.unverified_user,
            key=uuid.uuid4()
        )

        # URL for resending verification email
        self.url = reverse('core:resend-verification-email')

    @patch('core.views.email_verification.send_verification_email')
    def test_resend_verification_email_success(self, mock_send_email):
        """Test successful resend of verification email."""
        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('Verification email has been sent', response.data['message'])

        # Check that send_verification_email was called with the correct user
        mock_send_email.assert_called_once_with(self.unverified_user)

    def test_resend_verification_email_already_verified(self):
        """Test resend verification email for already verified user."""
        # Make the request for a verified user
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('Verification email has been sent', response.data['message'])

    def test_resend_verification_email_nonexistent_user(self):
        """Test resend verification email for nonexistent user."""
        # Make the request with a nonexistent email
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should return 200 for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        # The message should be generic to avoid user enumeration
        self.assertIn('If the email exists in our system, a verification email has been sent', response.data['message'])

    def test_resend_verification_email_missing_email(self):
        """Test resend verification email with missing email."""
        # Make the request without an email
        response = self.client.post(self.url, {})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Email address is required', response.data['message'])

    def test_resend_verification_email_empty_email(self):
        """Test resend verification email with an empty email."""
        # Make the request with an empty email
        response = self.client.post(self.url, {'email': ''})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Email address is required', response.data['message'])

    def test_resend_verification_email_invalid_email_format(self):
        """Test resend verification email with an invalid email format."""
        # Make the request with an invalid email format
        response = self.client.post(self.url, {'email': 'not-an-email'})

        # Check the response - should return 200 for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        # The message should be generic to avoid user enumeration
        self.assertIn('If the email exists in our system, a verification email has been sent', response.data['message'])

    @patch('core.views.email_verification.send_verification_email')
    def test_resend_verification_email_with_existing_verification(self, mock_send_email):
        """Test resend verification email when user already has a verification record."""
        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('Verification email has been sent', response.data['message'])

        # Check that send_verification_email was called with the correct user
        mock_send_email.assert_called_once_with(self.unverified_user)

        # Check that the old verification record was invalidated
        # Note: This depends on the implementation of send_verification_email,
        # which is mocked here. The actual invalidation would be tested in the
        # send_verification_email tests.

    def test_resend_verification_email_method_not_allowed(self):
        """Test that only POST method is allowed for resending verification email."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response - should return 405 Method Not Allowed
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_resend_verification_email_with_uppercase_email(self):
        """Test resend verification email with uppercase email."""
        # Make the request with uppercase email
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should work with case-insensitive email matching
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('If the email exists in our system, a verification email has been sent', response.data['message'])

    def test_resend_verification_email_with_whitespace_in_email(self):
        """Test resend verification email with whitespace in email."""
        # Make the request with whitespace in email
        response = self.client.post(self.url, {'email': ' <EMAIL> '})

        # Check the response - should work with trimmed email
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('If the email exists in our system, a verification email has been sent', response.data['message'])

    def test_resend_verification_email_with_json_content_type(self):
        """Test resend verification email with JSON content type."""
        # Make the request with JSON content type
        response = self.client.post(
            self.url,
            {'email': '<EMAIL>'},
            format='json'
        )

        # Check the response - should work with JSON content type
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('Verification email has been sent', response.data['message'])

    @patch('core.views.email_verification.send_verification_email')
    def test_resend_verification_email_error_handling(self, mock_send_email):
        """Test error handling in resend verification email."""
        # Mock send_verification_email to raise an exception
        mock_send_email.side_effect = Exception("Test exception")

        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should return 500 Internal Server Error
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['success'], False)
        self.assertIn('message: Test exception', response.data['message'])


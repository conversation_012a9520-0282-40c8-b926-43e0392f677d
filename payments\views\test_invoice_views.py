"""
Test views for downloading invoice PDFs using different libraries
"""
from django.http import Http404
from rest_framework import status

from common.views import BaseAPIView, APIResponse
from common.utils.pdf_generator import (
    render_to_pdf,
    generate_pdf_with_weasyprint,
    generate_invoice_pdf_with_reportlab
)
from payments.models import Payment
from payments.services import InvoiceService


class BaseTestInvoicePDFView(BaseAPIView):
    """Base test view for downloading invoice PDFs"""
    permission_classes = []

    def _get_first_payment(self):
        """Get the first available payment"""
        payment = Payment.objects.select_related('payer', 'event_registration').first()

        if not payment:
            raise Http404("No payments found in the system")

        return payment




class TestInvoicePDFReportLabView(BaseTestInvoicePDFView):
    """Test view for downloading invoice PDF using ReportLab"""

    def get(self, request, *args, **kwargs):
        """Download invoice PDF using ReportLab"""
        try:
            payment = self._get_first_payment()
            invoice_items = [item.to_dict() for item in InvoiceService.generate_invoice_items(payment)]

            # Generate PDF using ReportLab
            filename = f"test_invoice_reportlab_{payment.invoice_number}.pdf"
            return generate_invoice_pdf_with_reportlab(payment, invoice_items, filename)
        except Http404 as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )


class TestInvoicePDFWeasyPrintView(BaseTestInvoicePDFView):
    """Test view for downloading invoice PDF using WeasyPrint"""

    def get(self, request, *args, **kwargs):
        """Download invoice PDF using WeasyPrint"""
        try:
            payment = self._get_first_payment()

            # Generate context using service
            context = InvoiceService.generate_invoice_context(payment)

            # Generate PDF using WeasyPrint
            filename = f"test_invoice_weasyprint_{payment.invoice_number}.pdf"
            return generate_pdf_with_weasyprint('pdf/invoice.html', context, filename)
        except Http404 as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )


class TestInvoicePDFXhtml2PDFView(BaseTestInvoicePDFView):
    """Test view for downloading invoice PDF using xhtml2pdf"""

    def get(self, request, *args, **kwargs):
        """Download invoice PDF using xhtml2pdf"""
        try:
            payment = self._get_first_payment()

            # Generate context using service
            context = InvoiceService.generate_invoice_context(payment)

            # Generate PDF using xhtml2pdf
            pdf = render_to_pdf('pdf/invoice.html', context)
            if pdf:
                filename = f"test_invoice_xhtml2pdf_{payment.invoice_number}.pdf"
                pdf['Content-Disposition'] = f'attachment; filename="{filename}"'
                return pdf

            return APIResponse(
                message="Error generating PDF with xhtml2pdf",
                data=None,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Http404 as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from simple_history.admin import SimpleHistoryAdmin

from .models import Member, Department, EventRegistration
from core.models.event import Event
from core.models.event_config import EventConfig

@admin.register(Department)
class DepartmentAdmin(SimpleHistoryAdmin):
    """
    Admin for the Department model
    """
    list_display = ('name', 'department_city', 'department_county', 'department_state')
    search_fields = ('name', 'department_city', 'department_county', 'billing_city', 'billing_county')
    list_filter = ('department_state', 'billing_state')


@admin.register(Member)
class MemberAdmin(SimpleHistoryAdmin):
    """
    Custom admin for the Member model
    """
    list_display = ('name', 'email', 'department', 'membership_class', 'active', 'membership_active', 'is_deceased')
    list_filter = ('active', 'membership_active', 'membership_class', 'is_deceased', 'executive_board', 'committee_member')
    search_fields = ('name', 'email', 'department__name')
    fieldsets = (
        ('Personal Information', {
            'fields': ('name', 'mi', 'dst', 'title', 'email', 'username')
        }),
        ('Contact Information', {
            'fields': ('address', 'city', 'st', 'zip_code', 'home_phone', 'business_phone')
        }),
        ('Department', {
            'fields': ('department',)
        }),
        ('Membership Details', {
            'fields': ('membership_class', 'executive_board', 'committee_member', 'committee',
                      'new_member', 'lifetime', 'paid_next_year', 'lapel_pin', 'is_deceased', 'active', 'membership_active',
                      'orig_join_date', 'notes', 'picture')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',),
        }),
        ('Important dates', {
            'fields': ('last_login', 'date_joined', 'date_created', 'date_updated'),
            'classes': ('collapse',),
        }),
    )
    readonly_fields = ('date_created', 'date_updated')
    filter_horizontal = ('groups', 'user_permissions')


@admin.register(EventRegistration)
class EventRegistrationAdmin(SimpleHistoryAdmin):
    list_display = ('id', 'get_full_name', 'fire_department', 'registration_type', 'number_of_participants',
                    'total_amount', 'payment_status', 'registration_date')
    list_filter = ('registration_type', 'payment_status', 'group_registration')
    search_fields = ('first_name', 'last_name', 'email', 'fire_department', 'invoice_number')
    date_hierarchy = 'registration_date'
    readonly_fields = ('registration_date',)
    fieldsets = (
        ('Registrant Information', {
            'fields': ('member', 'first_name', 'last_name', 'title', 'fire_department', 'email', 'phone')
        }),
        ('Address', {
            'fields': ('address', 'city', 'state', 'zipcode')
        }),
        ('Registration Details', {
            'fields': ('registration_type', 'number_of_participants', 'number_of_guests', 'notes', 'invoice_number')
        }),
        ('Payment Information', {
            'fields': ('base_amount', 'guest_amount', 'total_amount', 'payment_status')
        }),
        ('Group Registration', {
            'fields': ('group_registration', 'group_members')
        }),
        ('Additional Participants', {
            'fields': ('extra_participants',)
        }),
    )
    filter_horizontal = ('group_members',)

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.calculate_total_amount()
        super().save_model(request, obj, form, change)


@admin.register(Event)
class EventAdmin(SimpleHistoryAdmin):
    """
    Admin for the Event model
    """
    list_display = ('event_name', 'event_date', 'event_location', 'registration_fee_normal', 
                    'total_registrations', 'is_late_registration', 'is_at_capacity', 'is_active')
    list_filter = ('is_active', 'event_date')
    search_fields = ('event_name', 'event_location', 'event_description')
    readonly_fields = ('created_at', 'updated_at', 'total_registrations', 'spots_remaining', 
                       'is_late_registration', 'is_at_capacity')
    fieldsets = (
        ('Basic Information', {
            'fields': ('event_name', 'event_date', 'event_end_date', 'event_location', 'event_description', 'is_active')
        }),
        ('Configuration', {
            'fields': ('config',)
        }),
        ('Registration Fees', {
            'fields': ('registration_fee_normal', 'registration_fee_late', 'guest_fee', 'late_registration_date')
        }),
        ('Capacity', {
            'fields': ('max_participants', 'total_registrations', 'spots_remaining', 'is_at_capacity')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def save_model(self, request, obj, form, change):
        obj.save()  # Will apply defaults from config if needed


@admin.register(EventConfig)
class EventConfigAdmin(SimpleHistoryAdmin):
    """
    Admin for the EventConfig model
    """
    list_display = ('name', 'registration_fee_normal', 'registration_fee_late', 'guest_fee', 
                    'default_max_participants', 'days_until_late_registration', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Default Registration Fees', {
            'fields': ('registration_fee_normal', 'registration_fee_late', 'guest_fee')
        }),
        ('Default Settings', {
            'fields': ('default_max_participants', 'days_until_late_registration')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if obj.is_active:
            # Display a message about deactivating other configs
            if not change or not EventConfig.objects.get(pk=obj.pk).is_active:
                self.message_user(request, 
                                  f"Configuration '{obj.name}' has been set as active. All other configurations have been deactivated.")
        super().save_model(request, obj, form, change)

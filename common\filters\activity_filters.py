from django_filters import rest_framework as filters
from common.models import UserActivity
from .base_filters import BaseBooleanFilterSet

class UserActivityFilter(BaseBooleanFilterSet):
    """
    Filter class for UserActivity model with filters for user, description, and time range
    """
    # User filters
    user = filters.NumberFilter(field_name='user__id')
    user_email = filters.CharFilter(field_name='user__email', lookup_expr='icontains')
    user_name = filters.CharFilter(field_name='user__name', lookup_expr='icontains')
    
    # Description filter
    description = filters.CharFilter(lookup_expr='icontains')
    
    # Time range filters
    from_date = filters.DateTimeFilter(field_name='timestamp', lookup_expr='gte')
    to_date = filters.DateTimeFilter(field_name='timestamp', lookup_expr='lte')
    
    class Meta:
        model = UserActivity
        fields = [
            'user', 'user_email', 'user_name', 'description',
            'from_date', 'to_date'
        ] 
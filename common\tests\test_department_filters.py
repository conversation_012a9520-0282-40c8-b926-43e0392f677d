from django.test import TestCase
from django.db import models
from django_filters import rest_framework as filters
from common.filters.department_filters import DepartmentFilter
from core.models.department import Department
from django.test.utils import setup_test_environment


class TestDepartmentFilter(DepartmentFilter):
    """Test filter class configured with Department model"""
    class Meta(DepartmentFilter.Meta):
        model = Department
        fields = DepartmentFilter.Meta.fields


class DepartmentFilterTests(TestCase):
    """Test cases for the DepartmentFilter"""

    def setUp(self):
        """Set up test data"""
        # Create departments with various combinations
        self.dept1 = Department.objects.create(
            name="Engineering Department",
            department_city="San Francisco",
            department_county="San Francisco",
            department_state="CA",
            billing_city="San Francisco",
            billing_county="San Francisco",
            billing_state="CA"
        )
        
        self.dept2 = Department.objects.create(
            name="Marketing Department",
            department_city="New York City",
            department_county="New York",
            department_state="NY",
            billing_city="Albany",
            billing_county="Albany",
            billing_state="NY"
        )
        
        self.dept3 = Department.objects.create(
            name="Sales Department",
            department_city="Los Angeles",
            department_county="Los Angeles",
            department_state="CA",
            billing_city="San Diego",
            billing_county="San Diego",
            billing_state="CA"
        )
        
        self.dept4 = Department.objects.create(
            name="Research & Development",
            department_city="San Jose",
            department_county="Santa Clara",
            department_state="CA",
            billing_city="Mountain View",
            billing_county="Santa Clara",
            billing_state="CA"
        )

        self.filter_class = TestDepartmentFilter

    def test_name_filter(self):
        """Test filtering by department name"""
        # Exact name match
        filterset = self.filter_class({'name': 'Engineering Department'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept1])

        # Partial name match
        filterset = self.filter_class({'name': 'Department'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 3)  # Should match Engineering, Marketing, and Sales

        # Case insensitive match
        filterset = self.filter_class({'name': 'ENGINEERING'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept1])

        # Special characters in name
        filterset = self.filter_class({'name': 'Research & Development'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

    def test_department_city_filter(self):
        """Test filtering by department city"""
        # Exact city match
        filterset = self.filter_class({'department_city': 'San Francisco'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept1])

        # Partial city match
        filterset = self.filter_class({'department_city': 'San'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match San Francisco, San Jose

        # Case insensitive match
        filterset = self.filter_class({'department_city': 'NEW YORK CITY'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept2])

    def test_department_county_filter(self):
        """Test filtering by department county"""
        # Exact county match
        filterset = self.filter_class({'department_county': 'Santa Clara'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

        # Partial county match
        filterset = self.filter_class({'department_county': 'San'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match San Francisco

        # Case insensitive match
        filterset = self.filter_class({'department_county': 'LOS ANGELES'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept3])

    def test_department_state_filter(self):
        """Test filtering by department state"""
        # Exact state match (case sensitive due to iexact)
        filterset = self.filter_class({'department_state': 'CA'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 3)  # Should match all CA departments

        # Case insensitive match
        filterset = self.filter_class({'department_state': 'ny'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept2])

        # Invalid state
        filterset = self.filter_class({'department_state': 'XX'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 0)

    def test_billing_city_filter(self):
        """Test filtering by billing city"""
        # Exact city match
        filterset = self.filter_class({'billing_city': 'Mountain View'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

        # Partial city match
        filterset = self.filter_class({'billing_city': 'San'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match San Francisco, San Diego

        # Case insensitive match
        filterset = self.filter_class({'billing_city': 'ALBANY'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept2])

    def test_billing_county_filter(self):
        """Test filtering by billing county"""
        # Exact county match
        filterset = self.filter_class({'billing_county': 'Santa Clara'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

        # Partial county match
        filterset = self.filter_class({'billing_county': 'San'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 3)  # Matches San Francisco, San Diego, Santa Clara

        # Case insensitive match
        filterset = self.filter_class({'billing_county': 'ALBANY'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept2])

    def test_billing_state_filter(self):
        """Test filtering by billing state"""
        # Exact state match
        filterset = self.filter_class({'billing_state': 'CA'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 3)  # Should match all CA departments

        # Case insensitive match
        filterset = self.filter_class({'billing_state': 'ny'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept2])

        # Invalid state
        filterset = self.filter_class({'billing_state': 'XX'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 0)

    def test_multiple_filters(self):
        """Test applying multiple filters simultaneously"""
        # Test department address filters together
        filterset = self.filter_class({
            'department_city': 'San',
            'department_state': 'CA'
        }, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match San Francisco and San Jose

        # Test billing address filters together
        filterset = self.filter_class({
            'billing_city': 'San',
            'billing_state': 'CA'
        }, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 2)

        # Test mixed department and billing filters
        filterset = self.filter_class({
            'department_state': 'CA',
            'billing_county': 'Santa Clara'
        }, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

        # Test name with address filters
        filterset = self.filter_class({
            'name': 'Department',
            'department_state': 'CA'
        }, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 2)

    def test_empty_filters(self):
        """Test behavior with empty filter values"""
        # Empty filter dict
        filterset = self.filter_class({}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 4)  # Should return all departments

        # Empty string values
        filterset = self.filter_class({
            'name': '',
            'department_city': '',
            'billing_state': ''
        }, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 4)  # Should return all departments

    def test_case_sensitivity(self):
        """Test case sensitivity of various filters"""
        # Test name filter
        filterset = self.filter_class({'name': 'ENGINEERING DEPARTMENT'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept1])

        # Test city filters
        filterset = self.filter_class({'department_city': 'SAN FRANCISCO'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept1])

        # Test county filters
        filterset = self.filter_class({'department_county': 'SANTA CLARA'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

        # Test state filters (should be case insensitive)
        filterset = self.filter_class({'department_state': 'ca'}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 3)

    def test_whitespace_handling(self):
        """Test handling of whitespace in filter values"""
        # Test with leading/trailing whitespace
        filterset = self.filter_class({'name': '  Engineering Department  '}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept1])

        # Test with internal whitespace - need to match exactly how it's stored
        filterset = self.filter_class({'department_city': 'New York City'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept2])

        # Test with only whitespace
        filterset = self.filter_class({'name': '   '}, Department.objects.all())
        self.assertEqual(filterset.qs.count(), 4)  # Should return all departments

    def test_special_characters(self):
        """Test handling of special characters in filter values"""
        # Test with ampersand
        filterset = self.filter_class({'name': 'Research & Development'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4])

        # Test with partial match including special character
        filterset = self.filter_class({'name': '& Dev'}, Department.objects.all())
        self.assertEqual(list(filterset.qs), [self.dept4]) 
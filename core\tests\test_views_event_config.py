"""
Tests for the EventConfig API views.
"""
import logging
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal

from django.contrib.auth import get_user_model
from core.models.event_config import EventConfig

User = get_user_model()
logger = logging.getLogger(__name__)


class EventConfigAPITests(TestCase):
    """Test cases for the EventConfig API views."""

    def setUp(self):
        """Set up test data."""
        print("Setting up EventConfigAPITests...")
        self.client = APIClient()
        
        # Create a staff user (admin)
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Admin User',
            is_staff=True
        )
        
        # Create a regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User'
        )
        
        # Get tokens for authentication
        self.admin_token = str(RefreshToken.for_user(self.admin_user).access_token)
        self.regular_token = str(RefreshToken.for_user(self.regular_user).access_token)
        
        # URLs for testing
        self.configs_url = reverse('core:event-configs-list-create')
        self.active_config_url = reverse('core:event-configs-active')
        
        # Create a test config
        self.config = EventConfig.objects.create(
            name='Test Config',
            description='Test description',
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('120.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=True
        )
        
        self.config_detail_url = reverse('core:event-configs-detail', kwargs={'config_id': self.config.id})
        self.set_active_url = reverse('core:event-configs-set-active', kwargs={'config_id': self.config.id})
        
        # Data for creating a new config
        self.valid_config_data = {
            'name': 'New Config',
            'description': 'New test configuration',
            'registration_fee_normal': 110.00,
            'registration_fee_late': 130.00,
            'guest_fee': 55.00,
            'default_max_participants': 150,
            'days_until_late_registration': 10,
            'is_active': True
        }
        print(f"Setup complete. Config ID: {self.config.id}")

    def test_list_configs_admin(self):
        """Test that an admin can list all configurations."""
        print("Running test_list_configs_admin")
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.get(self.configs_url)
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['name'], self.config.name)

    def test_list_configs_regular_user(self):
        """Test that a regular user cannot list configurations."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')
        response = self.client.get(self.configs_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_config_admin(self):
        """Test that an admin can create a new configuration."""
        print("Running test_create_config_admin")
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.post(self.configs_url, self.valid_config_data, format='json')
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['name'], self.valid_config_data['name'])
        
        # Verify the new config was created
        self.assertTrue(EventConfig.objects.filter(name=self.valid_config_data['name']).exists())
        
        # Verify it's now the active config
        new_config = EventConfig.objects.get(name=self.valid_config_data['name'])
        self.assertTrue(new_config.is_active)
        
        # Original config should now be inactive
        self.config.refresh_from_db()
        self.assertFalse(self.config.is_active)

    def test_create_config_regular_user(self):
        """Test that a regular user cannot create a configuration."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')
        response = self.client.post(self.configs_url, self.valid_config_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Verify no new config was created
        self.assertFalse(EventConfig.objects.filter(name=self.valid_config_data['name']).exists())

    def test_get_config_detail_admin(self):
        """Test that an admin can get config details."""
        print("Running test_get_config_detail_admin")
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.get(self.config_detail_url)
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], self.config.name)
        self.assertEqual(response.data['data']['description'], self.config.description)
        self.assertEqual(Decimal(response.data['data']['registration_fee_normal']), self.config.registration_fee_normal)

    def test_get_config_detail_regular_user(self):
        """Test that a regular user cannot get config details."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')
        response = self.client.get(self.config_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_config_admin(self):
        """Test that an admin can update a configuration."""
        print("Running test_update_config_admin")
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        update_data = {
            'name': 'Updated Config',
            'registration_fee_normal': 125.00
        }
        
        response = self.client.patch(self.config_detail_url, update_data, format='json')
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], update_data['name'])
        self.assertEqual(Decimal(response.data['data']['registration_fee_normal']), Decimal(update_data['registration_fee_normal']))
        
        # Verify config was updated
        self.config.refresh_from_db()
        self.assertEqual(self.config.name, update_data['name'])
        self.assertEqual(self.config.registration_fee_normal, Decimal(update_data['registration_fee_normal']))

    def test_update_config_regular_user(self):
        """Test that a regular user cannot update a configuration."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')
        update_data = {
            'name': 'Should Not Update',
            'registration_fee_normal': 999.00
        }
        
        response = self.client.patch(self.config_detail_url, update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Verify config was not updated
        self.config.refresh_from_db()
        self.assertNotEqual(self.config.name, update_data['name'])
        self.assertNotEqual(self.config.registration_fee_normal, Decimal(update_data['registration_fee_normal']))

    def test_delete_config_admin(self):
        """Test that an admin can delete a configuration."""
        # Create a second, inactive config to test deletion
        inactive_config = EventConfig.objects.create(
            name='Inactive Config',
            description='Inactive test config',
            is_active=False
        )
        
        inactive_config_url = reverse('core:event-configs-detail', kwargs={'config_id': inactive_config.id})
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.delete(inactive_config_url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify config was deleted
        self.assertFalse(EventConfig.objects.filter(id=inactive_config.id).exists())

    def test_cannot_delete_active_config(self):
        """Test that an active configuration cannot be deleted."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.delete(self.config_detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Verify active config was not deleted
        self.assertTrue(EventConfig.objects.filter(id=self.config.id).exists())

    def test_delete_config_regular_user(self):
        """Test that a regular user cannot delete a configuration."""
        inactive_config = EventConfig.objects.create(
            name='Inactive Config',
            description='Inactive test config',
            is_active=False
        )
        
        inactive_config_url = reverse('core:event-configs-detail', kwargs={'config_id': inactive_config.id})
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.regular_token}')
        response = self.client.delete(inactive_config_url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Verify config was not deleted
        self.assertTrue(EventConfig.objects.filter(id=inactive_config.id).exists())

    def test_get_active_config(self):
        """Test getting the active configuration."""
        print("Running test_get_active_config")
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.get(self.active_config_url)
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], self.config.name)
        self.assertTrue(response.data['data']['is_active'])

    def test_set_config_as_active(self):
        """Test setting a configuration as active."""
        print("Running test_set_config_as_active")
        # Create an inactive config
        inactive_config = EventConfig.objects.create(
            name='Soon To Be Active',
            description='Will be set as active',
            is_active=False
        )
        
        set_inactive_active_url = reverse('core:event-configs-set-active', kwargs={'config_id': inactive_config.id})
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.post(set_inactive_active_url)
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], inactive_config.name)
        self.assertTrue(response.data['data']['is_active'])
        
        # Original config should now be inactive
        self.config.refresh_from_db()
        self.assertFalse(self.config.is_active)
        
        # New config should be active
        inactive_config.refresh_from_db()
        self.assertTrue(inactive_config.is_active)

    def test_config_with_invalid_data(self):
        """Test creating a configuration with invalid data."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        
        # Test with negative fees
        invalid_data = dict(self.valid_config_data)
        invalid_data['registration_fee_normal'] = -50.00
        
        response = self.client.post(self.configs_url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test with missing required field
        invalid_data = dict(self.valid_config_data)
        del invalid_data['name']
        
        response = self.client.post(self.configs_url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test with duplicate name
        invalid_data = dict(self.valid_config_data)
        invalid_data['name'] = self.config.name
        
        response = self.client.post(self.configs_url, invalid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST) 
"""
Tests for the exception handlers in the common app.

This module contains comprehensive tests for the custom exception handlers,
including error formatting and response structure for all possible scenarios.
"""
from django.test import TestCase
from rest_framework.views import exception_handler
from rest_framework.exceptions import (
    ValidationError, NotFound, AuthenticationFailed,
    Permission<PERSON>enied, APIException, ParseError, MethodNotAllowed,
    NotAcceptable, UnsupportedMediaType, Throttled
)
from rest_framework import status
from rest_framework.response import Response
from django.core.exceptions import (
    ObjectDoesNotExist, PermissionDenied as DjangoPermissionDenied,
    ValidationError as DjangoValidationError, SuspiciousOperation
)
from django.http import Http404
from rest_framework.test import APIRequestFactory
from rest_framework.views import APIView

from common.exception_handlers.exception_handlers import (
    format_error_message,
    custom_exception_handler
)


class TestView(APIView):
    def get(self, request):
        raise ValidationError("Test error")


class FormatErrorMessageTests(TestCase):
    """Test cases for the format_error_message function"""

    def test_string_with_message_prefix(self):
        """Test formatting string errors that contain 'message:' prefix"""
        error = "Error: message: This is the error. Additional info."
        result = format_error_message(error)
        self.assertEqual(result, "This is the error")

    def test_simple_string_error(self):
        """Test formatting simple string errors"""
        error = "Simple error message"
        result = format_error_message(error)
        self.assertEqual(result, "Simple error message")

    def test_list_errors(self):
        """Test formatting list of errors"""
        errors = ["Error 1", "Error 2", "Error 3"]
        result = format_error_message(errors)
        self.assertEqual(result, "Error 1; Error 2; Error 3")

    def test_dict_with_formatted_response(self):
        """Test formatting already formatted response dict"""
        error = {
            "message": "Error message",
            "success": False,
            "data": None
        }
        result = format_error_message(error)
        self.assertEqual(result, "Error message")

    def test_dict_with_field_errors(self):
        """Test formatting dict with field errors"""
        error = {
            "field1": ["Error 1", "Error 2"],
            "field2": "Error 3"
        }
        result = format_error_message(error)
        self.assertEqual(result, "field1: Error 1, Error 2. field2: Error 3")

    def test_nested_dict_errors(self):
        """Test formatting nested dict errors"""
        error = {
            "field1": {
                "nested": ["Error 1", "Error 2"]
            },
            "field2": "Error 3"
        }
        result = format_error_message(error)
        self.assertIn("field1: {'nested': ['Error 1', 'Error 2']}", result)
        self.assertIn("field2: Error 3", result)

    def test_non_string_errors(self):
        """Test formatting non-string error types"""
        error = 123
        result = format_error_message(error)
        self.assertEqual(result, "123")


class CustomExceptionHandlerTests(TestCase):
    """Test cases for the custom_exception_handler function"""

    def setUp(self):
        self.factory = APIRequestFactory()
        self.view = TestView.as_view()

    def test_validation_error_handling(self):
        """Test handling of ValidationError"""
        request = self.factory.get('/')
        error = ValidationError("Validation failed")
        response = custom_exception_handler(error, {'request': request})

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data['message'], "Validation failed")
        self.assertFalse(response.data['success'])
        self.assertIsNone(response.data['data'])

    def test_not_found_error_handling(self):
        """Test handling of NotFound error"""
        request = self.factory.get('/')
        error = NotFound("Resource not found")
        response = custom_exception_handler(error, {'request': request})

        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.data['message'], "Resource not found")
        self.assertFalse(response.data['success'])
        self.assertIsNone(response.data['data'])

    def test_unhandled_exception_handling(self):
        """Test handling of unhandled exceptions"""
        request = self.factory.get('/')
        error = Exception("Unexpected error")
        response = custom_exception_handler(error, {'request': request})

        self.assertEqual(response.status_code, 500)
        self.assertEqual(response.data['message'], "Unexpected error")
        self.assertFalse(response.data['success'])
        self.assertIsNone(response.data['data'])

    def test_api_exception_handling(self):
        """Test handling of generic APIException"""
        request = self.factory.get('/')
        error = APIException("API error occurred")
        response = custom_exception_handler(error, {'request': request})

        self.assertEqual(response.status_code, 500)
        self.assertEqual(response.data['message'], "API error occurred")
        self.assertFalse(response.data['success'])
        self.assertIsNone(response.data['data'])

    def test_complex_validation_error_handling(self):
        """Test handling of ValidationError with complex error data"""
        request = self.factory.get('/')
        error_detail = {
            'field1': ['Error 1', 'Error 2'],
            'field2': 'Error 3'
        }
        error = ValidationError(error_detail)
        response = custom_exception_handler(error, {'request': request})

        self.assertEqual(response.status_code, 400)
        self.assertIn('field1: Error 1. field1: Error 2.', response.data['message'])
        self.assertIn('field2: Error 3', response.data['message'])
        self.assertFalse(response.data['success'])
        self.assertIsNone(response.data['data'])

    def test_error_with_custom_status_code(self):
        """Test handling of APIException with custom status code"""
        request = self.factory.get('/')
        class CustomError(APIException):
            status_code = 418  # I'm a teapot

        error = CustomError("Custom error")
        response = custom_exception_handler(error, {'request': request})

        self.assertEqual(response.status_code, 418)
        self.assertEqual(response.data['message'], "Custom error")
        self.assertFalse(response.data['success'])
        self.assertIsNone(response.data['data'])

    def test_format_string_error(self):
        """Test formatting a simple string error."""
        error = "This is an error"
        formatted = format_error_message(error)
        self.assertEqual(formatted, "This is an error")

    def test_format_string_error_empty(self):
        """Test formatting an empty string."""
        error = ""
        formatted = format_error_message(error)
        self.assertEqual(formatted, "")

    def test_format_list_error(self):
        """Test formatting a list of errors."""
        error = ["Error 1", "Error 2", "Error 3"]
        formatted = format_error_message(error)
        self.assertEqual(formatted, "Error 1; Error 2; Error 3")

    def test_format_list_error_empty(self):
        """Test formatting an empty list."""
        error = []
        formatted = format_error_message(error)
        self.assertEqual(formatted, "")

    def test_format_list_error_mixed_types(self):
        """Test formatting a list with mixed types."""
        error = ["Error 1", 123, True, None]
        formatted = format_error_message(error)
        self.assertEqual(formatted, "Error 1; 123; True; None")

    def test_format_dict_error(self):
        """Test formatting a dictionary of errors."""
        error = {
            "field1": ["Error 1", "Error 2"],
            "field2": "Error 3"
        }
        formatted = format_error_message(error)
        self.assertEqual(formatted, "field1: Error 1. field1: Error 2. field2: Error 3")

    def test_format_dict_error_empty(self):
        """Test formatting an empty dictionary."""
        error = {}
        formatted = format_error_message(error)
        self.assertEqual(formatted, "")

    def test_format_dict_error_with_special_chars(self):
        """Test formatting a dictionary with special characters."""
        error = {
            "field1": ["Error: with colon", "Error; with semicolon"],
            "field2": "Error. with period"
        }
        formatted = format_error_message(error)
        self.assertIn("field1: Error: with colon. field1: Error; with semicolon.", formatted)
        self.assertIn("field2: Error. with period", formatted)

    def test_format_dict_error_with_nested_dicts(self):
        """Test formatting a dictionary with nested dictionaries."""
        error = {
            "field1": {"nested1": "Nested error 1", "nested2": "Nested error 2"},
            "field2": "Error 3"
        }
        formatted = format_error_message(error)
        self.assertIn("field1: {'nested1': 'Nested error 1', 'nested2': 'Nested error 2'}", formatted)
        self.assertIn("field2: Error 3", formatted)

    def test_format_dict_error_with_nested_lists(self):
        """Test formatting a dictionary with nested lists."""
        error = {
            "field1": [["Nested error 1"], ["Nested error 2"]],
            "field2": "Error 3"
        }
        formatted = format_error_message(error)
        self.assertIn("field1:", formatted)
        self.assertIn("field2: Error 3", formatted)

    def test_format_dict_with_non_string_values(self):
        """Test formatting a dictionary with non-string values."""
        error = {
            "field1": 123,
            "field2": True,
            "field3": None
        }
        formatted = format_error_message(error)
        self.assertIn("field1: 123", formatted)
        self.assertIn("field2: True", formatted)
        self.assertIn("field3: None", formatted)

    def test_format_already_formatted_error(self):
        """Test formatting an already formatted error."""
        error = {
            "message": "Already formatted",
            "success": False,
            "data": None
        }
        formatted = format_error_message(error)
        self.assertEqual(formatted, "Already formatted")

    def test_format_already_formatted_error_with_extra_fields(self):
        """Test formatting an already formatted error with extra fields."""
        error = {
            "message": "Already formatted",
            "success": False,
            "data": None,
            "extra": "Extra field"
        }
        formatted = format_error_message(error)
        self.assertEqual(formatted, "Already formatted")

    def test_format_error_with_message_prefix(self):
        """Test formatting a string with 'message:' prefix."""
        error = "message: This is the message. Details here."
        formatted = format_error_message(error)
        self.assertEqual(formatted, "This is the message")

    def test_format_error_with_message_prefix_only(self):
        """Test formatting a string with only 'message:' prefix."""
        error = "message:"
        formatted = format_error_message(error)
        self.assertEqual(formatted, "")

    def test_format_error_with_message_prefix_and_no_period(self):
        """Test formatting a string with 'message:' prefix and no period."""
        error = "message: This is the message without period"
        formatted = format_error_message(error)
        self.assertEqual(formatted, "This is the message without period")

    def test_format_unexpected_type_int(self):
        """Test formatting an unexpected error type (int)."""
        error = 123
        formatted = format_error_message(error)
        self.assertEqual(formatted, "123")

    def test_format_unexpected_type_bool(self):
        """Test formatting an unexpected error type (bool)."""
        error = True
        formatted = format_error_message(error)
        self.assertEqual(formatted, "True")

    def test_format_unexpected_type_none(self):
        """Test formatting an unexpected error type (None)."""
        error = None
        formatted = format_error_message(error)
        self.assertEqual(formatted, "None")

    def test_format_unexpected_type_custom_object(self):
        """Test formatting an unexpected error type (custom object)."""
        class CustomError:
            def __str__(self):
                return "Custom error string"

        error = CustomError()
        formatted = format_error_message(error)
        self.assertEqual(formatted, "Custom error string")

    def test_format_error_with_error_detail_objects(self):
        """Test formatting errors with DRF ErrorDetail objects."""
        from rest_framework.exceptions import ErrorDetail

        error = {
            "field1": [
                ErrorDetail(string="Error 1", code="invalid"),
                ErrorDetail(string="Error 2", code="required")
            ],
            "field2": ErrorDetail(string="Error 3", code="blank")
        }

        formatted = format_error_message(error)
        self.assertIn("field1: Error 1. field1: Error 2.", formatted)
        self.assertIn("field2: Error 3", formatted)

    def test_format_message_with_message_prefix_corner_cases(self):
        """Test corner cases for message prefix extraction."""
        # Multiple colons in the message
        error = "message: This is: the message. Details here."
        formatted = format_error_message(error)
        self.assertEqual(formatted, "This is: the message")

        # Multiple periods in the message
        error = "message: This is. the message. Details here."
        formatted = format_error_message(error)
        self.assertEqual(formatted, "This is")

        # No period in the message
        error = "message: This is the message without period"
        formatted = format_error_message(error)
        self.assertEqual(formatted, "This is the message without period")

        # Just 'message:' with no content
        error = "message:"
        formatted = format_error_message(error)
        self.assertEqual(formatted, "")

        # 'message:' with only spaces
        error = "message:    "
        formatted = format_error_message(error)
        self.assertEqual(formatted, "")

    def test_exactly_three_keys_in_dict(self):
        """Test that a dict with exactly message, success, data is recognized as formatted."""
        # This matches the exact condition in the code
        error = {
            "message": "Already formatted message",
            "success": True,
            "data": {"some": "data"}
        }
        formatted = format_error_message(error)
        self.assertEqual(formatted, "Already formatted message")

        # But with an extra key it should no longer be treated as formatted
        error = {
            "message": "Already formatted message",
            "success": True,
            "data": {"some": "data"},
            "extra": "something else"
        }
        formatted = format_error_message(error)
        # Should be formatted as a regular dict now
        self.assertNotEqual(formatted, "Already formatted message")
        self.assertIn("message: Already formatted message", formatted)

    def test_validation_error_with_fields(self):
        """Test handling ValidationError with multiple fields."""
        exception = ValidationError({
            "field1": ["Invalid value", "Required field"],
            "field2": "Another error"
        })
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("field1: Invalid value. field1: Required field.", response.data["message"])
        self.assertIn("field2: Another error", response.data["message"])
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_validation_error_with_nested_fields(self):
        """Test handling ValidationError with nested fields."""
        exception = ValidationError({
            "parent": {
                "child1": ["Invalid value"],
                "child2": "Required field"
            },
            "field2": "Another error"
        })
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])
        # Message will contain formatted error string with parent field details

    def test_validation_error_with_list_message(self):
        """Test handling ValidationError with list message."""
        exception = ValidationError(["Error 1", "Error 2", "Error 3"])
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["message"], "Error 1; Error 2; Error 3")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_authentication_failed_error(self):
        """Test handling AuthenticationFailed exception."""
        exception = AuthenticationFailed("Invalid credentials")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(response.data["message"], "Invalid credentials")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_permission_denied_error(self):
        """Test handling PermissionDenied exception."""
        exception = PermissionDenied("Permission denied")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data["message"], "Permission denied")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_parse_error(self):
        """Test handling ParseError exception."""
        exception = ParseError("Malformed request")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["message"], "Malformed request")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_method_not_allowed_error(self):
        """Test handling MethodNotAllowed exception."""
        exception = MethodNotAllowed("POST")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        self.assertEqual(response.data["message"], "Method 'POST' not allowed.")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_not_acceptable_error(self):
        """Test handling NotAcceptable exception."""
        exception = NotAcceptable("Cannot satisfy the request Accept header")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_406_NOT_ACCEPTABLE)
        self.assertEqual(response.data["message"], "Cannot satisfy the request Accept header")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_unsupported_media_type_error(self):
        """Test handling UnsupportedMediaType exception."""
        exception = UnsupportedMediaType("Unsupported media type")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_415_UNSUPPORTED_MEDIA_TYPE)
        self.assertEqual(response.data["message"], "Unsupported media type")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_throttled_error(self):
        """Test handling Throttled exception."""
        exception = Throttled(wait=60, detail="Request was throttled")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertEqual(response.data["message"], "Request was throttled")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_throttled_error_with_default_message(self):
        """Test handling Throttled exception with default message."""
        exception = Throttled(wait=60)
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertIn("throttled", response.data["message"].lower())
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_django_does_not_exist_exception(self):
        """Test handling Django's ObjectDoesNotExist exception."""
        class TestObjectDoesNotExist(ObjectDoesNotExist):
            pass

        exception = TestObjectDoesNotExist("Object does not exist")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data["message"], "Object does not exist")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_django_permission_denied_exception(self):
        """Test handling Django's PermissionDenied exception."""
        exception = DjangoPermissionDenied("Permission denied by Django")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.data["message"], "Permission denied by Django")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_django_validation_error_exception(self):
        """Test handling Django's ValidationError exception."""
        exception = DjangoValidationError("Django validation error")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data["message"], "Django validation error")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_django_validation_error_with_message_list(self):
        """Test handling Django's ValidationError with message list."""
        exception = DjangoValidationError(["Error 1", "Error 2"])
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data["message"], "['Error 1', 'Error 2']")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_django_validation_error_with_message_dict(self):
        """Test handling Django's ValidationError with message dict."""
        exception = DjangoValidationError({
            'field1': ['Error 1', 'Error 2'],
            'field2': 'Error 3'
        })
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])
        # Message will contain dict string representation

    def test_django_suspicious_operation_exception(self):
        """Test handling Django's SuspiciousOperation exception."""
        exception = SuspiciousOperation("Suspicious operation detected")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data["message"], "Suspicious operation detected")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_django_http404_exception(self):
        """Test handling Django's Http404 exception."""
        exception = Http404("Page not found")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data["message"], "Page not found")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_custom_api_exception(self):
        """Test handling a custom APIException."""
        class CustomAPIException(APIException):
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
            default_detail = "Unprocessable Entity"
            default_code = "unprocessable_entity"

        exception = CustomAPIException()
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(response.data["message"], "Unprocessable Entity")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_custom_api_exception_with_custom_detail(self):
        """Test handling a custom APIException with custom detail."""
        class CustomAPIException(APIException):
            status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
            default_detail = "Unprocessable Entity"
            default_code = "unprocessable_entity"

        exception = CustomAPIException("Custom detail message")
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertEqual(response.data["message"], "Custom detail message")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_exception_with_custom_headers(self):
        """Test handling an exception with custom headers."""
        exception = Throttled(wait=60)
        exception.headers = {"Retry-After": "60"}
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertEqual(response.headers["Retry-After"], "60")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_exception_with_no_detail(self):
        """Test handling an exception with no detail attribute."""
        class NoDetailException(Exception):
            pass

        exception = NoDetailException()
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn(str(exception), response.data["message"])
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

    def test_exception_with_complex_detail(self):
        """Test handling an exception with complex detail object."""
        class ComplexDetailException(APIException):
            status_code = status.HTTP_400_BAD_REQUEST

            def __init__(self):
                self.detail = {
                    "code": "complex_error",
                    "message": "Complex error message",
                    "errors": [
                        {"field": "field1", "message": "Error 1"},
                        {"field": "field2", "message": "Error 2"}
                    ]
                }

        exception = ComplexDetailException()
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])
        # Message will contain the formatted complex detail

    def test_preserving_response_headers(self):
        """Test that custom exception handler preserves response headers."""
        # Create an exception with headers
        exception = MethodNotAllowed("GET")
        response = exception_handler(exception, {})

        # Add some custom headers
        response["X-Custom-Header"] = "custom value"
        response["Content-Language"] = "en-US"

        # Run it through our custom handler
        custom_response = custom_exception_handler(exception, {})

        # Headers should be preserved
        self.assertEqual(custom_response["X-Custom-Header"], "custom value")
        self.assertEqual(custom_response["Content-Language"], "en-US")

    def test_response_structure_is_consistent(self):
        """Test that all responses have the same structure regardless of exception type."""
        exceptions_to_test = [
            ValidationError("Validation error"),
            NotFound("Not found"),
            AuthenticationFailed("Auth failed"),
            PermissionDenied("Permission denied"),
            ParseError("Parse error"),
            MethodNotAllowed("POST"),
            UnsupportedMediaType("Unsupported"),
            Throttled(60),
            ValueError("Value error"),
            DjangoValidationError("Django validation"),
            Http404("404")
        ]

        for exception in exceptions_to_test:
            response = custom_exception_handler(exception, {})

            # All should have the same data structure
            self.assertIn("message", response.data)
            self.assertIn("success", response.data)
            self.assertIn("data", response.data)

            # Success should always be False
            self.assertEqual(response.data["success"], False)

            # Data should always be None
            self.assertIsNone(response.data["data"])

            # Message should be a string
            self.assertIsInstance(response.data["message"], str)

    def test_response_has_correct_content_type(self):
        """Test that the response has the correct content type."""
        exception = ValidationError("Validation error")
        response = custom_exception_handler(exception, {})

        # Content type should be application/json
        self.assertEqual(response["Content-Type"], "application/json")

    def test_error_with_split_logic_for_message_prefix(self):
        """Test the specific split logic used for message prefix extraction."""
        # This tests the exact split logic in format_error_message
        error = "message: This is the first part. This is the second part."
        formatted = format_error_message(error)

        # Should extract "This is the first part" (between "message:" and the first ".")
        self.assertEqual(formatted, "This is the first part")

        # Test with a malformed message that would cause an IndexError in the split logic
        error = "message:"  # Nothing after the colon
        formatted = format_error_message(error)

        # Should handle the exception and return the original string
        self.assertEqual(formatted, "message:")

    def test_handling_of_exception_with_str_representation(self):
        """Test handling of exception with a custom __str__ representation."""
        class CustomException(Exception):
            def __str__(self):
                return "Custom exception string representation"

        exception = CustomException()
        response = custom_exception_handler(exception, {})

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data["message"], "Custom exception string representation")
        self.assertEqual(response.data["success"], False)
        self.assertIsNone(response.data["data"])

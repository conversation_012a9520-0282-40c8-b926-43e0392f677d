# Generated by Django 5.2 on 2025-04-27 20:44

import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_historicalmember_account_historicalmember_county_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_name', models.CharField(max_length=255)),
                ('event_date', models.DateField()),
                ('event_end_date', models.DateField(blank=True, null=True)),
                ('event_location', models.CharField(max_length=255)),
                ('event_description', models.TextField(blank=True, null=True)),
                ('registration_fee_normal', models.DecimalField(decimal_places=2, default=100.0, max_digits=10)),
                ('registration_fee_late', models.DecimalField(decimal_places=2, default=115.0, max_digits=10)),
                ('guest_fee', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('late_registration_date', models.DateField(blank=True, null=True)),
                ('max_participants', models.PositiveIntegerField(default=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-event_date'],
            },
        ),
        migrations.AddField(
            model_name='eventregistration',
            name='event',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='registrations', to='core.event'),
        ),
        migrations.AddField(
            model_name='historicaleventregistration',
            name='event',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='core.event'),
        ),
        migrations.CreateModel(
            name='HistoricalEvent',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('event_name', models.CharField(max_length=255)),
                ('event_date', models.DateField()),
                ('event_end_date', models.DateField(blank=True, null=True)),
                ('event_location', models.CharField(max_length=255)),
                ('event_description', models.TextField(blank=True, null=True)),
                ('registration_fee_normal', models.DecimalField(decimal_places=2, default=100.0, max_digits=10)),
                ('registration_fee_late', models.DecimalField(decimal_places=2, default=115.0, max_digits=10)),
                ('guest_fee', models.DecimalField(decimal_places=2, default=50.0, max_digits=10)),
                ('late_registration_date', models.DateField(blank=True, null=True)),
                ('max_participants', models.PositiveIntegerField(default=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical event',
                'verbose_name_plural': 'historical events',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

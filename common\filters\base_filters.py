import warnings
from django_filters import rest_framework as filters
from django.db.models import Q # Import Q for complex queries

class BaseBooleanFilterSet(filters.FilterSet):
    """
    Base FilterSet with custom handling for boolean fields allowing
    'yes', 'no', and 'all' string inputs.
    """
    def filter_boolean_field(self, queryset, name, value):
        """
        Custom filter method for boolean fields.

        Args:
            queryset: The initial queryset.
            name (str): The name of the field being filtered.
            value (str): The value provided for filtering ('yes', 'no', 'all', or other).

        Returns:
            QuerySet: The filtered queryset.
        """
        field_name = name # The 'name' argument directly corresponds to the field name

        # Handle Python boolean values directly
        if isinstance(value, bool):
            print(f"Filtering boolean field '{field_name}' with value: '{value}'")
            print(f"  -> Applying filter: {field_name}={value}")
            return queryset.filter(**{field_name: value})
            
        # For string values, convert to lowercase and strip whitespace
        if value is not None:
            value_str = str(value).lower().strip()
            print(f"Filtering boolean field '{field_name}' with value: '{value_str}'")
            
            if value_str == 'yes':
                print(f"  -> Applying filter: {field_name}=True")
                return queryset.filter(**{field_name: True})
            elif value_str == 'no':
                print(f"  -> Applying filter: {field_name}=False")
                return queryset.filter(**{field_name: False})
            elif value_str == 'all':
                print(f"  -> No boolean filter applied for '{field_name}' (value='all')")
                return queryset # No filtering applied
            elif value_str == 'true':
                print(f"  -> Applying filter: {field_name}=True")
                return queryset.filter(**{field_name: True})
            elif value_str == 'false':
                print(f"  -> Applying filter: {field_name}=False")
                return queryset.filter(**{field_name: False})
            elif value_str == '':
                # Empty string is treated as 'all'
                print(f"  -> Empty value treated as 'all'")
                return queryset
        
        # For None, empty strings, or any other invalid value
        # Use warnings.warn() with explicit stacklevel to ensure warning appears at caller's level
        warning_message = (
            f"Invalid boolean filter value '{value}' for field '{field_name}'. "
            f"Treating as 'all'. Expected 'yes', 'no', or 'all'."
        )
        # Use stacklevel=2 to show the warning at the caller's location rather than this function
        warnings.warn(warning_message, stacklevel=2)
        print(f"  -> Invalid value treated as 'all'")
        return queryset

    class Meta:
        abstract = True # Make this an abstract base class 
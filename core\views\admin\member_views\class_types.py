from rest_framework import status

from common.views import BaseAPIView
from common.views import APIResponse
from core.models import Member
from common.permissions import IsStaffUser


class MembershipClassTypesAPIView(BaseAPIView):
    """
    Get all membership class types
    """
    permission_classes = [IsStaffUser]
    
    def get(self, request, *args, **kwargs):
        # Dynamically retrieve membership class types from the MembershipStatus enum
        membership_classes = [
            {'id': status.value, 'name': status.label}
            for status in Member.MembershipStatus
        ]
        
        return APIResponse(
            data=membership_classes,
            message="Membership class types retrieved successfully",
            status_code=status.HTTP_200_OK
        ) 
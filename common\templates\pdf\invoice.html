<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    {% load humanize %}
    <style>
        @page {
            size: letter;
            margin: 1cm;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 12px;
            line-height: 1.3;
        }
        .header {
            background-color: #000000;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #000;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .header-right {
            text-align: right;
            font-size: 24px;
            font-weight: bold;
        }
        .address {
            margin-top: 10px;
            font-size: 12px;
            color: #333;
            text-align: left;
            margin-left: 53px;
        }
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
        }
        .billed-to {
            width: 50%;
            text-align: left;
        }
        .billed-to-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .invoice-status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            width: 50%;
            margin-left: auto;
            margin-right: auto;
            padding: 5px;
        }
        .invoice-status-label {
            font-weight: bold;
            margin-right: 5px;
        }
        .invoice-status-value {
            color: #000000;
            font-weight: bold;
        }
        .invoice-details {
            width: 50%;
            text-align: right;
        }
        .invoice-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .invoice-details td {
            padding: 3px 0;
            vertical-align: top;
        }
        .invoice-details td:first-child {
            text-align: right;
            padding-right: 10px;
            font-weight: bold;
        }
        .invoice-details td:last-child {
            text-align: right;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 1px solid #000;
            table-layout: fixed;
        }
        .invoice-table th {
            background-color: #fff;
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-weight: bold;
        }
        .invoice-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        .invoice-table .description {
            text-align: left;
        }
        .invoice-table .quantity-col {
            width: 15%;
            overflow: visible;
        }
        .invoice-table .description-col {
            width: 55%;
        }
        .invoice-table .price-col {
            width: 15%;
        }
        .invoice-table .amount-col {
            width: 15%;
        }
        .invoice-table .amount {
            text-align: right;
        }
        .period-row td {
            text-align: left;
            border-left: 1px solid #000;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            border-top: none;
            padding: 8px;
            text-align: left !important;
        }
        .subtotal-row td {
            text-align: right;
            font-weight: bold;
            border: none;
            padding: 8px;
        }
        .total-row td {
            text-align: right;
            font-weight: bold;
            border: none;
            padding: 8px;
        }
        .payment-box {
            border: 1px solid #000;
            padding: 10px;
            width: 150px;
            text-align: center;
            margin-left: auto;
            margin-top: 10px;
            font-size: 12px;
        }
        .payment-box-label {
            color: #666;
            margin-bottom: 5px;
        }
        .payment-box-amount {
            font-weight: bold;
            color: #000;
        }
        .footer {
            margin-top: 60px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }
        .footer-left {
            width: 45%;
        }
        .footer-right {
            width: 45%;
            text-align: right;
        }
        .footer h3 {
            margin-bottom: 5px;
            font-size: 12px;
            font-weight: bold;
        }
        .footer a {
            color: #000;
            text-decoration: underline;
        }
        .subject-reference {
            margin-top: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Mississippi Firefighters Association</h1>
        <div class="header-right">INVOICE</div>
    </div>

    <div class="address">
        P.O. Box 1507<br>
        Brandon, MS 39403-1507
    </div>

    <div class="invoice-info">
        <div class="billed-to">
            <div class="billed-to-label">BILLED TO:</div>
            {{ invoice.payer.name }}<br>
            {% if invoice.billing_address %}
                {{ invoice.billing_address|linebreaks }}
            {% else %}
                {% if invoice.payer.address %}{{ invoice.payer.address }}<br>{% endif %}
                {% if invoice.payer.city or invoice.payer.st or invoice.payer.zip_code %}
                    {{ invoice.payer.city }}{% if invoice.payer.city and invoice.payer.st %}, {% endif %}{{ invoice.payer.st }} {{ invoice.payer.zip_code }}
                {% endif %}
            {% endif %}
        </div>
        <div class="invoice-details">
            <table>
                <tr>
                    <td>INVOICE NUMBER</td>
                    <td>{{ invoice.invoice_number }}</td>
                </tr>
                <tr>
                    <td>INVOICE DATE</td>
                    <td>{{ invoice.date|date:"F d, Y" }}</td>
                </tr>
                <tr>
                    <td>DUE DATE</td>
                    <td>{{ invoice.due_date|date:"F d, Y" }}</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="invoice-status">
        <span class="invoice-status-label">INVOICE STATUS:</span>
        <span class="invoice-status-value">{{ invoice.get_status_display }}</span>
    </div>

    <table class="invoice-table">
        <colgroup>
            <col class="quantity-col">
            <col class="description-col">
            <col class="price-col">
            <col class="amount-col">
        </colgroup>
        <thead>
            <tr>
                <th>QUANTITY</th>
                <th>DESCRIPTION</th>
                <th>UNIT PRICE</th>
                <th>AMOUNT</th>
            </tr>
        </thead>
        <tbody>
            {% for item in items %}
            <tr>
                <td>{{ item.quantity }}</td>
                <td class="description">{{ item.description }}</td>
                <td class="amount">${{ item.unit_price|floatformat:2|intcomma }}</td>
                <td class="amount">${{ item.amount|floatformat:2|intcomma }}</td>
            </tr>
            {% endfor %}

            <tr class="period-row">
                <td colspan="4" style="text-align: left !important;">
                    Covering Period from January 1, {{ invoice.due_date|date:"Y" }} to December 31, {{ invoice.due_date|date:"Y" }}
                </td>
            </tr>
        </tbody>
    </table>

    <table style="width: 100%; border-collapse: collapse;">
        <tr class="subtotal-row">
            <td style="width: 85%; text-align: right;">SUBTOTAL</td>
            <td style="width: 15%; text-align: right;">${{ invoice.total_amount|floatformat:2|intcomma }}</td>
        </tr>
        <tr class="total-row">
            <td style="width: 85%; text-align: right;">TOTAL</td>
            <td style="width: 15%; text-align: right;">${{ invoice.total_amount|floatformat:2|intcomma }}</td>
        </tr>
    </table>

    <div class="payment-box">
        <div class="payment-box-label">PAY THIS<br>AMOUNT</div>
        <div class="payment-box-amount">${{ invoice.total_amount|floatformat:2|intcomma }}</div>
    </div>

    <div class="footer">
        <div class="footer-left">
            <h3>DIRECT ALL INQUIRES TO:</h3>
            Jennifer Williams<br>
            662-542-0047<br>
            email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
            <div class="subject-reference">Subject reference: MFFA Membership</div>
        </div>
        <div class="footer-right">
            <h3>MAKE ALL CHECKS PAYABLE TO:</h3>
            Mississippi Firefighters Association<br>
            P.O. Box 1507<br>
            Brandon, MS 39403-1507
        </div>
    </div>
</body>
</html>

"""
Test cases for User Activity serializers.
"""
import json
from django.test import TestCase
from django.utils import timezone
from datetime import timedelta, datetime
from unittest.mock import patch, Mock
from rest_framework.test import APITestCase
from rest_framework.renderers import J<PERSON><PERSON>enderer
import pytz

from common.models import UserActivity
from core.models import Member
from core.serializers.auth.activity_serializers import UserActivitySerializer


class UserActivitySerializerTests(APITestCase):
    """Test cases for the UserActivitySerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Activity Test User',
            active=True
        )

        # Create user activities with various timestamps
        self.now = timezone.now()

        # Activity just now
        self.recent_activity = UserActivity.objects.create(
            user=self.user,
            description="Recent test activity",
            timestamp=self.now
        )

        # Activity from 30 minutes ago
        self.minutes_ago_activity = UserActivity.objects.create(
            user=self.user,
            description="Minutes ago test activity",
            timestamp=self.now - timedelta(minutes=30)
        )

        # Activity from 3 hours ago
        self.hours_ago_activity = UserActivity.objects.create(
            user=self.user,
            description="Hours ago test activity",
            timestamp=self.now - timedelta(hours=3)
        )

        # Activity from 5 days ago
        self.days_ago_activity = UserActivity.objects.create(
            user=self.user,
            description="Days ago test activity",
            timestamp=self.now - timedelta(days=5)
        )

        # Activity from 2 months ago
        self.months_ago_activity = UserActivity.objects.create(
            user=self.user,
            description="Months ago test activity",
            timestamp=self.now - timedelta(days=60)
        )

        # Activity from 3 years ago
        self.years_ago_activity = UserActivity.objects.create(
            user=self.user,
            description="Years ago test activity",
            timestamp=self.now - timedelta(days=1095)  # Approximately 3 years
        )

        # User that will be deleted for testing null user relationships
        self.user_to_delete = Member.objects.create_user(
            email='<EMAIL>',
            password='deleteme123',
            name='Delete Me User',
            active=True
        )

        self.orphaned_activity = UserActivity.objects.create(
            user=self.user_to_delete,
            description="Orphaned activity",
            timestamp=self.now
        )

    def test_basic_serialization(self):
        """Test basic serialization of a user activity."""
        serializer = UserActivitySerializer(self.recent_activity)
        data = serializer.data

        # Test that all fields are properly serialized
        self.assertEqual(data['id'], self.recent_activity.id)
        self.assertEqual(data['user'], self.user.id)
        self.assertEqual(data['user_email'], self.user.email)
        self.assertEqual(data['user_name'], self.user.name)
        self.assertEqual(data['description'], self.recent_activity.description)
        self.assertIn('timestamp', data)
        self.assertIn('time_ago', data)

    def test_read_only_fields(self):
        """Test that read-only fields are respected."""
        activity_data = {
            'id': 9999,  # This should be ignored
            'user': 9999,  # This should be ignored
            'timestamp': '2023-01-01T00:00:00Z',  # This should be ignored
            'description': 'New description'  # This should be updated
        }

        serializer = UserActivitySerializer(self.recent_activity, data=activity_data, partial=True)
        self.assertTrue(serializer.is_valid())

        updated_activity = serializer.save()

        # The read-only fields should not be changed
        self.assertEqual(updated_activity.id, self.recent_activity.id)
        self.assertEqual(updated_activity.user, self.user)
        self.assertNotEqual(updated_activity.timestamp.strftime('%Y-%m-%dT%H:%M:%SZ'), activity_data['timestamp'])

        # But the description should be updated
        self.assertEqual(updated_activity.description, activity_data['description'])

    def test_time_ago_seconds(self):
        """Test the time_ago calculation for seconds."""
        # Since we can't override the auto_now_add field, we'll directly test the get_time_ago method
        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Seconds ago test activity"
        )

        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now - timedelta(seconds=30)

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Verify the result
            self.assertEqual(time_ago, "30 seconds ago")

    def test_time_ago_minutes(self):
        """Test the time_ago calculation for minutes."""
        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Minutes ago test activity"
        )

        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now - timedelta(minutes=30)

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Verify the result
            self.assertEqual(time_ago, "30 minutes ago")

    def test_time_ago_hours(self):
        """Test the time_ago calculation for hours."""
        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Hours ago test activity"
        )

        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now - timedelta(hours=3)

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Verify the result
            self.assertEqual(time_ago, "3 hours ago")

    def test_time_ago_days(self):
        """Test the time_ago calculation for days."""
        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Days ago test activity"
        )

        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now - timedelta(days=5)

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Verify the result
            self.assertEqual(time_ago, "5 days ago")

    def test_time_ago_months(self):
        """Test the time_ago calculation for months."""
        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Months ago test activity"
        )

        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now - timedelta(days=60)

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Verify the result
            self.assertEqual(time_ago, "2 months ago")

    def test_time_ago_years(self):
        """Test the time_ago calculation for years."""
        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Years ago test activity"
        )

        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now - timedelta(days=1095)  # Approximately 3 years

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Verify the result
            self.assertEqual(time_ago, "3 years ago")

    def test_time_ago_edge_cases(self):
        """Test the time_ago calculation for edge cases."""
        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Edge case test activity"
        )

        # Manually set timestamps for testing
        now = timezone.now()

        # Test with exactly 1 second
        activity.timestamp = now - timedelta(seconds=1)
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now
            self.assertEqual(serializer.get_time_ago(activity), "1 seconds ago")

        # Test with exactly 1 minute
        activity.timestamp = now - timedelta(minutes=1)
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now
            self.assertEqual(serializer.get_time_ago(activity), "1 minutes ago")

        # Test with exactly 1 hour
        activity.timestamp = now - timedelta(hours=1)
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now
            self.assertEqual(serializer.get_time_ago(activity), "1 hours ago")

        # Test with exactly 1 day
        activity.timestamp = now - timedelta(days=1)
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now
            self.assertEqual(serializer.get_time_ago(activity), "1 days ago")

        # Test with exactly 1 month (30 days)
        activity.timestamp = now - timedelta(days=30)
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now
            self.assertEqual(serializer.get_time_ago(activity), "1 months ago")

        # Test with exactly 1 year (365 days)
        activity.timestamp = now - timedelta(days=365)
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now
            self.assertEqual(serializer.get_time_ago(activity), "1 years ago")

    def test_serialization_when_user_deleted(self):
        """Test serialization when the associated user has been deleted."""
        # First verify the activity is properly associated
        self.assertEqual(self.orphaned_activity.user, self.user_to_delete)

        # Now delete the user
        self.user_to_delete.delete()

        # Refresh the activity
        self.orphaned_activity.refresh_from_db()

        # The user should be None
        self.assertIsNone(self.orphaned_activity.user)

        # Serialize the activity
        serializer = UserActivitySerializer(self.orphaned_activity)
        data = serializer.data

        # Check the user fields
        self.assertIsNone(data['user'])
        self.assertIsNone(data['user_email'])
        self.assertIsNone(data['user_name'])

    def test_serialization_handles_very_old_timestamps(self):
        """Test that the serializer handles very old timestamps correctly."""
        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Very old activity"
        )

        # Manually set timestamps for testing
        activity.timestamp = datetime(1970, 1, 1, tzinfo=pytz.UTC)  # Unix epoch
        fixed_now = datetime(2023, 1, 1, tzinfo=pytz.UTC)

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = fixed_now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # This should be many years ago
            self.assertIn('years ago', time_ago)
            years = int(time_ago.split()[0])
            self.assertGreater(years, 40)  # Should be more than 40 years

    def test_serialization_handles_future_timestamps(self):
        """Test that the serializer handles future timestamps correctly."""
        # Create a serializer instance
        serializer = UserActivitySerializer()

        # Create an activity
        activity = UserActivity.objects.create(
            user=self.user,
            description="Future activity"
        )

        # Manually set timestamps for testing
        now = timezone.now()
        activity.timestamp = now + timedelta(days=30)  # 30 days in the future

        # Patch timezone.now() to return our fixed time
        with patch('core.serializers.auth.activity_serializers.timezone.now') as mock_now:
            mock_now.return_value = now

            # Call the method directly
            time_ago = serializer.get_time_ago(activity)

            # Since our calculation is "now - timestamp", this would be negative
            # The serializer should handle this gracefully
            self.assertIn('ago', time_ago)

    def test_bulk_serialization(self):
        """Test serialization of multiple activities."""
        activities = UserActivity.objects.filter(user=self.user).order_by('-timestamp')

        serializer = UserActivitySerializer(activities, many=True)
        data = serializer.data

        # Verify the correct number of items
        self.assertEqual(len(data), activities.count())

        # Verify the order
        timestamps = [item['timestamp'] for item in data]
        self.assertEqual(timestamps, sorted(timestamps, reverse=True))

    def test_json_serialization(self):
        """Test that the serialized data can be properly converted to JSON."""
        serializer = UserActivitySerializer(self.recent_activity)

        # Use DRF's JSONRenderer to convert to JSON
        json_data = JSONRenderer().render(serializer.data)

        # Parse the JSON to verify its format
        parsed_data = json.loads(json_data.decode('utf-8'))

        # Check essential fields
        self.assertEqual(parsed_data['id'], self.recent_activity.id)
        self.assertEqual(parsed_data['user'], self.user.id)
        self.assertEqual(parsed_data['description'], self.recent_activity.description)
        self.assertIn('timestamp', parsed_data)
        self.assertIn('time_ago', parsed_data)

    def test_serialization_with_empty_description(self):
        """Test serialization with an empty description."""
        # Create an activity with empty description instead of null
        # since the model doesn't allow null descriptions
        empty_desc_activity = UserActivity.objects.create(
            user=self.user,
            description="Empty description"
        )

        serializer = UserActivitySerializer(empty_desc_activity)
        data = serializer.data

        # The description should match what we set
        self.assertEqual(data['description'], "Empty description")

    def test_serialization_with_long_description(self):
        """Test serialization with a very long description."""
        long_description = "A" * 1000  # A 1000 character string

        long_desc_activity = UserActivity.objects.create(
            user=self.user,
            description=long_description
        )

        serializer = UserActivitySerializer(long_desc_activity)
        data = serializer.data

        # The long description should be preserved
        self.assertEqual(data['description'], long_description)

    def test_get_user_name_fallback(self):
        """Test that get_user_name falls back to email if name is empty."""
        # Create a user with no name
        no_name_user = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='',  # Empty name
            active=True
        )

        # Create an activity for this user
        no_name_activity = UserActivity.objects.create(
            user=no_name_user,
            description="Activity by user with no name"
        )

        serializer = UserActivitySerializer(no_name_activity)
        data = serializer.data

        # User name should fall back to email
        self.assertEqual(data['user_name'], no_name_user.email)

    def test_performance_with_large_dataset(self):
        """Test serializer performance with a large dataset."""
        # Create a large number of activities
        batch_size = 100
        activities = []

        for i in range(batch_size):
            activity = UserActivity.objects.create(
                user=self.user,
                description=f"Performance test activity {i}",
                timestamp=self.now - timedelta(minutes=i)
            )
            activities.append(activity)

        # Time the serialization
        import time
        start_time = time.time()

        serializer = UserActivitySerializer(activities, many=True)
        data = serializer.data

        end_time = time.time()

        # Verify the correct number of items
        self.assertEqual(len(data), batch_size)

        # Verify the serialization time is reasonable
        # This is a performance sanity check rather than a strict test
        serialization_time = end_time - start_time
        self.assertLess(serialization_time, 2.0)  # Should take less than 2 seconds

    def test_serializer_respects_model_changes(self):
        """Test that the serializer reflects model changes."""
        # Change the user's name
        self.user.name = "Changed Name"
        self.user.save()

        # Serialize the activity again
        serializer = UserActivitySerializer(self.recent_activity)
        data = serializer.data

        # The user_name should reflect the change
        self.assertEqual(data['user_name'], "Changed Name")

        # Change the description
        self.recent_activity.description = "Changed description"
        self.recent_activity.save()

        # Serialize again
        serializer = UserActivitySerializer(self.recent_activity)
        data = serializer.data

        # The description should reflect the change
        self.assertEqual(data['description'], "Changed description")
# Generated by Django 5.2 on 2025-04-05 13:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalpayment',
            name='paypal_fee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='paypal_order_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='paypal_payer_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='paypal_payment_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='paypal_payment_method',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='historicalpayment',
            name='paypal_payment_status',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_fee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_order_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_payer_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_payment_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_payment_method',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='paypal_payment_status',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpayment',
            name='payment_type',
            field=models.CharField(blank=True, choices=[('prepaid', 'Prepaid'), ('collect', 'Collect'), ('checks', 'Checks'), ('cash', 'Cash'), ('paypal', 'PayPal'), ('money_order', 'Money Order')], default='', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_type',
            field=models.CharField(blank=True, choices=[('prepaid', 'Prepaid'), ('collect', 'Collect'), ('checks', 'Checks'), ('cash', 'Cash'), ('paypal', 'PayPal'), ('money_order', 'Money Order')], default='', max_length=20, null=True),
        ),
    ]

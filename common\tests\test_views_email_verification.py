"""
Tests for the email verification views in the common app.

This module contains comprehensive tests for the EmailVerificationView
and ResendVerificationEmailView classes.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from common.models import EmailVerification

User = get_user_model()


class EmailVerificationViewTests(TestCase):
    """Test cases for the EmailVerificationView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User',
            active=False  # User is not active yet
        )

        # Create a verification record
        self.verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )

        # URL for email verification
        self.url = reverse('core:verify-email', args=[str(self.verification.key)])

    @patch('core.views.email_verification.verify_email')
    def test_successful_verification(self, mock_verify_email):
        """Test successful email verification."""
        # Mock the verify_email function to return success
        mock_verify_email.return_value = (self.user, None)

        # Make the request
        response = self.client.post(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data']['email'], self.user.email)
        self.assertIn('successfully verified', response.data['message'])

        # Check that verify_email was called with the correct key
        mock_verify_email.assert_called_once()

    @patch('core.views.email_verification.verify_email')
    def test_failed_verification_invalid_key(self, mock_verify_email):
        """Test failed email verification with an invalid key."""
        # Mock the verify_email function to return an error
        mock_verify_email.return_value = (None, 'Invalid verification key')

        # Make the request
        response = self.client.post(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Invalid verification key')

        # Check that verify_email was called with the correct key
        mock_verify_email.assert_called_once()

    @patch('core.views.email_verification.verify_email')
    def test_failed_verification_already_verified(self, mock_verify_email):
        """Test failed email verification when already verified."""
        # Mock the verify_email function to return an error
        mock_verify_email.return_value = (None, 'Email has already been verified')

        # Make the request
        response = self.client.post(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Email has already been verified')

        # Check that verify_email was called with the correct key
        mock_verify_email.assert_called_once()

    @patch('core.views.email_verification.verify_email')
    def test_failed_verification_expired(self, mock_verify_email):
        """Test failed email verification when the key has expired."""
        # Mock the verify_email function to return an error
        mock_verify_email.return_value = (None, 'Verification link has expired')

        # Make the request
        response = self.client.post(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Verification link has expired')

        # Check that verify_email was called with the correct key
        mock_verify_email.assert_called_once()

    def test_verification_with_nonexistent_key(self):
        """Test email verification with a nonexistent key."""
        # URL with a nonexistent key
        url = reverse('core:verify-email', args=[str(uuid.uuid4())])

        # Make the request
        response = self.client.post(url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Invalid verification link')

    def test_verification_with_invalid_uuid_format(self):
        """Test email verification with an invalid UUID format."""
        # This test is skipped because the URL pattern only accepts valid UUIDs
        # and will raise a NoReverseMatch exception for invalid UUIDs
        pass

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    @patch('core.views.email_verification.verify_email')
    def test_activity_tracking(self, mock_verify_email, mock_create_activity):
        """Test that user activity is tracked for email verification."""
        # Mock the verify_email function to return success
        mock_verify_email.return_value = (self.user, None)

        # Make the request
        self.client.post(self.url)

        # Check if activity tracking was called
        # Note: This might not be called because the user might not be authenticated
        # in the request context during the test
        mock_create_activity.assert_not_called()

    def test_verification_does_not_activate_user(self):
        """Test that email verification does not automatically activate the user."""
        # Make the request
        self.client.post(self.url)

        # Refresh the user from the database
        self.user.refresh_from_db()

        # Check that the user is still not active
        self.assertFalse(self.user.active)


class ResendVerificationEmailViewTests(TestCase):
    """Test cases for the ResendVerificationEmailView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User',
            active=False  # User is not active yet
        )

        # URL for resending verification email
        self.url = reverse('core:resend-verification-email')

    @patch('core.views.email_verification.send_verification_email')
    def test_resend_verification_email_success(self, mock_send_email):
        """Test successful resend of verification email."""
        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('Verification email has been sent', response.data['message'])

        # Check that send_verification_email was called with the correct user
        mock_send_email.assert_called_once_with(self.user)

    def test_resend_verification_email_already_active(self):
        """Test resend verification email when user is already active."""
        # Set the user as active
        self.user.active = True
        self.user.save()

        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('already been verified', response.data['message'])

    def test_resend_verification_email_nonexistent_user(self):
        """Test resend verification email for nonexistent user."""
        # Make the request with a nonexistent email
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should still return 200 for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('If the email exists', response.data['message'])

    def test_resend_verification_email_missing_email(self):
        """Test resend verification email with missing email."""
        # Make the request without an email
        response = self.client.post(self.url, {})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Email address is required', response.data['message'])

    def test_resend_verification_email_empty_email(self):
        """Test resend verification email with an empty email."""
        # Make the request with an empty email
        response = self.client.post(self.url, {'email': ''})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Email address is required', response.data['message'])

    def test_resend_verification_email_invalid_email_format(self):
        """Test resend verification email with an invalid email format."""
        # Make the request with an invalid email format
        response = self.client.post(self.url, {'email': 'not-an-email'})

        # Check the response - should still return 200 for security reasons
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('If the email exists', response.data['message'])

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    @patch('core.views.email_verification.send_verification_email')
    def test_activity_tracking(self, mock_send_email, mock_create_activity):
        """Test that user activity is tracked for resending verification email."""
        # Make the request
        self.client.post(self.url, {'email': '<EMAIL>'})

        # Check if activity tracking was called
        # Note: This might not be called because the user might not be authenticated
        # in the request context during the test
        mock_create_activity.assert_not_called()

    def test_resend_verification_email_method_not_allowed(self):
        """Test that only POST method is allowed for resending verification email."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response - should return 405 Method Not Allowed
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

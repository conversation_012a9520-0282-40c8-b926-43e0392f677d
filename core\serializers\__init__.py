"""
Serializers module for the core app.
"""

# Import auth serializers
from .auth import (
    RegisterSerializer,
    LoginSerializer,
    ChangePasswordSerializer,
    AdminChangePasswordSerializer,
    UserDetailsSerializer
)

# Import member serializers from the renamed file to avoid naming conflicts
from .member_serializers import (
    DepartmentsSerializer,
    MembershipRosterAdminSerializer,
    MemberIdSerializer,
    MergeMembersSerializer
)

# Import public serializers
from .public_serializers import MemberPublicSerializer

# Import event serializers
from .event import (
    EventSerializer,
    EventDetailSerializer,
    EventCreateUpdateSerializer
)

# Import event config serializers
from .event_config_serializer import (
    EventConfigSerializer,
    EventConfigDetailSerializer
)

# Alias DepartmentsSerializer as DepartmentSerializer for backward compatibility
DepartmentSerializer = DepartmentsSerializer

__all__ = [
    # Auth serializers
    'RegisterSerializer',
    'LoginSerializer',
    'ChangePasswordSerializer',
    'AdminChangePasswordSerializer',
    'UserDetailsSerializer',

    # Member serializers
    'DepartmentsSerializer',
    'MembershipRosterAdminSerializer',
    'MemberIdSerializer',
    'MergeMembersSerializer',
    'DepartmentSerializer',  # Alias for DepartmentsSerializer
    'MemberPublicSerializer',

    # Event serializers
    'EventSerializer',
    'EventDetailSerializer',
    'EventCreateUpdateSerializer',

    # Event config serializers
    'EventConfigSerializer',
    'EventConfigDetailSerializer'
]
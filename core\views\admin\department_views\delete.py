from rest_framework import status
from rest_framework.generics import DestroyAPIView

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Department
from core.serializers import DepartmentsSerializer


class DepartmentDeleteAPIView(BaseAPIView, DestroyAPIView):
    """
    Delete a specific department (admin only)
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentsSerializer
    permission_classes = [IsStaffUser]

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        
        return APIResponse(
            data=None,
            message="Department deleted successfully",
            status_code=status.HTTP_204_NO_CONTENT
        ) 
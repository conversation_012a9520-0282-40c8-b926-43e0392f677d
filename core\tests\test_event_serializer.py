"""
Test cases for Event serializers.
"""
from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from rest_framework.exceptions import ValidationError
from rest_framework.test import APITestCase

from core.models.event import Event
from core.models.event_config import EventConfig
from core.serializers.event import (
    EventSerializer,
    EventDetailSerializer,
    EventCreateUpdateSerializer
)


class EventSerializerTests(APITestCase):
    """Test cases for the EventSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create event configuration
        self.config = EventConfig.objects.create(
            name="Test Configuration",
            description="Configuration for testing",
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=True
        )
        
        # Create an event with full details
        self.event = Event.objects.create(
            event_name="Test Event",
            event_date=timezone.now().date() + timedelta(days=10),
            event_end_date=timezone.now().date() + timedelta(days=12),
            event_location="Test Location",
            event_description="Test Description",
            registration_fee_normal=Decimal('90.00'),
            registration_fee_late=Decimal('105.00'),
            guest_fee=Decimal('45.00'),
            late_registration_date=timezone.now().date() + timedelta(days=3),
            max_participants=150,
            config=self.config,
            is_active=True
        )
        
        # Create a minimal event using defaults from config
        self.minimal_event = Event.objects.create(
            event_name="Minimal Event",
            event_date=timezone.now().date() + timedelta(days=20),
            event_location="Minimal Location",
            config=self.config
        )
        
        # Event data for serializer testing
        self.event_data = {
            'event_name': 'New Test Event',
            'event_date': (timezone.now().date() + timedelta(days=15)).isoformat(),
            'event_end_date': (timezone.now().date() + timedelta(days=16)).isoformat(),
            'event_location': 'New Test Location',
            'event_description': 'New Test Description',
            'registration_fee_normal': '110.00',
            'registration_fee_late': '125.00',
            'guest_fee': '55.00',
            'late_registration_date': (timezone.now().date() + timedelta(days=8)).isoformat(),
            'max_participants': 120,
            'is_active': True,
            'config': self.config.id
        }
    
    def test_event_serialization(self):
        """Test serialization of an event."""
        serializer = EventSerializer(self.event)
        data = serializer.data
        
        # Test basic fields
        self.assertEqual(data['event_name'], self.event.event_name)
        self.assertEqual(data['event_location'], self.event.event_location)
        self.assertEqual(data['event_description'], self.event.event_description)
        self.assertEqual(Decimal(data['registration_fee_normal']), self.event.registration_fee_normal)
        self.assertEqual(Decimal(data['registration_fee_late']), self.event.registration_fee_late)
        self.assertEqual(Decimal(data['guest_fee']), self.event.guest_fee)
        self.assertEqual(data['max_participants'], self.event.max_participants)
        self.assertEqual(data['config'], self.event.config.id)
        
        # Test read-only fields
        self.assertIn('total_registrations', data)
        self.assertIn('is_late_registration', data)
        self.assertIn('spots_remaining', data)
        self.assertIn('is_at_capacity', data)
        self.assertIn('created_at', data)
        self.assertIn('updated_at', data)
    
    def test_minimal_event_serialization(self):
        """Test serialization of an event with minimal data."""
        serializer = EventSerializer(self.minimal_event)
        data = serializer.data
        
        # Test that fields using defaults from config are correctly serialized
        self.assertEqual(data['event_name'], self.minimal_event.event_name)
        self.assertEqual(data['event_location'], self.minimal_event.event_location)
        
        # These should match the values from the config or calculated defaults
        self.assertEqual(Decimal(data['registration_fee_normal']), self.config.registration_fee_normal)
        self.assertEqual(Decimal(data['registration_fee_late']), self.config.registration_fee_late)
        self.assertEqual(Decimal(data['guest_fee']), self.config.guest_fee)
        self.assertEqual(data['max_participants'], self.config.default_max_participants)
        
        # There should be a calculated late_registration_date
        self.assertIsNotNone(data['late_registration_date'])
    
    def test_serialization_with_null_optional_fields(self):
        """Test serialization of an event with null optional fields."""
        # Create an event with some null fields
        event_with_nulls = Event.objects.create(
            event_name="Event With Nulls",
            event_date=timezone.now().date() + timedelta(days=30),
            event_location="Null Test Location",
            event_end_date=None,
            event_description=None,
            is_active=True
        )
        
        serializer = EventSerializer(event_with_nulls)
        data = serializer.data
        
        # Test that null fields are correctly serialized
        self.assertEqual(data['event_name'], event_with_nulls.event_name)
        self.assertEqual(data['event_location'], event_with_nulls.event_location)
        self.assertIsNone(data['event_end_date'])
        self.assertIsNone(data['event_description'])
        
        # The serializer should handle null values for calculated fields
        self.assertEqual(data['total_registrations'], 0)
        self.assertEqual(data['spots_remaining'], event_with_nulls.max_participants)


class EventDetailSerializerTests(APITestCase):
    """Test cases for the EventDetailSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create event configuration
        self.config = EventConfig.objects.create(
            name="Detail Test Configuration",
            description="Configuration for detail testing",
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=True
        )
        
        # Create an event using the config
        self.event = Event.objects.create(
            event_name="Detail Test Event",
            event_date=timezone.now().date() + timedelta(days=10),
            event_location="Detail Test Location",
            config=self.config
        )
        
        # Create an event with no config, using the active config
        self.event_no_config = Event.objects.create(
            event_name="No Config Event",
            event_date=timezone.now().date() + timedelta(days=15),
            event_location="No Config Location"
        )
    
    def test_detail_serialization_with_config(self):
        """Test detailed serialization of an event with explicit config."""
        serializer = EventDetailSerializer(self.event)
        data = serializer.data
        
        # Check that all fields from the base serializer are included
        self.assertEqual(data['event_name'], self.event.event_name)
        self.assertEqual(data['event_location'], self.event.event_location)
        
        # Check for the additional config_details field
        self.assertIn('config_details', data)
        
        # Verify config_details contains the expected data
        config_details = data['config_details']
        self.assertEqual(config_details['name'], self.config.name)
        self.assertEqual(config_details['description'], self.config.description)
        self.assertEqual(Decimal(config_details['registration_fee_normal']), self.config.registration_fee_normal)
        self.assertEqual(Decimal(config_details['registration_fee_late']), self.config.registration_fee_late)
        self.assertEqual(Decimal(config_details['guest_fee']), self.config.guest_fee)
    
    def test_detail_serialization_with_effective_config(self):
        """Test detailed serialization of an event using effective config."""
        serializer = EventDetailSerializer(self.event_no_config)
        data = serializer.data
        
        # Check that config_details contains data from the active config
        self.assertIn('config_details', data)
        
        config_details = data['config_details']
        active_config = EventConfig.get_active_config()
        
        self.assertEqual(config_details['name'], active_config.name)
        self.assertEqual(Decimal(config_details['registration_fee_normal']), active_config.registration_fee_normal)
        
        # The event should still have the correct fields
        self.assertEqual(data['event_name'], self.event_no_config.event_name)
        self.assertEqual(data['event_location'], self.event_no_config.event_location)


class EventCreateUpdateSerializerTests(APITestCase):
    """Test cases for the EventCreateUpdateSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create event configuration
        self.config = EventConfig.objects.create(
            name="Create Test Config",
            description="Configuration for create/update testing",
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=True
        )
        
        # Create an event for update testing
        self.existing_event = Event.objects.create(
            event_name="Existing Event",
            event_date=timezone.now().date() + timedelta(days=10),
            event_location="Existing Location",
            config=self.config
        )
        
        # Valid data for event creation
        self.valid_event_data = {
            'event_name': 'New Test Event',
            'event_date': (timezone.now().date() + timedelta(days=20)).isoformat(),
            'event_end_date': (timezone.now().date() + timedelta(days=21)).isoformat(),
            'event_location': 'New Test Location',
            'event_description': 'New Test Description',
            'registration_fee_normal': '110.00',
            'registration_fee_late': '125.00',
            'guest_fee': '55.00',
            'max_participants': 120,
            'is_active': True,
            'config': self.config.id
        }
        
        # Data with invalid dates (end before start)
        self.invalid_dates_data = self.valid_event_data.copy()
        self.invalid_dates_data.update({
            'event_end_date': (timezone.now().date() + timedelta(days=15)).isoformat(),
        })
        
        # Data with invalid late registration date (after event)
        self.invalid_late_reg_data = self.valid_event_data.copy()
        self.invalid_late_reg_data.update({
            'late_registration_date': (timezone.now().date() + timedelta(days=25)).isoformat(),
        })
    
    def test_create_event_with_valid_data(self):
        """Test creating an event with valid data."""
        serializer = EventCreateUpdateSerializer(data=self.valid_event_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the event
        event = serializer.save()
        
        # Verify fields were saved correctly
        self.assertEqual(event.event_name, self.valid_event_data['event_name'])
        self.assertEqual(event.event_location, self.valid_event_data['event_location'])
        self.assertEqual(event.event_description, self.valid_event_data['event_description'])
        self.assertEqual(str(event.registration_fee_normal), self.valid_event_data['registration_fee_normal'])
        self.assertEqual(str(event.registration_fee_late), self.valid_event_data['registration_fee_late'])
        self.assertEqual(str(event.guest_fee), self.valid_event_data['guest_fee'])
        self.assertEqual(event.max_participants, self.valid_event_data['max_participants'])
        self.assertEqual(event.is_active, self.valid_event_data['is_active'])
        self.assertEqual(event.config.id, self.valid_event_data['config'])
    
    def test_update_event_with_partial_data(self):
        """Test updating an event with partial data."""
        update_data = {
            'event_name': 'Updated Event Name',
            'event_description': 'Updated description',
            'registration_fee_normal': '95.00'
        }
        
        serializer = EventCreateUpdateSerializer(
            self.existing_event, 
            data=update_data, 
            partial=True
        )
        self.assertTrue(serializer.is_valid())
        
        # Save the updated event
        updated_event = serializer.save()
        
        # Verify updated fields
        self.assertEqual(updated_event.event_name, update_data['event_name'])
        self.assertEqual(updated_event.event_description, update_data['event_description'])
        self.assertEqual(str(updated_event.registration_fee_normal), update_data['registration_fee_normal'])
        
        # Verify fields not in update_data remain unchanged
        self.assertEqual(updated_event.event_location, self.existing_event.event_location)
        self.assertEqual(updated_event.event_date, self.existing_event.event_date)
        self.assertEqual(updated_event.config, self.existing_event.config)
    
    def test_create_with_invalid_dates(self):
        """Test validation fails when end date is before start date."""
        serializer = EventCreateUpdateSerializer(data=self.invalid_dates_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('event_end_date', serializer.errors)
    
    def test_create_with_invalid_late_registration(self):
        """Test validation fails when late registration is after event date."""
        serializer = EventCreateUpdateSerializer(data=self.invalid_late_reg_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('late_registration_date', serializer.errors)
    
    def test_create_without_config(self):
        """Test creating an event without specifying a config."""
        # Remove config from valid data
        no_config_data = self.valid_event_data.copy()
        no_config_data.pop('config')
        
        serializer = EventCreateUpdateSerializer(data=no_config_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the event
        event = serializer.save()
        
        # Verify the event was created correctly
        self.assertEqual(event.event_name, no_config_data['event_name'])
        
        # The event should use the default active config values
        # This is tested indirectly by checking that the event has values
        self.assertIsNotNone(event.registration_fee_normal)
        self.assertIsNotNone(event.registration_fee_late)
        self.assertIsNotNone(event.guest_fee)
        self.assertIsNotNone(event.max_participants)
        self.assertIsNotNone(event.late_registration_date)
    
    def test_minimal_required_fields(self):
        """Test creating an event with only required fields."""
        minimal_data = {
            'event_name': 'Minimal Event',
            'event_date': (timezone.now().date() + timedelta(days=30)).isoformat(),
            'event_location': 'Minimal Location'
        }
        
        serializer = EventCreateUpdateSerializer(data=minimal_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the event
        event = serializer.save()
        
        # Verify minimal fields were saved
        self.assertEqual(event.event_name, minimal_data['event_name'])
        self.assertEqual(event.event_location, minimal_data['event_location'])
        
        # The rest should have default values from the active config
        active_config = EventConfig.get_active_config()
        self.assertEqual(event.registration_fee_normal, active_config.registration_fee_normal)
        self.assertEqual(event.registration_fee_late, active_config.registration_fee_late)
        self.assertEqual(event.guest_fee, active_config.guest_fee)
        self.assertEqual(event.max_participants, active_config.default_max_participants)
        
        # Late registration date should be calculated
        expected_late_date = event.event_date - timedelta(days=active_config.days_until_late_registration)
        self.assertEqual(event.late_registration_date, expected_late_date) 
# Generated by Django 5.2 on 2025-05-04 20:04

import django.core.validators
import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_event_eventregistration_event_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Unique name for this configuration', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of this configuration', null=True)),
                ('registration_fee_normal', models.DecimalField(decimal_places=2, default=100.0, help_text='Default fee for normal registration', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('registration_fee_late', models.DecimalField(decimal_places=2, default=115.0, help_text='Default fee for late registration', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('guest_fee', models.DecimalField(decimal_places=2, default=50.0, help_text='Default fee for guests', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('default_max_participants', models.PositiveIntegerField(default=100, help_text='Default maximum number of participants for an event')),
                ('days_until_late_registration', models.PositiveIntegerField(default=7, help_text='Default number of days before event when late registration begins')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this configuration is currently active. Only one configuration can be active at a time.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Event Configuration',
                'verbose_name_plural': 'Event Configurations',
            },
        ),
        migrations.AlterField(
            model_name='event',
            name='guest_fee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='event',
            name='max_participants',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='event',
            name='registration_fee_late',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='event',
            name='registration_fee_normal',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='historicalevent',
            name='guest_fee',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='historicalevent',
            name='max_participants',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalevent',
            name='registration_fee_late',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='historicalevent',
            name='registration_fee_normal',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='event',
            name='config',
            field=models.ForeignKey(blank=True, help_text='Configuration to use for this event. If not specified, the active configuration will be used.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='events', to='core.eventconfig'),
        ),
        migrations.AddField(
            model_name='historicalevent',
            name='config',
            field=models.ForeignKey(blank=True, db_constraint=False, help_text='Configuration to use for this event. If not specified, the active configuration will be used.', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='core.eventconfig'),
        ),
        migrations.CreateModel(
            name='HistoricalEventConfig',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='Unique name for this configuration', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Description of this configuration', null=True)),
                ('registration_fee_normal', models.DecimalField(decimal_places=2, default=100.0, help_text='Default fee for normal registration', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('registration_fee_late', models.DecimalField(decimal_places=2, default=115.0, help_text='Default fee for late registration', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('guest_fee', models.DecimalField(decimal_places=2, default=50.0, help_text='Default fee for guests', max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('default_max_participants', models.PositiveIntegerField(default=100, help_text='Default maximum number of participants for an event')),
                ('days_until_late_registration', models.PositiveIntegerField(default=7, help_text='Default number of days before event when late registration begins')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this configuration is currently active. Only one configuration can be active at a time.')),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical Event Configuration',
                'verbose_name_plural': 'historical Event Configurations',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

from django.urls import path

# Import public views
from core.views import (
    MembershipRosterPublicListView,
    MembershipRosterPublicDetailView,
    MembershipClassTypesPublicAPIView,
    DepartmentPublicListView,
    DepartmentPublicDetailView,
    DepartmentPublicMembersAPIView,
)

# Public URLs
public_urlpatterns = [
    # Member URLs
    path('members/', MembershipRosterPublicListView.as_view(), name='public-member-list'),
    path('member-profile/', MembershipRosterPublicDetailView.as_view(), name='public-member-detail'),
    path('membership-classes/', MembershipClassTypesPublicAPIView.as_view(), name='public-membership-classes'),

    # Department URLs
    path('departments/', DepartmentPublicListView.as_view(), name='public-department-list'),
    path('departments/<int:pk>/', DepartmentPublicDetailView.as_view(), name='public-department-detail'),
    path('departments/<int:pk>/members/', DepartmentPublicMembersAPIView.as_view(), name='public-department-members'),
]
"""
Tests for Member model performance.

This module contains tests that focus on performance optimization techniques
and transaction behavior for the Member model.
"""
import time
from unittest.mock import patch
from contextlib import contextmanager

from django.test import TestCase, TransactionTestCase
from django.db import transaction, connection
from django.db.models import Prefetch, Count
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta

from core.models import Department, Member
from common.models import EmailVerification, PasswordReset, UserActivity

User = get_user_model()


class MemberPerformanceTests(TestCase):
    """Test cases for Member model performance optimization."""

    def setUp(self):
        """Set up test data."""
        # Create departments
        self.dept1 = Department.objects.create(
            name='Performance Department 1',
            department_city='Performance City 1',
            department_state='MS'
        )

        self.dept2 = Department.objects.create(
            name='Performance Department 2',
            department_city='Performance City 2',
            department_state='MS'
        )

        # Create a larger set of users for testing performance
        self.users = []
        for i in range(10):
            department = self.dept1 if i % 2 == 0 else self.dept2
            user = User.objects.create_user(
                email=f'perf_user{i}@example.com',
                password='password123',
                name=f'Performance User {i}',
                department=department,
                membership_class=Member.MembershipStatus.MEMBER,
                role=Member.Role.VOLUNTEER if i % 3 == 0 else Member.Role.CAREER,
                gender=Member.Gender.MALE if i % 2 == 0 else Member.Gender.FEMALE,
                active=True,
                membership_active=i % 4 != 0
            )
            self.users.append(user)

            # Create some related objects for select_related and prefetch_related testing
            if i % 3 == 0:
                # Create email verification
                EmailVerification.objects.create(
                    user=user,
                    verified=i % 2 == 0,
                    expires_at=timezone.now() + timedelta(days=30)
                )

            if i % 4 == 0:
                # Create password reset
                PasswordReset.objects.create(
                    user=user,
                    used=False,
                    expires_at=timezone.now() + timedelta(hours=24)
                )

            # Create user activities
            for j in range(i % 3 + 1):  # 1-3 activities per user
                UserActivity.objects.create(
                    user=user,
                    description=f'Activity {j} for User {i}'
                )

    @contextmanager
    def assert_query_count(self, expected_count):
        """
        Context manager to count the number of queries executed in a block of code.

        Args:
            expected_count: The expected number of queries
        """
        initial_count = len(connection.queries)
        yield
        final_count = len(connection.queries)
        executed_queries = final_count - initial_count
        self.assertEqual(
            executed_queries,
            expected_count,
            f"Expected {expected_count} queries but got {executed_queries}"
        )

    def test_select_related_optimization(self):
        """Test select_related optimization for reducing queries."""
        # Reset query count
        connection.queries_log.clear()

        # Force DEBUG to True to track queries
        with self.settings(DEBUG=True):
            # Without select_related, accessing department requires a separate query for each user
            with self.assert_query_count(1 + len(self.users)):  # 1 for initial query + N for departments
                users = User.objects.all()
                department_names = [user.department.name for user in users]

            connection.queries_log.clear()

            # With select_related, all data is fetched in a single query
            with self.assert_query_count(1):  # Only 1 query
                users = User.objects.select_related('department').all()
                department_names = [user.department.name for user in users]

    def test_prefetch_related_optimization(self):
        """Test prefetch_related optimization for reducing queries."""
        # Reset query count
        connection.queries_log.clear()

        # Force DEBUG to True to track queries
        with self.settings(DEBUG=True):
            # Without prefetch_related, accessing related activities requires a separate query for each user
            with self.assert_query_count(1 + len(self.users)):  # 1 for users + N for activities
                users = User.objects.all()
                activities_count = [user.activities.count() for user in users]

            connection.queries_log.clear()

            # With prefetch_related, only 2 queries are needed
            with self.assert_query_count(2):  # 1 for users + 1 for all activities
                users = User.objects.prefetch_related('activities').all()
                activities_count = [user.activities.count() for user in users]

    def test_combined_optimizations(self):
        """Test combining select_related and prefetch_related optimizations."""
        # Reset query count
        connection.queries_log.clear()

        # Force DEBUG to True to track queries
        with self.settings(DEBUG=True):
            # Using both select_related and prefetch_related
            with self.assert_query_count(2):  # 1 for users with departments + 1 for activities
                users = User.objects.select_related('department').prefetch_related('activities').all()

                # Access both department and activities
                for user in users:
                    dept_name = user.department.name
                    activities = list(user.activities.all())

    def test_deferred_loading(self):
        """Test deferred loading of fields."""
        # Reset query count
        connection.queries_log.clear()

        # Force DEBUG to True to track queries
        with self.settings(DEBUG=True):
            with self.assert_query_count(1):
                # Load all fields
                users = User.objects.all()
                basic_info = [(user.id, user.email) for user in users]

            connection.queries_log.clear()

            with self.assert_query_count(1):
                # Only load specific fields
                users = User.objects.only('id', 'email').all()
                basic_info = [(user.id, user.email) for user in users]

                # Accessing deferred field triggers additional query - not easily testable
                # first_user = users[0]
                # name = first_user.name  # This would trigger another query

    def test_values_performance(self):
        """Test performance of values() and values_list()."""
        # Reset query count
        connection.queries_log.clear()

        # Force DEBUG to True to track queries
        with self.settings(DEBUG=True):
            with self.assert_query_count(1):
                # Using values() for optimized query
                users_dict = User.objects.values('id', 'email', 'name').all()
                user_data = list(users_dict)

            connection.queries_log.clear()

            with self.assert_query_count(1):
                # Using values_list() for even more optimized query
                emails = User.objects.values_list('email', flat=True).all()
                email_list = list(emails)

    def test_bulk_operations_performance(self):
        """Test performance of bulk operations."""
        # Reset query count
        connection.queries_log.clear()

        # Force DEBUG to True to track queries
        with self.settings(DEBUG=True):
            # Create multiple users one by one (inefficient)
            with self.assert_query_count(6):  # 6 separate queries (3 inserts + additional queries)
                for i in range(3):
                    User.objects.create(
                        email=f'bulk_test_{i}@example.com',
                        name=f'Bulk Test User {i}'
                    )

            connection.queries_log.clear()

            # Create multiple users using bulk_create (efficient)
            with self.assert_query_count(1):  # Single insert query
                User.objects.bulk_create([
                    User(
                        email=f'bulk_create_{i}@example.com',
                        name=f'Bulk Create User {i}'
                    )
                    for i in range(3)
                ])

            connection.queries_log.clear()

            # Update multiple users one by one (inefficient)
            users_to_update = User.objects.filter(email__startswith='bulk_create_')[:3]
            with self.assert_query_count(7):  # 1 select + 6 queries for updates (2 per user)
                for i, user in enumerate(users_to_update):
                    user.name = f'Updated Bulk User {i}'
                    user.save()

            connection.queries_log.clear()

            # Update multiple users using bulk_update (efficient)
            users_to_update = list(User.objects.filter(email__startswith='bulk_test_')[:3])
            for i, user in enumerate(users_to_update):
                user.name = f'Bulk Updated User {i}'

            with self.assert_query_count(1):  # Just 1 update query (no select needed)
                User.objects.bulk_update(users_to_update, ['name'])


class MemberTransactionTests(TransactionTestCase):
    """Test cases for Member model transaction behavior."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Transaction Test Department',
            department_city='Transaction City',
            department_state='MS'
        )

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Transaction Test User',
            department=self.department
        )

    def test_successful_transaction(self):
        """Test a successful transaction."""
        initial_count = User.objects.count()

        with transaction.atomic():
            User.objects.create_user(
                email='<EMAIL>',
                password='password123',
                name='Success User'
            )

        # Transaction should be committed
        self.assertEqual(User.objects.count(), initial_count + 1)
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())

    def test_failed_transaction(self):
        """Test a failed transaction rolls back changes."""
        initial_count = User.objects.count()

        try:
            with transaction.atomic():
                # Create a valid user
                User.objects.create_user(
                    email='<EMAIL>',
                    password='password123',
                    name='Valid User'
                )

                # Create a duplicate email user to trigger exception
                User.objects.create_user(
                    email='<EMAIL>',  # Duplicate email
                    password='password123',
                    name='Duplicate Email User'
                )
        except:
            pass  # Exception is expected

        # Transaction should be rolled back
        self.assertEqual(User.objects.count(), initial_count)
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())

    def test_savepoint_rollback(self):
        """Test savepoint and rollback within transaction."""
        initial_count = User.objects.count()

        with transaction.atomic():
            # Create first user
            User.objects.create_user(
                email='<EMAIL>',
                password='password123',
                name='First Save User'
            )

            # Create a savepoint
            savepoint = transaction.savepoint()

            # Create second user
            User.objects.create_user(
                email='<EMAIL>',
                password='password123',
                name='Second Save User'
            )

            # Rollback to savepoint
            transaction.savepoint_rollback(savepoint)

        # First user should be committed, second user should be rolled back
        self.assertEqual(User.objects.count(), initial_count + 1)
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())

    def test_nested_transactions(self):
        """Test nested transactions."""
        initial_count = User.objects.count()

        with transaction.atomic():
            # Outer transaction
            User.objects.create_user(
                email='<EMAIL>',
                password='password123',
                name='Outer User'
            )

            try:
                with transaction.atomic():
                    # Inner transaction
                    User.objects.create_user(
                        email='<EMAIL>',
                        password='password123',
                        name='Inner User'
                    )

                    # Raise exception to rollback inner transaction
                    raise ValueError("Rolling back inner transaction")
            except ValueError:
                pass  # Expected exception

        # Outer transaction should be committed, inner transaction should be rolled back
        self.assertEqual(User.objects.count(), initial_count + 1)
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())

    @patch('core.models.user.Member.save')
    def test_transaction_on_exception(self, mock_save):
        """Test transaction behavior on exception."""
        mock_save.side_effect = Exception("Save failed")

        initial_count = User.objects.count()

        try:
            with transaction.atomic():
                User.objects.create(
                    email='<EMAIL>',
                    name='Exception User'
                )
        except Exception:
            pass  # Expected exception

        # No users should be added
        self.assertEqual(User.objects.count(), initial_count)
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())


class MemberConcurrencyTests(TransactionTestCase):
    """Test cases for Member model concurrency behavior."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Concurrency Test User'
        )

    def test_concurrent_updates(self):
        """Test handling of concurrent updates."""
        # Get two separate instances of the same user
        user1 = User.objects.get(pk=self.user.pk)
        user2 = User.objects.get(pk=self.user.pk)

        # Update first instance
        user1.name = 'Updated By User 1'
        user1.save()

        # Update second instance
        user2.name = 'Updated By User 2'
        user2.save()

        # Last save wins
        refreshed_user = User.objects.get(pk=self.user.pk)
        self.assertEqual(refreshed_user.name, 'Updated By User 2')

    def test_select_for_update(self):
        """Test select_for_update for pessimistic locking."""
        # This test simulates the behavior, but doesn't actually test
        # concurrent access which would require multiple threads/processes

        with transaction.atomic():
            # Lock the row
            locked_user = User.objects.select_for_update().get(pk=self.user.pk)

            # Update the locked user
            locked_user.name = 'Updated With Lock'
            locked_user.save()

            # In a real concurrent scenario, another transaction would be
            # blocked here until our transaction completes

        # Verify update
        refreshed_user = User.objects.get(pk=self.user.pk)
        self.assertEqual(refreshed_user.name, 'Updated With Lock')


class MemberCachingTests(TestCase):
    """Test cases for Member model caching strategies."""

    def setUp(self):
        """Set up test data."""
        cache.clear()

        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Caching Test User'
        )

    def tearDown(self):
        """Clean up after tests."""
        cache.clear()

    def test_manual_caching(self):
        """Test manual caching of user data."""
        # Cache user data
        cache_key = f'user_{self.user.pk}'
        cache.set(cache_key, {'name': self.user.name, 'email': self.user.email}, timeout=300)

        # Retrieve from cache
        cached_data = cache.get(cache_key)
        self.assertIsNotNone(cached_data)
        self.assertEqual(cached_data['name'], self.user.name)
        self.assertEqual(cached_data['email'], self.user.email)

        # Update user
        self.user.name = 'Updated Cached User'
        self.user.save()

        # Cache should have old data
        cached_data = cache.get(cache_key)
        self.assertEqual(cached_data['name'], 'Caching Test User')

        # Update cache
        cache.set(cache_key, {'name': self.user.name, 'email': self.user.email}, timeout=300)

        # Cache should have new data
        cached_data = cache.get(cache_key)
        self.assertEqual(cached_data['name'], 'Updated Cached User')

    def test_cache_invalidation(self):
        """Test cache invalidation on user updates."""
        # Cache user data
        cache_key = f'user_{self.user.pk}'
        cache.set(cache_key, {'name': self.user.name, 'email': self.user.email}, timeout=300)

        # Update user and invalidate cache
        self.user.name = 'Invalidated Cache User'
        self.user.save()
        cache.delete(cache_key)

        # Cache should be empty
        cached_data = cache.get(cache_key)
        self.assertIsNone(cached_data)

        # Refill cache
        cache.set(cache_key, {'name': self.user.name, 'email': self.user.email}, timeout=300)

        # Cache should have new data
        cached_data = cache.get(cache_key)
        self.assertEqual(cached_data['name'], 'Invalidated Cache User')
"""
Tests for the basic functionality of the BaseAPIView class in the common app.

This module contains tests for the core response methods of the BaseAPIView class.
"""
from django.test import TestCase, RequestFactory
from rest_framework import status
from rest_framework.response import Response

from common.views import BaseAPIView


class MockView(BaseAPIView):
    """Mock view for testing BaseAPIView."""
    
    def get(self, request):
        """Return a success response."""
        return self.success_response(data={'test': 'data'})
    
    def post(self, request):
        """Return an error response."""
        return self.error_response(message='Error occurred')
    
    def put(self, request):
        """Return a regular DRF Response to test finalize_response."""
        return Response({'test': 'data'})
    
    def delete(self, request):
        """Return a regular DRF Response with error status to test finalize_response."""
        return Response({'error': 'Something went wrong'}, status=status.HTTP_400_BAD_REQUEST)
    
    def patch(self, request):
        """Return a success response with custom status code."""
        return self.success_response(
            data={'created': 'data'},
            message='Resource created',
            status=status.HTTP_201_CREATED
        )


class BaseAPIViewBasicTests(TestCase):
    """Test cases for the basic functionality of the BaseAPIView class."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()
        
        # Create a view instance
        self.view = MockView.as_view()

    def test_success_response_method(self):
        """Test the success_response method."""
        # Create a GET request
        request = self.factory.get('/')
        
        # Call the view
        response = self.view(request)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Operation completed successfully')
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {'test': 'data'})

    def test_error_response_method(self):
        """Test the error_response method."""
        # Create a POST request
        request = self.factory.post('/')
        
        # Call the view
        response = self.view(request)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['message'], 'Error occurred')
        self.assertEqual(response.data['success'], False)
        self.assertIsNone(response.data['data'])

    def test_finalize_response_with_regular_response_success(self):
        """Test finalize_response with a regular DRF Response (success)."""
        # Create a PUT request
        request = self.factory.put('/')
        
        # Call the view
        response = self.view(request)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Operation completed successfully')
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {'test': 'data'})

    def test_finalize_response_with_regular_response_error(self):
        """Test finalize_response with a regular DRF Response (error)."""
        # Create a DELETE request
        request = self.factory.delete('/')
        
        # Call the view
        response = self.view(request)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['message'], 'error: Something went wrong')
        self.assertEqual(response.data['success'], False)
        self.assertIsNone(response.data['data'])

    def test_success_response_with_custom_status(self):
        """Test success_response with a custom status code."""
        # Create a PATCH request
        request = self.factory.patch('/')
        
        # Call the view
        response = self.view(request)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['message'], 'Resource created')
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {'created': 'data'})

    def test_finalize_response_with_api_response(self):
        """Test finalize_response with an already formatted APIResponse."""
        # Create a view instance that returns an APIResponse directly
        class DirectAPIResponseView(BaseAPIView):
            def get(self, request):
                from common.views import APIResponse
                return APIResponse(
                    message='Direct APIResponse',
                    data={'direct': 'data'},
                    status_code=status.HTTP_200_OK
                )
        
        # Create a view
        view = DirectAPIResponseView.as_view()
        
        # Create a request
        request = self.factory.get('/')
        
        # Call the view
        response = view(request)
        
        # Check the response - should be unchanged
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Direct APIResponse')
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {'direct': 'data'})

    def test_finalize_response_preserves_headers(self):
        """Test that finalize_response preserves headers from the original response."""
        # Create a view instance that returns a Response with headers
        class HeadersView(BaseAPIView):
            def get(self, request):
                response = Response({'test': 'data'})
                response['X-Custom-Header'] = 'Custom Value'
                return response
        
        # Create a view
        view = HeadersView.as_view()
        
        # Create a request
        request = self.factory.get('/')
        
        # Call the view
        response = view(request)
        
        # Check the response - should have the custom header
        self.assertEqual(response['X-Custom-Header'], 'Custom Value')
        
        # And should also be formatted as an APIResponse
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {'test': 'data'})

    def test_finalize_response_with_empty_response_data(self):
        """Test finalize_response with an empty response data."""
        # Create a view instance that returns an empty Response
        class EmptyResponseView(BaseAPIView):
            def get(self, request):
                return Response({})
        
        # Create a view
        view = EmptyResponseView.as_view()
        
        # Create a request
        request = self.factory.get('/')
        
        # Call the view
        response = view(request)
        
        # Check the response - should be formatted as an APIResponse
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], {})

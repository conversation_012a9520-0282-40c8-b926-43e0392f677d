"""
Tests for the generate_fake_members management command.
"""
import datetime
from io import <PERSON><PERSON>
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.core.management import call_command
from django.db import IntegrityError
from django.utils import timezone
from core.models import Member, Department
from core.management.commands.generate_fake_members import Command


class GenerateFakeMembersCommandTests(TestCase):
    """Test cases for the generate_fake_members management command."""

    def setUp(self):
        """Set up test data."""
        # Create a test department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Initial count of members
        self.initial_member_count = Member.objects.count()

    def test_command_with_default_count(self):
        """Test the command with default count (100)."""
        # Redirect stdout to capture output
        out = StringIO()

        # Mock Faker to avoid random data and speed up tests
        with patch('core.management.commands.generate_fake_members.Faker') as mock_faker:
            # Setup mock faker instance
            mock_faker_instance = MagicMock()
            mock_faker.return_value = mock_faker_instance

            # Mock unique email generator
            mock_faker_instance.unique.email.side_effect = [f'test{i}@example.com' for i in range(100)]

            # Mock other faker methods
            mock_faker_instance.first_name.return_value = 'Test'
            mock_faker_instance.last_name.return_value = 'User'
            mock_faker_instance.random_letter.return_value = 'A'
            mock_faker_instance.street_address.return_value = '123 Test St'
            mock_faker_instance.city.return_value = 'Test City'
            mock_faker_instance.state_abbr.return_value = 'MS'
            mock_faker_instance.zipcode.return_value = '12345'
            mock_faker_instance.phone_number.return_value = '555-1234'
            mock_faker_instance.paragraph.return_value = 'Test notes'
            mock_faker_instance.date_time_between.return_value = timezone.make_aware(datetime.datetime(2020, 1, 1))

            # Call the command
            call_command('generate_fake_members', stdout=out)

        # Check output
        output = out.getvalue()
        self.assertIn('Generating 100 fake members', output)
        self.assertIn('Successfully created', output)

        # Check that members were created
        self.assertEqual(Member.objects.count(), self.initial_member_count + 100)

    def test_command_with_custom_count(self):
        """Test the command with a custom count."""
        # Redirect stdout to capture output
        out = StringIO()

        # Mock Faker to avoid random data and speed up tests
        with patch('core.management.commands.generate_fake_members.Faker') as mock_faker:
            # Setup mock faker instance
            mock_faker_instance = MagicMock()
            mock_faker.return_value = mock_faker_instance

            # Mock unique email generator
            mock_faker_instance.unique.email.side_effect = [f'test{i}@example.com' for i in range(10)]

            # Mock other faker methods
            mock_faker_instance.first_name.return_value = 'Test'
            mock_faker_instance.last_name.return_value = 'User'
            mock_faker_instance.random_letter.return_value = 'A'
            mock_faker_instance.street_address.return_value = '123 Test St'
            mock_faker_instance.city.return_value = 'Test City'
            mock_faker_instance.state_abbr.return_value = 'MS'
            mock_faker_instance.zipcode.return_value = '12345'
            mock_faker_instance.phone_number.return_value = '555-1234'
            mock_faker_instance.paragraph.return_value = 'Test notes'
            mock_faker_instance.date_time_between.return_value = timezone.make_aware(datetime.datetime(2020, 1, 1))

            # Call the command with custom count
            call_command('generate_fake_members', count=10, stdout=out)

        # Check output
        output = out.getvalue()
        self.assertIn('Generating 10 fake members', output)
        self.assertIn('Successfully created', output)

        # Check that members were created
        self.assertEqual(Member.objects.count(), self.initial_member_count + 10)

    def test_command_with_no_departments(self):
        """Test the command when no departments exist."""
        # Delete all departments
        Department.objects.all().delete()

        # Redirect stdout to capture output
        out = StringIO()

        # Mock Faker to avoid random data and speed up tests
        with patch('core.management.commands.generate_fake_members.Faker') as mock_faker:
            # Setup mock faker instance
            mock_faker_instance = MagicMock()
            mock_faker.return_value = mock_faker_instance

            # Mock unique email generator
            mock_faker_instance.unique.email.side_effect = [f'test{i}@example.com' for i in range(5)]

            # Mock other faker methods
            mock_faker_instance.first_name.return_value = 'Test'
            mock_faker_instance.last_name.return_value = 'User'
            mock_faker_instance.random_letter.return_value = 'A'
            mock_faker_instance.street_address.return_value = '123 Test St'
            mock_faker_instance.city.return_value = 'Test City'
            mock_faker_instance.state_abbr.return_value = 'MS'
            mock_faker_instance.zipcode.return_value = '12345'
            mock_faker_instance.phone_number.return_value = '555-1234'
            mock_faker_instance.paragraph.return_value = 'Test notes'
            mock_faker_instance.date_time_between.return_value = timezone.make_aware(datetime.datetime(2020, 1, 1))

            # Call the command with a small count to speed up test
            call_command('generate_fake_members', count=5, stdout=out)

        # Check output
        output = out.getvalue()
        self.assertIn('No departments found. Creating a default department', output)
        self.assertIn('Successfully created', output)

        # Check that a default department was created
        self.assertEqual(Department.objects.count(), 1)
        self.assertEqual(Department.objects.first().name, 'Default Department')

        # Check that members were created
        self.assertEqual(Member.objects.count(), self.initial_member_count + 5)

    @patch('core.management.commands.generate_fake_members.Faker')
    @patch('core.management.commands.generate_fake_members.Member.objects.create')
    def test_command_with_error_handling(self, mock_create, mock_faker_factory):
        """
        Test the command's error handling when Member.objects.create fails.
        """
        # Configure the mock Faker instance
        mock_faker_instance = MagicMock()
        # Set up the unique attribute as a MagicMock with email method
        mock_faker_instance.unique = MagicMock()
        mock_faker_instance.unique.email.return_value = '<EMAIL>'
        # Set up other methods with string returns instead of MagicMock objects
        mock_faker_instance.first_name.return_value = 'Test'
        mock_faker_instance.last_name.return_value = 'User'
        mock_faker_instance.random_letter.return_value = 'A'
        mock_faker_instance.street_address.return_value = '123 Test St'
        mock_faker_instance.city.return_value = 'Test City'
        mock_faker_instance.state_abbr.return_value = 'MS'
        mock_faker_instance.zipcode.return_value = '12345'
        mock_faker_instance.phone_number.return_value = '555-1234'
        mock_faker_instance.paragraph.return_value = 'Test notes'
        mock_faker_instance.date_time_between.return_value = timezone.now()
        mock_faker_factory.return_value = mock_faker_instance

        # Simulate an IntegrityError on the second attempt
        call_count = {'count': 0}
        def side_effect(*args, **kwargs):
            call_count['count'] += 1
            if call_count['count'] == 2:
                raise IntegrityError("Simulated unique constraint violation")
            else:
                # Return a mock Member object for successful calls
                mock_member = MagicMock(spec=Member)
                mock_member.email = kwargs.get('email', f'test{call_count["count"]}@example.com')
                return mock_member

        mock_create.side_effect = side_effect

        out = StringIO()
        err = StringIO()
        # Expect the command to catch the IntegrityError and report it
        call_command('generate_fake_members', count=3, stdout=out, stderr=err)

        # Check that the command reported the error
        self.assertIn("Error creating member: Simulated unique constraint violation", err.getvalue())
        # Check that the command continued and tried to create the next member (or finished)
        # It should have attempted 3 creations, succeeded on 1st and 3rd, failed on 2nd.
        self.assertEqual(mock_create.call_count, 3)
        # Check success message reflects only successfully created members (adjust based on actual output)
        self.assertIn("Successfully created", out.getvalue())

    def test_add_arguments(self):
        """Test the add_arguments method."""
        # Create a mock parser
        parser = MagicMock()

        # Call add_arguments
        command = Command()
        command.add_arguments(parser)

        # Check that the parser was called with the correct arguments
        parser.add_argument.assert_called_once_with(
            '--count', type=int, default=100, help='Number of fake members to create'
        )

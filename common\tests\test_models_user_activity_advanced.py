"""
Advanced tests for the UserActivity model in the common app.

This module contains comprehensive tests for the UserActivity model,
including edge cases, performance, and Django's Meta options.
"""
import datetime
from unittest.mock import patch
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import connection, reset_queries
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.utils import DataError, IntegrityError

from common.models import UserActivity

User = get_user_model()


class UserActivityAdvancedModelTests(TestCase):
    """Advanced test cases for the UserActivity model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        self.activity = UserActivity.objects.create(
            user=self.user,
            description="Test activity"
        )

    def test_verbose_name_and_plural(self):
        """Test verbose_name and verbose_name_plural in Meta."""
        self.assertEqual(UserActivity._meta.verbose_name, "User Activity")
        self.assertEqual(UserActivity._meta.verbose_name_plural, "User Activities")

    def test_ordering(self):
        """Test that activities are ordered by timestamp in descending order."""
        # Set a base time for consistent testing
        base_time = timezone.now()
        
        # Update the timestamp of the existing activity
        self.activity.timestamp = base_time
        self.activity.save()
        
        # Create another activity with a later timestamp
        later_time = base_time + datetime.timedelta(hours=1)
        activity2 = UserActivity.objects.create(
            user=self.user,
            description="Later activity"
        )
        # Set timestamp after creation to avoid auto_now_add
        activity2.timestamp = later_time
        activity2.save()
        
        # Create another activity with an earlier timestamp
        earlier_time = base_time - datetime.timedelta(hours=1)
        activity3 = UserActivity.objects.create(
            user=self.user,
            description="Earlier activity"
        )
        # Set timestamp after creation to avoid auto_now_add
        activity3.timestamp = earlier_time
        activity3.save()
        
        # Refresh from database to ensure we have the latest values
        activity2.refresh_from_db()
        activity3.refresh_from_db()
        self.activity.refresh_from_db()
        
        # Query all activities
        activities = list(UserActivity.objects.all())
        
        # Check ordering by comparing timestamps
        self.assertEqual(activities[0].timestamp, later_time)
        self.assertEqual(activities[1].timestamp, base_time)
        self.assertEqual(activities[2].timestamp, earlier_time)
        
        # Check ordering by comparing descriptions
        self.assertEqual(activities[0].description, "Later activity")
        self.assertEqual(activities[1].description, "Test activity")
        self.assertEqual(activities[2].description, "Earlier activity")

    def test_string_representation_formatting(self):
        """Test __str__ method with different timestamp formats."""
        # Update the timestamp to a known value
        specific_time = timezone.make_aware(datetime.datetime(2023, 1, 1, 12, 0, 0))
        self.activity.timestamp = specific_time
        self.activity.save()
        
        # Expected string representation
        expected = f"{self.user.email} - Test activity - 2023-01-01 12:00:00"
        
        # Check string representation
        self.assertEqual(str(self.activity), expected)

    def test_description_max_length(self):
        """Test description field max length."""
        max_length = UserActivity._meta.get_field('description').max_length
        self.assertEqual(max_length, 255)
        
        # Test with a description at max length
        long_description = "a" * 255
        activity = UserActivity.objects.create(
            user=self.user,
            description=long_description
        )
        self.assertEqual(activity.description, long_description)
        
        # Test with a description exceeding max length
        too_long = UserActivity(
            user=self.user,
            description="a" * 256
        )
        
        # Should raise ValidationError when full_clean is called
        with self.assertRaises(ValidationError):
            too_long.full_clean()

    def test_description_required(self):
        """Test that description is required."""
        with self.assertRaises(IntegrityError):
            UserActivity.objects.create(
                user=self.user,
                description=None
            )

    def test_user_on_delete_cascade(self):
        """Test that activities are deleted when the user is deleted."""
        # Create a new user for this test to avoid affecting other tests
        test_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Cascade Test User'
        )
        
        # Create multiple activities for the test user
        UserActivity.objects.create(
            user=test_user,
            description="Activity 1"
        )
        UserActivity.objects.create(
            user=test_user,
            description="Activity 2"
        )
        
        # Check that activities exist
        self.assertEqual(UserActivity.objects.filter(user=test_user).count(), 2)
        
        # Get the user ID before deletion
        user_id = test_user.id
        
        # Delete the user
        test_user.delete()
        
        # Check that activities are deleted - use the ID instead of the user object
        self.assertEqual(UserActivity.objects.filter(user_id=user_id).count(), 0)

    @override_settings(DEBUG=True)
    def test_query_efficiency(self):
        """Test the query efficiency when accessing user activities."""
        # Clear query log
        reset_queries()
        
        # Create a user with multiple activities
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Efficiency User'
        )
        
        # Create activities
        for i in range(5):
            UserActivity.objects.create(
                user=user,
                description=f"Activity {i}"
            )
        
        # Clear query log again
        reset_queries()
        
        # Query all activities for the user
        activities = UserActivity.objects.filter(user=user)
        list(activities)  # Force evaluation of the queryset
        
        # Check the number of queries
        # Should be just 1 query to fetch all activities
        self.assertEqual(len(connection.queries), 1)
        
        # Now test with prefetch_related to include user data
        reset_queries()
        activities = UserActivity.objects.filter(user=user).select_related('user')
        
        # Accessing user data shouldn't require additional queries
        for activity in activities:
            user_email = activity.user.email
        
        # Should still be just 1 query
        self.assertEqual(len(connection.queries), 1)

    def test_filtering_by_user(self):
        """Test filtering by user."""
        # Create multiple users
        user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='User One'
        )
        user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='User Two'
        )
        
        # Create activities for each user
        for i in range(3):
            UserActivity.objects.create(
                user=user1,
                description=f"User1 Activity {i}"
            )
            UserActivity.objects.create(
                user=user2,
                description=f"User2 Activity {i}"
            )
        
        # Test filtering by each user
        user1_activities = UserActivity.objects.filter(user=user1)
        self.assertEqual(user1_activities.count(), 3)
        for activity in user1_activities:
            self.assertEqual(activity.user, user1)
            self.assertTrue(activity.description.startswith("User1"))
        
        user2_activities = UserActivity.objects.filter(user=user2)
        self.assertEqual(user2_activities.count(), 3)
        for activity in user2_activities:
            self.assertEqual(activity.user, user2)
            self.assertTrue(activity.description.startswith("User2"))

    def test_filtering_by_timerange(self):
        """Test filtering by timerange."""
        # Delete the activity created in setUp to avoid interference
        self.activity.delete()

        # Create activities with specific timestamps
        now = timezone.now()
        
        # Create a series of activities over time
        for i in range(5):
            # Each activity is 1 day apart
            time_delta = datetime.timedelta(days=i)
            activity = UserActivity.objects.create(
                user=self.user,
                description=f"Activity {i}"
            )
            # Set timestamp manually to avoid auto_now_add
            activity.timestamp = now - time_delta
            activity.save()
        
        # Test filtering by recent activities (last 2 days)
        two_days_ago = now - datetime.timedelta(days=2)
        recent_activities = UserActivity.objects.filter(timestamp__gte=two_days_ago)
        self.assertEqual(recent_activities.count(), 3)  # Activities from days 0, 1, 2
        
        # Test filtering by older activities (more than 2 days ago)
        older_activities = UserActivity.objects.filter(timestamp__lt=two_days_ago)
        self.assertEqual(older_activities.count(), 2)  # Only 2 older ones (days 3, 4)
        
        # Test date range filtering
        three_days_ago = now - datetime.timedelta(days=3)
        one_day_ago = now - datetime.timedelta(days=1)
        range_activities = UserActivity.objects.filter(
            timestamp__gte=three_days_ago,
            timestamp__lte=one_day_ago
        )
        self.assertEqual(range_activities.count(), 3)  # Activities from days 1, 2, and 3

    def test_filtering_by_description(self):
        """Test filtering by description."""
        # Create activities with different descriptions
        descriptions = [
            "Login attempt",
            "Logout action",
            "Profile update",
            "Settings change",
            "Login successful"
        ]
        
        for desc in descriptions:
            UserActivity.objects.create(
                user=self.user,
                description=desc
            )
        
        # Test exact match
        login_activities = UserActivity.objects.filter(description="Login attempt")
        self.assertEqual(login_activities.count(), 1)
        self.assertEqual(login_activities[0].description, "Login attempt")
        
        # Test contains match
        login_related = UserActivity.objects.filter(description__contains="Login")
        self.assertEqual(login_related.count(), 2)
        
        # Test case-insensitive match
        login_case_insensitive = UserActivity.objects.filter(description__icontains="login")
        self.assertEqual(login_case_insensitive.count(), 2)
        
        # Test startswith
        profile_update = UserActivity.objects.filter(description__startswith="Profile")
        self.assertEqual(profile_update.count(), 1)
        self.assertEqual(profile_update[0].description, "Profile update")

    def test_bulk_creation(self):
        """Test bulk creation of activities."""
        # Use bulk_create for multiple activities
        activities_to_create = []
        for i in range(10):
            activities_to_create.append(
                UserActivity(
                    user=self.user,
                    description=f"Bulk activity {i}"
                )
            )
        
        # Bulk create
        UserActivity.objects.bulk_create(activities_to_create)
        
        # Check that all were created
        bulk_activities = UserActivity.objects.filter(description__startswith="Bulk activity")
        self.assertEqual(bulk_activities.count(), 10)
        
        # Check that the order is maintained
        all_activities = list(bulk_activities.order_by('description'))
        for i, activity in enumerate(all_activities):
            self.assertEqual(activity.description, f"Bulk activity {i}")

    def test_activity_search(self):
        """Test comprehensive search across activity fields."""
        # Create users with searchable names
        search_user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Search User One'
        )
        search_user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Search User Two'
        )
        
        # Create activities with searchable descriptions
        activities_data = [
            (search_user1, "Login activity from search user"),
            (search_user1, "Profile update activity"),
            (search_user2, "Another login activity"),
            (search_user2, "Settings changed by search user")
        ]
        
        for user, desc in activities_data:
            UserActivity.objects.create(
                user=user,
                description=desc
            )
        
        # Search by user name
        user_name_results = UserActivity.objects.filter(user__name__contains="Search User")
        self.assertEqual(user_name_results.count(), 4)
        
        # Search by description containing specific word
        login_results = UserActivity.objects.filter(description__icontains="login")
        self.assertEqual(login_results.count(), 2)
        
        # Complex search - user name and description
        complex_results = UserActivity.objects.filter(
            user__name__contains="Search User",
            description__icontains="login"
        )
        self.assertEqual(complex_results.count(), 2)

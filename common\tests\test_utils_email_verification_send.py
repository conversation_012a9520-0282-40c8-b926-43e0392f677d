"""
Tests for the email verification sending functionality in the common app.

This module contains comprehensive tests for the send_verification_email function.
"""
import uuid
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings

from common.utils.email import send_verification_email
from common.models import EmailVerification

User = get_user_model()


class SendVerificationEmailTests(TestCase):
    """Test cases for the send_verification_email function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Store the expected verification URL format for testing
        self.verification_url_format = f"{settings.FRONTEND_URL}/verify-email/"

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_verification(self, mock_send_mail):
        """Test that send_verification_email creates a verification record."""
        # Ensure no verifications exist before the test
        self.assertEqual(EmailVerification.objects.count(), 0)
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        self.assertEqual(verification.user, self.user)
        self.assertFalse(verification.verified)
        self.assertIsNotNone(verification.key)
        self.assertIsNotNone(verification.expires_at)
        
        # Verify expiration date is set correctly (30 days from now)
        expected_expiry = timezone.now() + timedelta(days=30)
        self.assertAlmostEqual(
            verification.expires_at.timestamp(),
            expected_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check email arguments in detail
        args = mock_send_mail.call_args[0]
        kwargs = mock_send_mail.call_args[1]
        
        # Subject should be about email verification
        self.assertEqual(args[0], "Verify your email address")
        
        # Plain text message should contain key info
        plain_message = args[1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(verification.key), plain_message)
        self.assertIn(f"{self.verification_url_format}{verification.key}", plain_message)
        self.assertIn("30 days", plain_message)
        
        # From email should be the default from email
        self.assertEqual(args[2], settings.DEFAULT_FROM_EMAIL)
        
        # Recipient should be the user's email
        self.assertEqual(args[3], [self.user.email])
        
        # HTML message should contain the verification key
        html_message = kwargs['html_message']
        self.assertIn(str(verification.key), html_message)
        
        # Email should not be sent silently
        self.assertFalse(kwargs['fail_silently'])

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_reuses_existing_verification(self, mock_send_mail):
        """Test that send_verification_email reuses an existing unexpired verification."""
        # Create an existing verification
        existing_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=15)  # Not expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that no new verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        
        # Check that the existing verification was returned
        self.assertEqual(verification, existing_verification)
        
        # Check that the email was sent with the existing verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(existing_verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(existing_verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_new_if_expired(self, mock_send_mail):
        """Test that send_verification_email creates a new verification if existing is expired."""
        # Create an expired verification
        expired_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() - timedelta(days=1)  # Expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a new verification was created
        self.assertEqual(EmailVerification.objects.count(), 2)
        self.assertNotEqual(verification, expired_verification)
        self.assertEqual(verification.user, self.user)
        
        # Check that the email was sent with the new verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_new_if_verified(self, mock_send_mail):
        """Test that send_verification_email creates a new verification if existing is verified."""
        # Create a verified verification
        verified_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=True,
            expires_at=timezone.now() + timedelta(days=15)  # Not expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a new verification was created
        self.assertEqual(EmailVerification.objects.count(), 2)
        self.assertNotEqual(verification, verified_verification)
        self.assertEqual(verification.user, self.user)
        
        # Check that the email was sent with the new verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.render_to_string')
    @patch('common.utils.email.send_mail')
    def test_send_verification_email_content(self, mock_send_mail, mock_render_to_string):
        """Test the content of the verification email."""
        # Mock render_to_string to return a simple HTML message
        mock_render_to_string.return_value = '<html>Verification Email</html>'
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check render_to_string call
        mock_render_to_string.assert_called_once_with('emails/verify_email.html', {
            'name': self.user.name,
            'verification_url': f"{settings.FRONTEND_URL}/verify-email/{verification.key}",
            'expiry_days': 30,
        })
        
        # Check send_mail call
        mock_send_mail.assert_called_once_with(
            "Verify your email address",
            mock_send_mail.call_args[0][1],  # plain_message (complex to check exactly)
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message='<html>Verification Email</html>',
            fail_silently=False,
        )
        
        # Check plain message content
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(verification.key), plain_message)
        self.assertIn("30 days", plain_message)

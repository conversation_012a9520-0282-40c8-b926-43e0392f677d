from django_filters import rest_framework as filters
from django.utils import timezone
from core.models import Member # Import Member model
from .base_filters import BaseBooleanFilterSet # Import the new base class

class DynamicFieldsMemberFilter(BaseBooleanFilterSet): # Inherit from BaseBooleanFilterSet
    """
    Filter class for Member model with dynamic field filtering,
    including custom boolean handling.
    """
    # Basic filters
    name = filters.CharFilter(lookup_expr='icontains')
    email = filters.CharFilter(lookup_expr='icontains')
    department = filters.NumberFilter(field_name='department__id')
    department_name = filters.CharFilter(field_name='department__name', lookup_expr='icontains')
    address = filters.CharFilter(lookup_expr='icontains')
    city = filters.CharFilter(lookup_expr='icontains')
    
    # Additional filters for all displayed columns
    dst = filters.CharFilter(lookup_expr='icontains')
    title = filters.CharFilter(lookup_expr='icontains')
    mi = filters.CharFilter(lookup_expr='icontains')
    st = filters.CharFilter(lookup_expr='icontains')
    zip_code = filters.CharFilter(lookup_expr='icontains')
    home_phone = filters.CharFilter(lookup_expr='icontains')
    business_phone = filters.CharFilter(lookup_expr='icontains')
    lapel_pin = filters.CharFilter(lookup_expr='icontains')
    notes = filters.CharFilter(lookup_expr='icontains')
    orig_join_date = filters.DateFilter(method='filter_orig_join_date')
    orig_join_date_gte = filters.DateFilter(method='filter_orig_join_date_gte')
    orig_join_date_lte = filters.DateFilter(method='filter_orig_join_date_lte')
    
    # Boolean filters using the custom method from BaseBooleanFilterSet
    # Changed to CharFilter to accept string inputs ('yes', 'no', 'all')
    active = filters.CharFilter(method='filter_boolean_field')
    ex_active = filters.CharFilter(method='filter_boolean_field', field_name='active')
    membership_active = filters.CharFilter(method='filter_boolean_field')
    is_deceased = filters.CharFilter(method='filter_boolean_field')
    executive_board = filters.CharFilter(method='filter_boolean_field')
    committee_member = filters.CharFilter(method='filter_boolean_field')
    new_member = filters.CharFilter(method='filter_boolean_field')
    lifetime = filters.CharFilter(method='filter_boolean_field')
    
    # Other filters
    committee = filters.CharFilter(lookup_expr='icontains')
    membership_class = filters.CharFilter(method='filter_membership_class')
    lead_status = filters.CharFilter(method='filter_lead_status')
    role = filters.CharFilter(method='filter_role')
    gender = filters.CharFilter(method='filter_gender')
    
    # Payment status filters (Keep BooleanFilter with method for now)
    # These might require custom logic if they aren't direct model fields
    has_paid_this_year = filters.BooleanFilter(method='filter_payment_status')
    has_paid_last_year = filters.BooleanFilter(method='filter_payment_status')
    has_paid_in_last_five_years = filters.BooleanFilter(method='filter_payment_status')
    
    class Meta:
        model = Member # Set the model directly
        fields = [
            'name', 'email', 'department', 'department_name', 'committee',
            'active', 'membership_active', 'is_deceased', 'executive_board', 'committee_member', 
            'new_member', 'lifetime', 'membership_class', 'lead_status', 'role', 'gender',
            'has_paid_this_year', 'has_paid_last_year', 'has_paid_in_last_five_years',
            'dst', 'title', 'mi', 'address', 'city', 'st', 'zip_code', 'home_phone', 'business_phone',
            'lapel_pin', 'notes', 'orig_join_date', 'orig_join_date_gte', 'orig_join_date_lte'
        ]

    def filter_payment_status(self, queryset, name, value):
        """
        Filter by payment status attributes that are calculated in the view
        Note: The name parameter indicates which filter triggered this (e.g., 'has_paid_this_year')
        The value will be True or False.
        Placeholder: Actual logic might need to be in the view or via queryset annotations.
        """
        print(f"Payment status filter called for {name} with value: {value} (type: {type(value)})")
        # Example: if name == 'has_paid_this_year':
            # Apply specific logic for this filter based on 'value'
            # return queryset.filter(...)
        # Since the logic isn't defined, return queryset unfiltered for now
        print(f"  -> Placeholder logic for {name}: returning queryset unfiltered.")
        return queryset

    def filter_membership_class(self, queryset, name, value):
        """
        Filter by membership class with support for 'all', specific classes, and 'none'
        - 'all': Return all members (no filtering)
        - 'life_member', 'honorary_member', 'associate_member', 'member': Filter by specific class
        - 'none': Return members with no membership class
        """
        print(f"Membership class filter called with value: {value}, type: {type(value)}")

        # Convert value to string and lowercase for consistent comparison
        value_str = str(value).lower()

        if value_str == 'all' or not value_str:
            print("Returning all members (no membership_class filtering)")
            return queryset
        elif value_str == 'none':
            print("Filtering for membership_class=null")
            return queryset.filter(membership_class__isnull=True)

        # Filter by specific class name (case-insensitive)
        print(f"Filtering for membership_class__iexact='{value_str}'")
        # Assuming membership_class is a CharField or similar
        return queryset.filter(membership_class__iexact=value_str)
        
    def filter_lead_status(self, queryset, name, value):
        """
        Filter by lead status
        """
        if not value:
            return queryset
            
        value_str = str(value).lower()
        if value_str == 'all':
            return queryset
            
        return queryset.filter(lead_status__iexact=value_str)
        
    def filter_role(self, queryset, name, value):
        """
        Filter by role
        """
        if not value:
            return queryset
            
        value_str = str(value).lower()
        if value_str == 'all':
            return queryset
            
        return queryset.filter(role__iexact=value_str)
        
    def filter_gender(self, queryset, name, value):
        """
        Filter by gender
        """
        if not value:
            return queryset
            
        value_str = str(value).lower()
        if value_str == 'all':
            return queryset
            
        return queryset.filter(gender__iexact=value_str)
        
    def filter_orig_join_date(self, queryset, name, value):
        """
        Filter by orig_join_date with proper date handling
        """
        if not value:
            return queryset
            
        try:
            # Make sure we're using date objects for comparison
            if hasattr(value, 'date'):
                # If it's a datetime object, get the date part
                value = value.date()
                
            # Filter using the date part of the DateTimeField
            return queryset.filter(orig_join_date__date=value)
        except Exception as e:
            print(f"Error filtering by orig_join_date: {e}")
            return queryset
            
    def filter_orig_join_date_gte(self, queryset, name, value):
        """
        Filter by orig_join_date >= value with proper date handling
        """
        if not value:
            return queryset
            
        try:
            # Make sure we're using date objects for comparison
            if hasattr(value, 'date'):
                # If it's a datetime object, get the date part
                value = value.date()
                
            # Filter using the date part of the DateTimeField
            return queryset.filter(orig_join_date__date__gte=value)
        except Exception as e:
            print(f"Error filtering by orig_join_date_gte: {e}")
            return queryset
            
    def filter_orig_join_date_lte(self, queryset, name, value):
        """
        Filter by orig_join_date <= value with proper date handling
        """
        if not value:
            return queryset
            
        try:
            # Make sure we're using date objects for comparison
            if hasattr(value, 'date'):
                # If it's a datetime object, get the date part
                value = value.date()
                
            # Filter using the date part of the DateTimeField
            return queryset.filter(orig_join_date__date__lte=value)
        except Exception as e:
            print(f"Error filtering by orig_join_date_lte: {e}")
            return queryset
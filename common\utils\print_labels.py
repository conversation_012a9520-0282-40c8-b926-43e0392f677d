import datetime
import os
import base64
from io import BytesIO
from django.http import HttpResponse
from django.conf import settings
from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.shared import Inches, Twips, Pt
from rest_framework import status
from django.core.exceptions import ValidationError
from typing import List, Dict, Any

from core.models import Member
from common.views import APIResponse


def set_cell_margins(cell, top=0, bottom=0, left=0, right=0):
    """
    Set the margins of the cell in twips (twentieths of a point).
    Each point is 20 twips.
    """
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()
    tcMar = OxmlElement('w:tcMar')

    # Set margins in twips (1 inch = 1440 twips, 1 point = 20 twips)
    margins = {
        'top': top,
        'bottom': bottom,
        'left': left,
        'right': right
    }
    for name, value in margins.items():
        subelement = OxmlElement('w:' + name)
        subelement.set(qn('w:w'), str(int(value * 1440)))  # Convert inches to twips
        subelement.set(qn('w:type'), 'dxa')
        tcMar.append(subelement)

    tcPr.append(tcMar)


def print_labels(request):
    """
    Generate and return labels for members based on request data
    
    Args:
        request: The HTTP request containing a JSON payload with:
            - member_ids: list of member IDs
            - format: label format (e.g., 'avery5160')
            - start_position: start position for labels (1-indexed)
            - include_department: whether to include department in the address
        
    Returns:
        APIResponse with PDF data as base64 string, or error response
    """
    try:
        # Validate and extract request data
        if not request.data:
            return APIResponse(
                message="No data provided",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Extract and validate member_ids
        member_ids = request.data.get('member_ids', [])
        if not member_ids or not isinstance(member_ids, list) or len(member_ids) == 0:
            return APIResponse(
                message="member_ids is required and must be a non-empty list",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Extract and validate format
        label_format = request.data.get('format', '')
        valid_formats = ['avery5160', 'avery8160', 'avery5163']
        if not label_format or label_format not in valid_formats:
            return APIResponse(
                message=f"format is required and must be one of: {', '.join(valid_formats)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Extract and validate start_position
        start_position = request.data.get('start_position', 1)
        if not isinstance(start_position, int) or start_position < 1:
            return APIResponse(
                message="start_position must be a positive integer",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Extract include_department flag
        include_department = request.data.get('include_department', False)
        
        # Get members from database
        members = Member.objects.filter(id__in=member_ids)
        
        if not members.exists():
            return APIResponse(
                message="No valid members found with the provided IDs",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Configure dimensions based on format
        if label_format == 'avery5160' or label_format == 'avery8160':
            margin_top = 0.5
            margin_bottom = 0.5
            margin_left = 0.319
            margin_right = 0.219
            label_height = 1.0
            label_width = 2.625
            columns = 3
        elif label_format == 'avery5163':
            margin_top = 0.5
            margin_bottom = 0.5
            margin_left = 0.5
            margin_right = 0.5
            label_height = 2.0
            label_width = 4.0
            columns = 2
        else:
            # Fallback to standard dimensions
            margin_top = 0.5
            margin_bottom = 0.5
            margin_left = 0.5
            margin_right = 0.5
            label_height = 1.0
            label_width = 2.625
            columns = 3

        # Prepare label content
        labels = []
        for member in members:
            address = []
            address.append(member.name.upper())
            
            if include_department and member.department:
                address.append(member.department.name.upper())
                
            address.append(member.address.upper())
            address.append(f"{member.city.upper()} {member.st.upper()} {member.zip_code.upper()}")
            
            labels.append("\n".join(address))

        # Create document
        doc = Document()
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(margin_top)
            section.bottom_margin = Inches(margin_bottom)
            section.left_margin = Inches(margin_left)
            section.right_margin = Inches(margin_right)
        
        table = doc.add_table(rows=0, cols=columns)
        table.style = 'Table Grid'
        table.autofit = False  # Disable autofit to keep column widths fixed

        # Set column widths
        for column in table.columns:
            column.width = Inches(label_width)

        # Calculate how many positions to skip at the beginning
        skip_positions = start_position - 1
        
        # Add empty cells for the skipped positions
        current_row = None
        for i in range(skip_positions):
            if i % columns == 0:  # Start a new row
                current_row = table.add_row()
                current_row.height = Inches(label_height)
            # Add an empty cell
            cell_index = i % columns
            if current_row:
                cell = current_row.cells[cell_index]
                set_cell_margins(cell, left=0.05)

        # Adding rows and cells for the labels
        current_position = skip_positions
        for index, label in enumerate(labels):
            position = current_position + index
            if position % columns == 0:  # Start a new row
                current_row = table.add_row()
                current_row.height = Inches(label_height)
            
            cell_index = position % columns
            cell = current_row.cells[cell_index]
            
            # Create a paragraph for styling
            paragraph = cell.paragraphs[0]
            run = paragraph.add_run(label)
            run.font.size = Pt(11)
            run.font.name = 'Times New Roman'
            set_cell_margins(cell, left=0.05)  # Set smaller left margin in inches

        # Generate the document
        current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"mailing_labels_{current_time}.docx"
        pseudo_file = BytesIO()
        doc.save(pseudo_file)
        pseudo_file.seek(0)
        
        # Create media directory if it doesn't exist
        media_dir = os.path.join(settings.BASE_DIR, 'media')
        if not os.path.exists(media_dir):
            os.makedirs(media_dir)
            
        # Create labels subdirectory if it doesn't exist
        labels_dir = os.path.join(media_dir, 'labels')
        if not os.path.exists(labels_dir):
            os.makedirs(labels_dir)
            
        # Save the file to the media directory
        file_path = os.path.join(labels_dir, filename)
        with open(file_path, 'wb') as f:
            f.write(pseudo_file.getvalue())

        # Create a base64 version of the file for API response
        pseudo_file.seek(0)
        encoded_file = base64.b64encode(pseudo_file.read()).decode('utf-8')
        
        return APIResponse(
            message="Labels generated successfully",
            data={"pdf": f"data:application/pdf;base64,{encoded_file}"},
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        return APIResponse(
            message=f"Error generating labels: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

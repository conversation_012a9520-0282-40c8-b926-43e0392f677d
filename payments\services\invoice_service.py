"""
Invoice service for handling invoice generation and PDF creation.
Centralizes invoice logic and eliminates code duplication.
"""
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from decimal import Decimal

from payments.models import Payment
from payments.config import PaymentConfig, ErrorMessages

logger = logging.getLogger(__name__)


class InvoiceItem:
    """Data class for invoice items"""
    def __init__(self, quantity: int, description: str, unit_price: Decimal, amount: Decimal):
        self.quantity = quantity
        self.description = description
        self.unit_price = unit_price
        self.amount = amount

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for template rendering"""
        return {
            'quantity': self.quantity,
            'description': self.description,
            'unit_price': float(self.unit_price),
            'amount': float(self.amount)
        }


class InvoiceService:
    """Service for generating invoice data and PDFs"""

    @staticmethod
    def generate_invoice_items(payment: Payment) -> List[InvoiceItem]:
        """Generate invoice items based on payment type"""
        items = []

        if payment.payment_for == Payment.PaymentFor.MEMBERSHIP:
            items = InvoiceService._generate_membership_items(payment)
        elif payment.payment_for == Payment.PaymentFor.EVENT:
            items = InvoiceService._generate_event_items(payment)

        # If no items were generated, add a default item
        if not items:
            items.append(InvoiceItem(
                quantity=1,
                description=f"Payment - {payment.get_payment_for_display()}",
                unit_price=payment.amount,
                amount=payment.amount
            ))

        return items

    @staticmethod
    def _generate_membership_items(payment: Payment) -> List[InvoiceItem]:
        """Generate invoice items for membership payments"""
        items = []
        covered_members = payment.covered_members.all()

        if not covered_members.exists():
            return items

        # Calculate per-member amount
        per_member_amount = payment.total_amount / covered_members.count()

        # Add each covered member as an item
        for member in covered_members:
            membership_class = getattr(member, 'get_membership_class_display', lambda: 'Member')()
            items.append(InvoiceItem(
                quantity=1,
                description=f"Membership - {member.name} ({membership_class})",
                unit_price=per_member_amount,
                amount=per_member_amount
            ))

        return items

    @staticmethod
    def _generate_event_items(payment: Payment) -> List[InvoiceItem]:
        """Generate invoice items for event registration payments"""
        items = []
        event_reg = payment.event_registration

        if not event_reg:
            return items

        # Add main registration
        event_name = event_reg.event.name if event_reg.event else 'Event'
        registration_type = getattr(event_reg, 'get_registration_type_display', lambda: 'Registration')()

        # Calculate unit price for participants
        participants = getattr(event_reg, 'number_of_participants', 1) or 1
        base_unit_price = event_reg.base_amount / participants if participants > 0 else event_reg.base_amount

        items.append(InvoiceItem(
            quantity=participants,
            description=f"Event Registration - {event_name} ({registration_type})",
            unit_price=base_unit_price,
            amount=event_reg.base_amount
        ))

        # Add guests if any
        guests = getattr(event_reg, 'number_of_guests', 0) or 0
        if guests > 0:
            guest_unit_price = event_reg.guest_amount / guests if guests > 0 else event_reg.guest_amount
            items.append(InvoiceItem(
                quantity=guests,
                description=f"Guest Registration - {event_name}",
                unit_price=guest_unit_price,
                amount=event_reg.guest_amount
            ))

        return items

    @staticmethod
    def generate_invoice_context(payment: Payment) -> Dict[str, Any]:
        """Generate context data for invoice template rendering"""
        items = InvoiceService.generate_invoice_items(payment)

        return {
            'invoice': payment,
            'items': [item.to_dict() for item in items],
            'period_start': datetime(payment.paid_year, 1, 1),
            'period_end': datetime(payment.paid_year, 12, 31),
            'total_amount': float(payment.total_amount or payment.amount),
            'item_count': len(items),
            'generated_at': datetime.now()
        }

    @staticmethod
    def get_invoice_filename(payment: Payment, prefix: str = "invoice") -> str:
        """Generate standardized filename for invoice PDFs"""
        invoice_number = payment.invoice_number or f"payment_{payment.pk}"
        # Sanitize filename
        safe_invoice_number = "".join(c for c in invoice_number if c.isalnum() or c in ('-', '_'))

        if prefix == "bulk_invoice":
            return PaymentConfig.BULK_INVOICE_FILENAME_FORMAT.format(invoice_number=safe_invoice_number)
        else:
            return PaymentConfig.INVOICE_FILENAME_FORMAT.format(invoice_number=safe_invoice_number)

    @staticmethod
    def validate_payment_for_invoice(payment: Payment) -> List[str]:
        """Validate that payment has required data for invoice generation"""
        errors = []

        if not payment.amount or payment.amount <= PaymentConfig.MIN_PAYMENT_AMOUNT:
            errors.append(ErrorMessages.INVALID_PAYMENT_AMOUNT)

        if not payment.payer:
            errors.append(ErrorMessages.PAYER_REQUIRED)

        if payment.payment_for == Payment.PaymentFor.MEMBERSHIP:
            if not payment.covered_members.exists():
                errors.append(ErrorMessages.NO_COVERED_MEMBERS)

        elif payment.payment_for == Payment.PaymentFor.EVENT:
            if not payment.event_registration:
                errors.append(ErrorMessages.EVENT_REQUIRED)
            elif not hasattr(payment.event_registration, 'base_amount'):
                errors.append("Event registration must have base amount")

        return errors

    @classmethod
    def can_generate_invoice(cls, payment: Payment) -> bool:
        """Check if payment can be used to generate an invoice"""
        return len(cls.validate_payment_for_invoice(payment)) == 0

    @staticmethod
    def calculate_invoice_totals(items: List[InvoiceItem]) -> Dict[str, Decimal]:
        """Calculate totals for invoice items"""
        subtotal = sum(item.amount for item in items)

        # For now, no tax or additional fees
        # This can be extended later for tax calculations
        tax_amount = Decimal('0.00')
        total = subtotal + tax_amount

        return {
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'total': total
        }

from django.contrib.auth import get_user_model
from django.db import transaction
from rest_framework import status, permissions
from rest_framework_simplejwt.tokens import RefreshToken
from common.views import BaseAPIView, APIResponse
from common.utils import track_activity
from common.utils.email import send_verification_email
from ...serializers.auth import (
    RegisterSerializer,
    LoginSerializer,
    UserDetailsSerializer
)
from rest_framework import serializers

Member = get_user_model()


class RegisterView(BaseAPIView):
    """
    View for user registration
    """
    permission_classes = [permissions.AllowAny]
    # throttle_classes = [RegisterRateThrottle]

    @transaction.atomic
    def post(self, request):
        """
        Handle POST request to register a new user
        """
        try:
            serializer = RegisterSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            user = serializer.save()

            # Send verification email (in case it wasn't sent by the serializer)
            # This ensures the verification email is sent even if the serializer implementation changes
            verification = send_verification_email(user)

            # Calculate expiration days
            from django.utils import timezone
            from datetime import timedelta
            expiry_days = (verification.expires_at - timezone.now()).days

            return APIResponse(
                data={
                    'user': UserDetailsSerializer(user).data,
                    'email_verification_sent': True,
                    'verification_expires_in_days': expiry_days
                },
                message=f"Registration successful. Please check your email ({user.email}) to verify your account. The verification link will expire in {expiry_days} days. After verification, an administrator will need to activate your account before you can log in.",
                status_code=status.HTTP_201_CREATED
            )
        except serializers.ValidationError as e:
            # Format error message for compatibility with tests
            error_detail = e.detail

            # Use the _format_error_message method to properly format the error
            formatted_error = self._format_error_message(error_detail)

            # Return error response with the formatted error message
            return APIResponse(
                message=formatted_error,
                status_code=status.HTTP_400_BAD_REQUEST
            )


class LoginView(BaseAPIView):
    """
    View for user login
    """
    permission_classes = [permissions.AllowAny]
    # throttle_classes = [LoginRateThrottle]

    @transaction.atomic
    def post(self, request):
        """
        Handle POST request to login a user
        """
        serializer = LoginSerializer(
            data=request.data,
            context={'request': request}
        )

        try:
            serializer.is_valid(raise_exception=True)

            user = serializer.validated_data['user']
            refresh = serializer.validated_data['refresh']
            access = serializer.validated_data['access']

            return APIResponse(
                data={
                    'user': UserDetailsSerializer(user).data,
                    'refresh': refresh,
                    'access': access
                },
                message="Staff login successful",
                status_code=status.HTTP_200_OK
            )
        except serializers.ValidationError as e:
            # Format error message for compatibility with tests
            error_detail = e.detail

            # Use the _format_error_message method to properly format the error
            formatted_error = self._format_error_message(error_detail)

            # Return error response with the formatted error message
            return APIResponse(
                message=formatted_error,
                status_code=status.HTTP_400_BAD_REQUEST
            )


class LogoutView(BaseAPIView):
    """
    View for logging out a user
    """
    permission_classes = [permissions.IsAuthenticated]

    @track_activity(description="User logged out")
    @transaction.atomic
    def post(self, request):
        """
        Handle POST request to logout a user
        """
        try:
            refresh_token = request.data.get('refresh')
            if not refresh_token:
                return APIResponse(
                    data=None,
                    message="Refresh token is required",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            token = RefreshToken(refresh_token)
            token.blacklist()

            return APIResponse(
                data=None,
                message="Logout successful",
                status_code=status.HTTP_200_OK
            )
        except Exception as e:
            # Log the error instead of printing it
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Logout failed: {str(e)}")

            return APIResponse(
                data=None,
                message=f"Logout failed: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
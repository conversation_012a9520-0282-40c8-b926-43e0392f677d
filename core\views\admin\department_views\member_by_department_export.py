from django.http import HttpResponse
from rest_framework import status
from django_filters import rest_framework as filters
import openpyxl
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO

from common.utils import track_activity
from common.views import BaseAPIView, APIResponse
from common.permissions import IsStaffUser
from common.filters.member_filters import DynamicFieldsMemberFilter
from core.models import Member
from core.serializers import MembershipRosterAdminSerializer


class DepartmentMemberExportView(BaseAPIView):
    """
    Export department members to Excel with filtering and no pagination (admin only)
    """
    permission_classes = [IsStaffUser]
    filter_backends = (filters.DjangoFilterBackend,)
    pagination_class = None
    filterset_class = DynamicFieldsMemberFilter

    def get_queryset(self):
        """Get the queryset for members, including related department"""
        # Start with all members and select_related department for efficiency
        queryset = Member.objects.all().select_related('department')

        # Check for department_id parameter (required for this view)
        department_id = self.kwargs.get('department_id', None)
        if department_id:
            queryset = queryset.filter(department_id=department_id)
        
        # Check for ids_list parameter
        ids_list = self.request.query_params.get('ids_list', None)
        if ids_list:
            # Split the comma-separated list and convert to integers
            try:
                ids = [int(id.strip()) for id in ids_list.split(',') if id.strip()]
                queryset = queryset.filter(id__in=ids)
            except ValueError:
                # If conversion fails, return empty queryset
                return Member.objects.none()
                
        return queryset

    def filter_queryset(self, queryset):
        """Apply filters to the queryset"""
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    @track_activity(description="Exported department members list")
    def get(self, request, *args, **kwargs):
        """
        Export department members to Excel with filters applied and no pagination
        
        Optional query parameters:
        - ids_list: Comma-separated list of member IDs to include in the export
        """
        # Get filtered queryset
        queryset = self.filter_queryset(self.get_queryset())
        
        try:
            # Create a new workbook and select the active worksheet
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Department Members"

            # Define column headers
            headers = [
                'ID', 'Name', 'Email', 'Department', 'Phone', 'Address', 
                'City', 'State', 'Zip Code', 'Membership Status', 
                'Executive Board', 'Committee Member', 'Committee', 
                'New Member', 'Lifetime', 'Active', 'Membership Active'
            ]

            # Add headers to the worksheet
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.value = header
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
                cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")

            # Add data to the worksheet
            for row_num, member in enumerate(queryset, 2):
                # ID
                worksheet.cell(row=row_num, column=1).value = member.id
                # Name
                worksheet.cell(row=row_num, column=2).value = member.name
                # Email
                worksheet.cell(row=row_num, column=3).value = member.email
                # Department
                worksheet.cell(row=row_num, column=4).value = member.department.name if member.department else ""
                # Phone (use home_phone as primary)
                worksheet.cell(row=row_num, column=5).value = member.home_phone or member.business_phone or ""
                # Address
                worksheet.cell(row=row_num, column=6).value = member.address
                # City
                worksheet.cell(row=row_num, column=7).value = member.city
                # State
                worksheet.cell(row=row_num, column=8).value = member.st
                # Zip Code
                worksheet.cell(row=row_num, column=9).value = member.zip_code
                # Membership Status
                worksheet.cell(row=row_num, column=10).value = member.get_membership_class_display() if hasattr(member, 'get_membership_class_display') else member.membership_class
                # Executive Board
                worksheet.cell(row=row_num, column=11).value = "Yes" if member.executive_board else "No"
                # Committee Member
                worksheet.cell(row=row_num, column=12).value = "Yes" if member.committee_member else "No"
                # Committee
                worksheet.cell(row=row_num, column=13).value = member.committee
                # New Member
                worksheet.cell(row=row_num, column=14).value = "Yes" if member.new_member else "No"
                # Lifetime
                worksheet.cell(row=row_num, column=15).value = "Yes" if member.lifetime else "No"
                # Active
                worksheet.cell(row=row_num, column=16).value = "Yes" if member.active else "No"
                # Membership Active
                worksheet.cell(row=row_num, column=17).value = "Yes" if member.membership_active else "No"

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = (max_length + 2) if max_length < 50 else 50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Create a BytesIO object to save the workbook to
            excel_file = BytesIO()
            workbook.save(excel_file)
            excel_file.seek(0)

            # Get department name for the filename (if available)
            department_name = "department"
            if queryset.exists() and queryset.first().department:
                department_name = queryset.first().department.name.lower().replace(' ', '_')

            # Create the HttpResponse with the Excel file
            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            # Set headers to force download
            response['Content-Disposition'] = f'attachment; filename={department_name}_members_export.xlsx'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition'
            # Disable caching to ensure fresh download each time
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

            return response
            
        except Exception as e:
            return APIResponse(
                message=f"Failed to generate export: {str(e)}",
                success=False,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 
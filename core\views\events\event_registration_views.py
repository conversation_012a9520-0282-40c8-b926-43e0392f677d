"""
Views for EventRegistration model.
"""
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters
from django.utils import timezone
from django.db import transaction, IntegrityError
from django.db.models import F

from common.views import BaseAPIView, APIResponse
from common.pagination import StandardPagination
from core.models import Event, EventRegistration, Member
from payments.serializers import EventRegistrationSerializer


class EventRegistrationFilter(filters.FilterSet):
    """
    Filter for EventRegistration model
    """
    first_name = filters.CharFilter(lookup_expr='icontains')
    last_name = filters.CharFilter(lookup_expr='icontains')
    fire_department = filters.CharFilter(lookup_expr='icontains')
    email = filters.CharFilter(lookup_expr='icontains')
    registration_date_after = filters.DateFilter(field_name='registration_date', lookup_expr='gte')
    registration_date_before = filters.DateFilter(field_name='registration_date', lookup_expr='lte')
    payment_status = filters.ChoiceFilter(choices=EventRegistration.PAYMENT_STATUS)

    class Meta:
        model = EventRegistration
        fields = [
            'first_name', 'last_name', 'fire_department', 'email',
            'registration_date_after', 'registration_date_before', 'payment_status'
        ]


class EventRegistrationListView(BaseAPIView, ListAPIView):
    """
    View for listing event registrations
    """
    queryset = EventRegistration.objects.all()
    serializer_class = EventRegistrationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = EventRegistrationFilter

    def list(self, request, *args, **kwargs):
        """
        Get a paginated and filtered list of event registrations
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Event registrations retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Event registrations retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class EventRegistrationDetailView(BaseAPIView):
    """
    View for retrieving, updating, and deleting an event registration
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, registration_id):
        """
        Get details of a specific event registration
        """
        try:
            registration = EventRegistration.objects.get(pk=registration_id)
        except EventRegistration.DoesNotExist:
            return self.error_response("Event registration not found", status=status.HTTP_404_NOT_FOUND)

        serializer = EventRegistrationSerializer(registration)
        return self.success_response(serializer.data)

    def put(self, request, registration_id):
        """
        Update an existing event registration
        """
        try:
            registration = EventRegistration.objects.get(pk=registration_id)
        except EventRegistration.DoesNotExist:
            return self.error_response("Event registration not found", status=status.HTTP_404_NOT_FOUND)

        # Check if registration can be modified
        if not registration.can_be_modified():
            return self.error_response("Registration cannot be modified after payment is completed")

        serializer = EventRegistrationSerializer(registration, data=request.data, partial=True)
        if serializer.is_valid():
            updated_registration = serializer.save()
            # Recalculate total amount
            updated_registration.calculate_total_amount()
            updated_registration.save()
            return self.success_response(EventRegistrationSerializer(updated_registration).data)
        return self.error_response(serializer.errors)

    def delete(self, request, registration_id):
        """
        Delete an event registration
        """
        try:
            registration = EventRegistration.objects.get(pk=registration_id)
        except EventRegistration.DoesNotExist:
            return self.error_response("Event registration not found", status=status.HTTP_404_NOT_FOUND)

        # Check if registration can be modified
        if not registration.can_be_modified():
            return self.error_response("Registration cannot be deleted after payment is completed")

        registration.delete()
        return self.success_response({"message": "Event registration deleted successfully"})


class EventRegistrationByEventView(BaseAPIView, ListAPIView):
    """
    View for listing event registrations for a specific event
    """
    serializer_class = EventRegistrationSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = EventRegistrationFilter

    def get_queryset(self):
        """
        Get queryset filtered by event_id
        """
        event_id = self.kwargs.get('event_id')
        try:
            event = Event.objects.get(pk=event_id)
            return EventRegistration.objects.filter(event=event)
        except Event.DoesNotExist:
            return EventRegistration.objects.none()

    def list(self, request, *args, **kwargs):
        """
        Get a paginated and filtered list of event registrations for a specific event
        """
        event_id = kwargs.get('event_id')
        try:
            event = Event.objects.get(pk=event_id)
        except Event.DoesNotExist:
            return APIResponse(
                message="Event not found",
                status_code=status.HTTP_404_NOT_FOUND
            )

        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message=f"Event registrations for event {event.event_name} retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message=f"Event registrations for event {event.event_name} retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class RegisterForEventView(BaseAPIView):
    """
    View for registering for an event
    """
    def post(self, request, event_id):
        """
        Register for an event
        """
        try:
            event = Event.objects.get(pk=event_id)
        except Event.DoesNotExist:
            return self.error_response("Event not found", status=status.HTTP_404_NOT_FOUND)

        # Check if event is active
        if not event.is_active:
            return self.error_response("Event is not active")

        # Add event to request data
        request_data = request.data.copy()
        request_data['event'] = event.id

        # Check if the email is already registered for this event
        email = request_data.get('email')
        if email and EventRegistration.objects.filter(event=event, email=email).exists():
            return self.error_response("You are already registered for this event")

        # Determine registration type based on late registration date
        if event.is_late_registration:
            request_data['registration_type'] = 'LATE'
        else:
            request_data['registration_type'] = 'NORMAL'

        # Try to find member by email if authenticated
        member = None
        if request.user.is_authenticated:
            try:
                member = Member.objects.get(email=request.user.email)
                request_data['member'] = member.id
            except Member.DoesNotExist:
                pass

        # Calculate amounts - always calculate these server-side regardless of client input
        base_amount = 0
        guest_amount = 0

        # Log client-provided values for debugging
        try:
            client_base_amount = request_data.get('base_amount')
            client_guest_amount = request_data.get('guest_amount')
            client_total_amount = request_data.get('total_amount')

            if client_base_amount is not None and client_guest_amount is not None and client_total_amount is not None:
                print(f"Client provided amounts: base={client_base_amount}, guest={client_guest_amount}, total={client_total_amount}")
        except Exception as e:
            print(f"Error reading client amounts: {str(e)}")

        # Always calculate server-side for security and accuracy
        try:
            if event.is_late_registration:
                base_amount = float(event.registration_fee_late) * int(request_data.get('number_of_participants', 1))
            else:
                base_amount = float(event.registration_fee_normal) * int(request_data.get('number_of_participants', 1))

            guest_amount = float(event.guest_fee) * int(request_data.get('number_of_guests', 0))
            total_amount = base_amount + guest_amount

            # Add calculated amounts to request data - this will override any client values
            request_data['base_amount'] = base_amount
            request_data['guest_amount'] = guest_amount
            request_data['total_amount'] = total_amount

            print(f"Server calculated amounts: base={base_amount}, guest={guest_amount}, total={total_amount}")
        except Exception as e:
            print(f"Error calculating amounts: {str(e)}")
            # Ensure we always have valid values even if calculation fails
            request_data['base_amount'] = request_data.get('base_amount', 0) or 0
            request_data['guest_amount'] = request_data.get('guest_amount', 0) or 0
            request_data['total_amount'] = request_data.get('total_amount', 0) or 0

        # Create registration with proper locking for concurrent operations
        with transaction.atomic():
            try:
                # Get a fresh copy of the event with a SELECT FOR UPDATE to lock the row
                event = Event.objects.select_for_update().get(pk=event_id)

                # Check if event has reached maximum participants (inside transaction)
                if event.registrations.count() >= event.max_participants:
                    return self.error_response("Event has reached maximum participants")

                # Check again if the email is already registered (inside transaction)
                if email and EventRegistration.objects.filter(event=event, email=email).exists():
                    return self.error_response("You are already registered for this event")

                serializer = EventRegistrationSerializer(data=request_data)

                if serializer.is_valid():
                    # Explicitly check for base_amount before saving
                    if 'base_amount' not in serializer.validated_data or serializer.validated_data['base_amount'] is None:
                        serializer.validated_data['base_amount'] = request_data.get('base_amount', 0) or 0

                    registration = serializer.save()

                    # Generate response with payment URL if needed
                    response_data = EventRegistrationSerializer(registration).data

                    # Add PayPal approval URL if needed
                    # This would typically be handled by a payment service
                    response_data['approval_url'] = f"/api/payments/event/{registration.id}/"

                    return self.success_response(response_data, status=status.HTTP_201_CREATED)
                else:
                    # Log validation errors
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"Event registration validation failed: {serializer.errors}")

                    # Use error_response which will now automatically format serializer.errors
                    return self.error_response(serializer.errors)
            except Event.DoesNotExist:
                return self.error_response("Event not found", status=status.HTTP_404_NOT_FOUND)
            except IntegrityError as e:
                # This could happen if there's a unique constraint violation
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"IntegrityError registering for event: {str(e)}")

                if "email" in str(e).lower():
                    return self.error_response("You are already registered for this event")
                return self.error_response("Error registering for event: database constraint violation")
            except Exception as e:
                # Log the error but don't expose details in production
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error registering for event: {str(e)}")
                return self.error_response("An error occurred during registration. Please try again later.")

from django.db.models import Count
from django_filters.rest_framework import Django<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import status
from rest_framework.generics import ListAPIView

from common.utils import track_activity
from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from common.pagination import StandardPagination
from common.filters import Department<PERSON>ilter
from core.models import Department
from core.serializers import DepartmentsSerializer


class DepartmentListView(BaseAPIView, ListAPIView):
    """
    List all departments with filtering and pagination (admin only)
    """
    queryset = Department.objects.annotate(member_count=Count('members')).order_by('name')
    serializer_class = DepartmentsSerializer
    permission_classes = [IsStaffUser]
    pagination_class = StandardPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = DepartmentFilter

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set the model in the filter class
        self.filterset_class.Meta.model = Department

    def get_paginated_response(self, data):
        """
        Custom paginated response to match the expected format in tests.
        """
        # Create a standard paginated response
        paginated_response = self.paginator.get_paginated_response(data)
        # Return with the custom APIResponse format - include all pagination data
        return APIResponse(data=paginated_response.data)

    @track_activity(description="Viewed departments list")
    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
                
            serializer = self.get_serializer(queryset, many=True)
            return APIResponse(data=serializer.data)
        except Exception as e:
            return APIResponse(
                message=f"Error retrieving departments: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )

from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView, UpdateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.filters import OrderingFilter
from django_filters import rest_framework as filters

from common.views import BaseAPIView
from common.views import APIResponse
from common.pagination import StandardPagination
from common.filters.public_member_filters import PublicMemberFilter
from core.models import Member
from core.serializers.auth import UserDetailsSerializer
from core.serializers import MemberPublicSerializer


class MembershipRosterPublicListView(BaseAPIView, ListAPIView):
    """
    List all active members (public view)
    Only returns name, department, and city fields
    """
    queryset = Member.objects.filter(active=True, is_deceased=False).select_related('department')
    serializer_class = MemberPublicSerializer
    pagination_class = StandardPagination
    permission_classes = [IsAuthenticated]
    filter_backends = (filters.DjangoFilterBackend, OrderingFilter)
    filterset_class = PublicMemberFilter
    ordering_fields = ['name', 'department__name', 'city']
    ordering = ['name']

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Active members retrieved successfully",
                status_code=status.HTTP_200_OK
            )
            
        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Active members retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class MembershipRosterPublicDetailView(BaseAPIView, RetrieveAPIView, UpdateAPIView):
    """
    Retrieve and update the logged-in member's details only
    """
    queryset = Member.objects.filter(active=True, is_deceased=False)
    serializer_class = UserDetailsSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """
        Get the user directly from the authentication token
        """
        return self.request.user

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return APIResponse(
            data=serializer.data,
            message="Member details retrieved successfully",
            status_code=status.HTTP_200_OK
        )

    def update(self, request, *args, **kwargs):
        """
        Update the logged-in member's own details
        """
        instance = self.get_object()
        partial = kwargs.pop('partial', False)
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return APIResponse(
            data=serializer.data,
            message="Member details updated successfully",
            status_code=status.HTTP_200_OK
        )


class MembershipClassTypesPublicAPIView(BaseAPIView):
    """
    Get all membership class types (public endpoint)
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        # Dynamically retrieve membership class types from the MembershipStatus enum
        membership_classes = [
            {'id': status.value, 'name': status.label}
            for status in Member.MembershipStatus
        ]
        
        return APIResponse(
            data=membership_classes,
            message="Membership class types retrieved successfully",
            status_code=status.HTTP_200_OK
        )

"""
Serializer for user registration.
"""
from typing import Dict, Any

from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError

from common.utils.email import send_verification_email
from common.models import EmailVerification

Member = get_user_model()


class RegisterSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    
    This serializer creates a new user with inactive status and sends an email verification
    link. The user will need to:
    1. Verify their email by clicking the link sent to their email
    2. Wait for an administrator to activate their account
    """
    password = serializers.Char<PERSON>ield(
        write_only=True, 
        required=True, 
        style={'input_type': 'password'},
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    class Meta:
        model = Member
        fields = ['email', 'name', 'password', 'confirm_password']
        extra_kwargs = {
            'email': {'required': True},
            'name': {'required': True}
        }

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate password confirmation and email uniqueness
        """
        # Email uniqueness is implicitly checked by the model,
        # but we can make a clearer error message
        email = attrs.get('email')
        if Member.objects.filter(email=email).exists():
            # Check if the user exists and their email verification status
            existing_user = Member.objects.get(email=email)
            
            # Check if user has any email verification records that are verified
            has_verified_email = EmailVerification.objects.filter(
                user=existing_user, 
                verified=True
            ).exists()
            
            if not has_verified_email:
                # Email registered but not verified
                raise serializers.ValidationError(
                    {"email": "This email is already registered but not verified. Please check your email for the verification link or request a new one."}
                )
            elif not existing_user.active:
                # Email verified but account not activated by admin
                raise serializers.ValidationError(
                    {"email": "Your email has been verified but your account is not yet activated. Please contact an administrator for approval."}
                )
            else:
                # Email verified and account active
                raise serializers.ValidationError(
                    {"email": "A user with this email already exists. Please login instead."}
                )
            
        # Now that we know the email is valid, check passwords
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError(
                {"confirm_password": "Password fields didn't match."}
            )
            
        return attrs

    def create(self, validated_data: Dict[str, Any]) -> Member:
        """
        Create and return a new user with encrypted password and send verification email
        
        Returns:
            Member: The newly created inactive user
        """
        # Remove confirm_password from the data as it's not needed for user creation
        validated_data.pop('confirm_password', None)
        
        # Create the user with only the fields needed for creation
        try:
            user = Member.objects.create_user(
                email=validated_data['email'],
                password=validated_data['password'],
                name=validated_data.get('name', ''),
                active=False,  # Set default to inactive, requiring email verification
                membership_active=False  # Set default membership status to inactive
            )
            
            # Send verification email - this creates an EmailVerification record
            # with a unique key and sends an email to the user with a verification link
            verification = send_verification_email(user)
            
            return user
        except DjangoValidationError as e:
            raise serializers.ValidationError({"detail": e.messages}) 
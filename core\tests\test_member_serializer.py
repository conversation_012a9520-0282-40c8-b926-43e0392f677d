"""
Test cases for Member serializers.
"""
from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APITestCase
from rest_framework.exceptions import ValidationError

from core.models import Member, Department
from core.serializers.member import (
    DepartmentsSerializer,
    MembershipRosterAdminSerializer,
    MemberIdSerializer
)


class DepartmentsSerializerTests(APITestCase):
    """Test cases for the DepartmentsSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create test departments
        self.department1 = Department.objects.create(
            name='Test Department 1',
            department_address1='123 Main St',
            department_address2='Suite 100',
            department_city='Anytown',
            department_district='North',
            department_county='County1',
            department_state='MS',
            department_zip_code='12345',
            billing_address1='456 Billing St',
            billing_address2='Floor 2',
            billing_city='Billtown',
            billing_district='South',
            billing_county='County2',
            billing_state='MS',
            billing_zip_code='67890'
        )
        
        self.department2 = Department.objects.create(
            name='Test Department 2',
            department_address1='789 Side St',
            department_city='Othertown',
            department_state='MS'
        )
        
        # Create members in the departments
        self.member1 = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Member One',
            department=self.department1
        )
        
        self.member2 = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Member Two',
            department=self.department1
        )
        
        self.member3 = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Member Three',
            department=self.department2
        )
        
        # Department data for creation tests
        self.department_data = {
            'name': 'New Department',
            'department_address1': '321 New St',
            'department_city': 'Newtown',
            'department_state': 'MS',
            'department_zip_code': '54321'
        }
    
    def test_department_serialization(self):
        """Test serialization of a department with full details."""
        serializer = DepartmentsSerializer(self.department1)
        data = serializer.data
        
        # Test that all fields are properly serialized
        self.assertEqual(data['name'], self.department1.name)
        self.assertEqual(data['department_address1'], self.department1.department_address1)
        self.assertEqual(data['department_address2'], self.department1.department_address2)
        self.assertEqual(data['department_city'], self.department1.department_city)
        self.assertEqual(data['department_district'], self.department1.department_district)
        self.assertEqual(data['department_county'], self.department1.department_county)
        self.assertEqual(data['department_state'], self.department1.department_state)
        self.assertEqual(data['department_zip_code'], self.department1.department_zip_code)
        
        # Test billing fields
        self.assertEqual(data['billing_address1'], self.department1.billing_address1)
        self.assertEqual(data['billing_address2'], self.department1.billing_address2)
        self.assertEqual(data['billing_city'], self.department1.billing_city)
        self.assertEqual(data['billing_district'], self.department1.billing_district)
        self.assertEqual(data['billing_county'], self.department1.billing_county)
        self.assertEqual(data['billing_state'], self.department1.billing_state)
        self.assertEqual(data['billing_zip_code'], self.department1.billing_zip_code)
    
    def test_department_serialization_with_member_count(self):
        """Test serialization of a department with member count."""
        # Add member_count to the department instance
        self.department1.member_count = 2
        serializer = DepartmentsSerializer(self.department1)
        data = serializer.data
        
        # Test that member_count is included
        self.assertEqual(data['member_count'], 2)
    
    def test_minimal_department_serialization(self):
        """Test serialization of a department with minimal details."""
        serializer = DepartmentsSerializer(self.department2)
        data = serializer.data
        
        # Test that required fields are properly serialized
        self.assertEqual(data['name'], self.department2.name)
        self.assertEqual(data['department_address1'], self.department2.department_address1)
        self.assertEqual(data['department_city'], self.department2.department_city)
        self.assertEqual(data['department_state'], self.department2.department_state)
        
        # Test that optional fields have default values
        self.assertEqual(data['department_address2'], '')
        self.assertEqual(data['department_district'], '')
        self.assertEqual(data['department_county'], '')
        self.assertEqual(data['department_zip_code'], '')
        
        # Test billing fields have default values
        self.assertEqual(data['billing_address1'], '')
        self.assertEqual(data['billing_address2'], '')
        self.assertEqual(data['billing_city'], '')
        self.assertEqual(data['billing_district'], '')
        self.assertEqual(data['billing_county'], '')
        self.assertEqual(data['billing_state'], 'MS')
        self.assertEqual(data['billing_zip_code'], '')
    
    def test_department_creation(self):
        """Test creating a department with the serializer."""
        serializer = DepartmentsSerializer(data=self.department_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the department
        department = serializer.save()
        
        # Verify fields were saved correctly
        self.assertEqual(department.name, self.department_data['name'])
        self.assertEqual(department.department_address1, self.department_data['department_address1'])
        self.assertEqual(department.department_city, self.department_data['department_city'])
        self.assertEqual(department.department_state, self.department_data['department_state'])
        self.assertEqual(department.department_zip_code, self.department_data['department_zip_code'])
    
    def test_department_update(self):
        """Test updating a department with the serializer."""
        update_data = {
            'name': 'Updated Department',
            'department_address1': '999 Update St',
            'department_city': 'Updateville'
        }
        
        serializer = DepartmentsSerializer(self.department2, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        # Save the updated department
        updated_department = serializer.save()
        
        # Verify fields were updated
        self.assertEqual(updated_department.name, update_data['name'])
        self.assertEqual(updated_department.department_address1, update_data['department_address1'])
        self.assertEqual(updated_department.department_city, update_data['department_city'])
        
        # Verify fields not in update_data remain unchanged
        self.assertEqual(updated_department.department_state, self.department2.department_state)
    
    def test_department_validation(self):
        """Test validation of department data."""
        # Test with duplicate name (should fail validation)
        duplicate_data = self.department_data.copy()
        duplicate_data['name'] = self.department1.name
        
        serializer = DepartmentsSerializer(data=duplicate_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)
        
        # Test with missing required field
        missing_data = self.department_data.copy()
        missing_data.pop('name')
        
        serializer = DepartmentsSerializer(data=missing_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)


class MembershipRosterAdminSerializerTests(APITestCase):
    """Test cases for the MembershipRosterAdminSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Roster Test Department',
            department_address1='123 Roster St',
            department_city='Rostertown',
            department_state='MS'
        )
        
        # Create a member with full details
        self.member = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Admin Roster Member',
            mi='A',
            dst='North',
            title='Mr.',
            address='456 Member St',
            city='Membertown',
            st='MS',
            zip_code='12345',
            home_phone='************',
            business_phone='************',
            department=self.department,
            membership_class=Member.MembershipStatus.MEMBER,
            executive_board=True,
            committee_member=True,
            committee='Test Committee',
            new_member=False,
            lifetime=True,
            paid_next_year=True,
            lapel_pin='Gold',
            is_deceased=False,
            active=True,
            membership_active=True,
            orig_join_date=timezone.now() - timedelta(days=365),
            notes='Test notes',
            picture='base64encoded'
        )
        
        # Member data for creation tests
        self.member_data = {
            'email': '<EMAIL>',
            'password': 'password123',
            'name': 'New Roster Member',
            'department': self.department.id,
            'membership_class': Member.MembershipStatus.ASSOCIATE_MEMBER,
            'active': True
        }
    
    def test_member_serialization(self):
        """Test serialization of a member with full details."""
        # Add payment status fields (normally calculated in view)
        self.member.has_paid_this_year = True
        self.member.has_paid_last_year = True
        self.member.has_paid_in_last_five_years = True
        self.member.has_paid_between_three_and_five_years_ago = False
        
        serializer = MembershipRosterAdminSerializer(self.member)
        data = serializer.data
        
        # Test that all fields are properly serialized
        self.assertEqual(data['name'], self.member.name)
        self.assertEqual(data['mi'], self.member.mi)
        self.assertEqual(data['dst'], self.member.dst)
        self.assertEqual(data['title'], self.member.title)
        self.assertEqual(data['email'], self.member.email)
        self.assertEqual(data['address'], self.member.address)
        self.assertEqual(data['city'], self.member.city)
        self.assertEqual(data['st'], self.member.st)
        self.assertEqual(data['zip_code'], self.member.zip_code)
        self.assertEqual(data['home_phone'], self.member.home_phone)
        self.assertEqual(data['business_phone'], self.member.business_phone)
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['department_name'], self.department.name)
        self.assertEqual(data['membership_class'], self.member.membership_class)
        self.assertEqual(data['membership_class_display'], 'Member')
        self.assertEqual(data['executive_board'], self.member.executive_board)
        self.assertEqual(data['committee_member'], self.member.committee_member)
        self.assertEqual(data['committee'], self.member.committee)
        self.assertEqual(data['new_member'], self.member.new_member)
        self.assertEqual(data['lifetime'], self.member.lifetime)
        self.assertEqual(data['paid_next_year'], self.member.paid_next_year)
        self.assertEqual(data['lapel_pin'], self.member.lapel_pin)
        self.assertEqual(data['is_deceased'], self.member.is_deceased)
        self.assertEqual(data['active'], self.member.active)
        self.assertEqual(data['membership_active'], self.member.membership_active)
        self.assertEqual(data['notes'], self.member.notes)
        self.assertEqual(data['picture'], self.member.picture)
        
        # Test payment status fields
        self.assertEqual(data['has_paid_this_year'], True)
        self.assertEqual(data['has_paid_last_year'], True)
        self.assertEqual(data['has_paid_in_last_five_years'], True)
        self.assertEqual(data['has_paid_between_three_and_five_years_ago'], False)
    
    def test_member_creation(self):
        """Test creating a member with the serializer."""
        serializer = MembershipRosterAdminSerializer(data=self.member_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the member
        member = serializer.save()
        
        # Verify fields were saved correctly
        self.assertEqual(member.email, self.member_data['email'])
        self.assertEqual(member.name, self.member_data['name'])
        self.assertEqual(member.department.id, self.member_data['department'])
        self.assertEqual(member.membership_class, self.member_data['membership_class'])
        self.assertEqual(member.active, self.member_data['active'])
    
    def test_member_update(self):
        """Test updating a member with the serializer."""
        update_data = {
            'name': 'Updated Roster Member',
            'title': 'Dr.',
            'executive_board': False,
            'notes': 'Updated notes'
        }
        
        serializer = MembershipRosterAdminSerializer(self.member, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        # Save the updated member
        updated_member = serializer.save()
        
        # Verify fields were updated
        self.assertEqual(updated_member.name, update_data['name'])
        self.assertEqual(updated_member.title, update_data['title'])
        self.assertEqual(updated_member.executive_board, update_data['executive_board'])
        self.assertEqual(updated_member.notes, update_data['notes'])
        
        # Verify fields not in update_data remain unchanged
        self.assertEqual(updated_member.email, self.member.email)
        self.assertEqual(updated_member.department, self.department)
    
    def test_serialization_without_department(self):
        """Test serialization of a member without a department."""
        # Create a member without department
        member_no_dept = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='No Department Member',
            department=None
        )
        
        serializer = MembershipRosterAdminSerializer(member_no_dept)
        data = serializer.data
        
        # Verify department related fields
        self.assertIsNone(data['department'])
        self.assertEqual(data['department_name'], '')
    
    def test_serialization_without_payment_status(self):
        """Test serialization of a member without payment status fields."""
        serializer = MembershipRosterAdminSerializer(self.member)
        data = serializer.data
        
        # Payment status fields should be false by default since they're read-only
        self.assertFalse(data['has_paid_this_year'])
        self.assertFalse(data['has_paid_last_year'])
        self.assertFalse(data['has_paid_in_last_five_years'])
        self.assertFalse(data['has_paid_between_three_and_five_years_ago'])


class MemberIdSerializerTests(TestCase):
    """Test cases for the MemberIdSerializer."""

    def setUp(self):
        """Set up test data."""
        # Valid member IDs data
        self.valid_member_ids = {
            'member_ids': [1, 2, 3, 4, 5]
        }
        
        # Invalid data (not a list)
        self.invalid_not_list = {
            'member_ids': 'not a list'
        }
        
        # Invalid data (list with non-integers)
        self.invalid_not_integers = {
            'member_ids': [1, 2, 'three', 4, 5]
        }
        
        # Invalid data (missing required field)
        self.invalid_missing_field = {
            'some_other_field': [1, 2, 3]
        }
    
    def test_valid_serialization(self):
        """Test serialization with valid data."""
        serializer = MemberIdSerializer(data=self.valid_member_ids)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['member_ids'], self.valid_member_ids['member_ids'])
    
    def test_validation_not_list(self):
        """Test validation fails when member_ids is not a list."""
        serializer = MemberIdSerializer(data=self.invalid_not_list)
        self.assertFalse(serializer.is_valid())
        self.assertIn('member_ids', serializer.errors)
    
    def test_validation_not_integers(self):
        """Test validation fails when member_ids contains non-integers."""
        serializer = MemberIdSerializer(data=self.invalid_not_integers)
        self.assertFalse(serializer.is_valid())
        self.assertIn('member_ids', serializer.errors)
    
    def test_validation_missing_field(self):
        """Test validation fails when required field is missing."""
        serializer = MemberIdSerializer(data=self.invalid_missing_field)
        self.assertFalse(serializer.is_valid())
        self.assertIn('member_ids', serializer.errors)
    
    def test_empty_list(self):
        """Test validation with an empty list."""
        empty_data = {
            'member_ids': []
        }
        serializer = MemberIdSerializer(data=empty_data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['member_ids'], [])
    
    def test_large_list(self):
        """Test serialization with a large list of IDs."""
        large_list = {
            'member_ids': list(range(1, 101))  # 100 member IDs
        }
        serializer = MemberIdSerializer(data=large_list)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(len(serializer.validated_data['member_ids']), 100) 
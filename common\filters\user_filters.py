from django_filters import rest_framework as filters
from django.contrib.auth import get_user_model

Member = get_user_model()

class UnverifiedUserFilter(filters.FilterSet):
    """
    Filter for unverified users
    """
    id = filters.NumberFilter(field_name='id')
    name = filters.CharFilter(lookup_expr='icontains')
    email = filters.CharFilter(lookup_expr='icontains')
    department = filters.NumberFilter(field_name='department__id')
    department_name = filters.CharFilter(field_name='department__name', lookup_expr='icontains')

    class Meta:
        model = Member
        fields = ['id', 'name', 'email', 'department', 'department_name'] 
"""
PDF generation utilities for the core app.
"""
import io
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfgen import canvas
from reportlab.platypus.flowables import KeepTogether

def generate_member_list_pdf(members, output_path=None):
    """
    Generate a PDF with a list of members.
    
    Args:
        members: QuerySet or list of Member objects
        output_path: Optional path to save the PDF to
        
    Returns:
        BytesIO object containing the PDF data if output_path is None,
        otherwise saves the PDF to the specified path and returns None
    """
    # Create a buffer if no output path is provided
    if output_path is None:
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
    else:
        doc = SimpleDocTemplate(output_path, pagesize=letter)
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = styles['Heading1']
    subtitle_style = styles['Heading2']
    normal_style = styles['Normal']
    
    # Create the content
    elements = []
    
    # Add title
    elements.append(Paragraph("Member List", title_style))
    elements.append(Spacer(1, 0.25 * inch))
    
    # Create table data
    table_data = [["Name", "Email", "Department", "Membership Status", "Address"]]
    
    # Add member data to table
    for member in members:
        department_name = member.department.name if member.department else "N/A"
        membership_status = member.get_membership_class_display() if hasattr(member, 'get_membership_class_display') else "N/A"
        address = f"{member.address}, {member.city}, {member.st} {member.zip_code}" if member.address else "N/A"
        
        table_data.append([
            member.name,
            member.email,
            department_name,
            membership_status,
            address
        ])
    
    # Create table and set style
    table = Table(table_data, colWidths=[1.5*inch, 2*inch, 1.5*inch, 1.2*inch, 2.5*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    
    elements.append(table)
    
    # Build the PDF
    doc.build(elements)
    
    # Return the buffer if no output path was provided
    if output_path is None:
        buffer.seek(0)
        return buffer.getvalue()
    return None

def generate_event_roster_pdf(event, registrations, output_path=None):
    """
    Generate a PDF roster for an event.
    
    Args:
        event: Event object
        registrations: QuerySet of EventRegistration objects
        output_path: Optional path to save the PDF to
        
    Returns:
        BytesIO object containing the PDF data if output_path is None,
        otherwise saves the PDF to the specified path and returns None
    """
    # Create a buffer if no output path is provided
    if output_path is None:
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
    else:
        doc = SimpleDocTemplate(output_path, pagesize=letter)
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = styles['Heading1']
    subtitle_style = styles['Heading2']
    normal_style = styles['Normal']
    
    # Create the content
    elements = []
    
    # Add title
    elements.append(Paragraph(f"Event Roster: {event.event_name}", title_style))
    elements.append(Spacer(1, 0.25 * inch))
    
    # Add event details
    event_details = f"Date: {event.event_date}<br/>"
    event_details += f"Location: {event.event_location}<br/>"
    event_details += f"Total Registrations: {registrations.count()}"
    elements.append(Paragraph(event_details, normal_style))
    elements.append(Spacer(1, 0.25 * inch))
    
    # Create table data for registrations
    table_data = [["Name", "Department", "Email", "Phone", "Payment Status"]]
    
    # Add registration data to table
    for reg in registrations:
        name = f"{reg.first_name} {reg.last_name}"
        
        table_data.append([
            name,
            reg.fire_department,
            reg.email,
            reg.phone,
            reg.payment_status
        ])
    
    # Create table and set style
    table = Table(table_data, colWidths=[1.5*inch, 1.8*inch, 2*inch, 1*inch, 1.5*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    
    elements.append(table)
    
    # Build the PDF
    doc.build(elements)
    
    # Return the buffer if no output path was provided
    if output_path is None:
        buffer.seek(0)
        return buffer.getvalue()
    return None

def generate_address_labels(members, label_format='avery5160', start_position=1, include_department=True, output_path=None):
    """
    Generate a PDF with address labels for members.
    
    Args:
        members: QuerySet or list of Member objects
        label_format: The Avery label format to use (avery5160, avery5163, avery5293)
        start_position: Starting position on the label sheet (1-based)
        include_department: Whether to include department name in the label
        output_path: Optional path to save the PDF to
        
    Returns:
        BytesIO object containing the PDF data if output_path is None,
        otherwise saves the PDF to the specified path and returns None
    """
    # Create a buffer if no output path is provided
    if output_path is None:
        buffer = io.BytesIO()
    else:
        buffer = output_path
    
    # Define label formats
    formats = {
        'avery5160': {
            'labels_per_row': 3,
            'rows_per_page': 10,
            'label_width': 2.63 * inch,
            'label_height': 1.0 * inch,
            'margin_top': 0.5 * inch,
            'margin_left': 0.19 * inch,
            'gap_width': 0.12 * inch,
            'gap_height': 0.0 * inch
        },
        'avery5163': {
            'labels_per_row': 2,
            'rows_per_page': 5,
            'label_width': 4.0 * inch,
            'label_height': 2.0 * inch,
            'margin_top': 0.5 * inch,
            'margin_left': 0.25 * inch,
            'gap_width': 0.125 * inch,
            'gap_height': 0.0 * inch
        },
        'avery5293': {
            'labels_per_row': 3,
            'rows_per_page': 11,
            'label_width': 2.63 * inch,
            'label_height': 0.92 * inch,
            'margin_top': 0.5 * inch,
            'margin_left': 0.19 * inch,
            'gap_width': 0.12 * inch,
            'gap_height': 0.0 * inch
        }
    }
    
    # Validate membership data
    if not members or not hasattr(members, '__iter__') or len(list(members)) == 0:
        raise ValueError("No valid members provided")
        
    # Check if requested format is supported
    if label_format not in formats:
        raise ValueError(f"Unsupported label format: {label_format}. Supported formats are: {', '.join(formats.keys())}")
    
    # Validate start position
    if not isinstance(start_position, int) or start_position < 1:
        raise ValueError("Start position must be a positive integer")
    
    # Get format specifications
    format_specs = formats[label_format]
    
    # Create a canvas for drawing
    c = canvas.Canvas(buffer, pagesize=letter)
    
    # Calculate labels per page
    labels_per_page = format_specs['labels_per_row'] * format_specs['rows_per_page']
    
    # Calculate current position
    current_position = start_position
    
    # Process each member
    for member in members:
        # Calculate row and column for current position
        row = (current_position - 1) // format_specs['labels_per_row']
        col = (current_position - 1) % format_specs['labels_per_row']
        
        # Check if we need a new page
        if row >= format_specs['rows_per_page']:
            c.showPage()
            row = 0
            col = 0
            current_position = 1
        
        # Calculate label position
        x = format_specs['margin_left'] + col * (format_specs['label_width'] + format_specs['gap_width'])
        y = letter[1] - format_specs['margin_top'] - (row + 1) * format_specs['label_height']
        
        # Draw label content
        c.setFont("Helvetica", 10)
        try:
            c.drawString(x + 10, y + format_specs['label_height'] - 20, member.name)
            c.drawString(x + 10, y + format_specs['label_height'] - 35, member.address)
            c.drawString(x + 10, y + format_specs['label_height'] - 50, f"{member.city}, {member.st} {member.zip_code}")
            
            # Include department if requested
            if include_department and member.department:
                c.drawString(x + 10, y + format_specs['label_height'] - 65, member.department.name)
        except AttributeError as e:
            raise ValueError(f"Member data is invalid or incomplete: {str(e)}")
        
        # Move to next position
        current_position += 1
    
    # Save the PDF
    c.showPage()
    c.save()
    
    # Return the buffer if no output path was provided
    if output_path is None:
        buffer.seek(0)
        return buffer.getvalue()
    return None

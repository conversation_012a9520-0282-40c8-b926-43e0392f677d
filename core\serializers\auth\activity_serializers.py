"""
Serializers for user activity tracking.
"""

from rest_framework import serializers
from common.models import UserActivity
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class UserActivitySerializer(serializers.ModelSerializer):
    """
    Serializer for UserActivity model
    """
    user_email = serializers.SerializerMethodField()
    user_name = serializers.SerializerMethodField()
    time_ago = serializers.SerializerMethodField()

    class Meta:
        model = UserActivity
        fields = ['id', 'user', 'user_email', 'user_name', 'description', 'timestamp', 'time_ago']
        read_only_fields = ['id', 'user', 'timestamp']

    def get_user_email(self, obj):
        return obj.user.email if obj.user else None

    def get_user_name(self, obj):
        if not obj.user:
            return None
        return obj.user.name if obj.user.name else obj.user.email

    def get_time_ago(self, obj):
        """
        Calculate a human-readable time difference from the timestamp
        Returns time in seconds, minutes, hours, days, months, or years ago
        """
        now = timezone.now()
        diff = now - obj.timestamp

        # Handle seconds
        seconds = diff.total_seconds()
        if seconds < 60:
            return f"{int(seconds)} seconds ago"

        # Handle minutes
        minutes = seconds / 60
        if minutes < 60:
            return f"{int(minutes)} minutes ago"

        # Handle hours
        hours = minutes / 60
        if hours < 24:
            return f"{int(hours)} hours ago"

        # Handle days
        days = hours / 24
        if days < 30:
            return f"{int(days)} days ago"

        # Handle months
        months = days / 30
        if months < 12:
            return f"{int(months)} months ago"

        # Handle years
        years = months / 12
        return f"{int(years)} years ago"
"""
Tests for the password reset email sending functionality in the common app.

This module contains comprehensive tests for the send_password_reset_email function.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch, MagicMock

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings

from common.utils.email import send_password_reset_email
from common.models import PasswordReset

User = get_user_model()


class SendPasswordResetEmailTests(TestCase):
    """Test cases for the send_password_reset_email function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Store the expected reset URL format for testing
        self.reset_url_format = f"{settings.FRONTEND_URL}/reset-password/"

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_creates_reset(self, mock_send_mail):
        """Test that send_password_reset_email creates a reset record."""
        # Ensure no resets exist before the test
        self.assertEqual(PasswordReset.objects.count(), 0)
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a reset was created
        self.assertEqual(PasswordReset.objects.count(), 1)
        self.assertEqual(reset.user, self.user)
        self.assertFalse(reset.used)
        self.assertIsNotNone(reset.key)
        self.assertIsNotNone(reset.expires_at)
        
        # Verify expiration date is set correctly (24 hours from now)
        expected_expiry = timezone.now() + timedelta(hours=24)
        self.assertAlmostEqual(
            reset.expires_at.timestamp(),
            expected_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check email arguments in detail
        args = mock_send_mail.call_args[0]
        kwargs = mock_send_mail.call_args[1]
        
        # Subject should be about password reset
        self.assertEqual(args[0], "Reset your password")
        
        # Plain text message should contain key info
        plain_message = args[1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(reset.key), plain_message)
        self.assertIn(f"{self.reset_url_format}{reset.key}", plain_message)
        self.assertIn("24 hours", plain_message)
        
        # From email should be the default from email
        self.assertEqual(args[2], settings.DEFAULT_FROM_EMAIL)
        
        # Recipient should be the user's email
        self.assertEqual(args[3], [self.user.email])
        
        # HTML message should contain the reset key
        html_message = kwargs['html_message']
        self.assertIn(str(reset.key), html_message)
        
        # Email should not be sent silently
        self.assertFalse(kwargs['fail_silently'])

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_invalidates_existing_resets(self, mock_send_mail):
        """Test that send_password_reset_email invalidates existing unexpired resets."""
        # Create an existing reset
        existing_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=12)  # Not expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, existing_reset)
        
        # Check that the existing reset was marked as used
        existing_reset.refresh_from_db()
        self.assertTrue(existing_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_does_not_invalidate_expired_resets(self, mock_send_mail):
        """Test that send_password_reset_email doesn't invalidate already expired resets."""
        # Create an expired reset
        expired_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() - timedelta(hours=1)  # Already expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, expired_reset)
        
        # Check that the expired reset was NOT marked as used (it's already expired)
        expired_reset.refresh_from_db()
        self.assertFalse(expired_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_does_not_invalidate_used_resets(self, mock_send_mail):
        """Test that send_password_reset_email doesn't attempt to invalidate already used resets."""
        # Create an already used reset
        used_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=True,
            expires_at=timezone.now() + timedelta(hours=12)  # Not expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, used_reset)
        
        # Check that the used reset is still marked as used
        used_reset.refresh_from_db()
        self.assertTrue(used_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.render_to_string')
    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_content(self, mock_send_mail, mock_render_to_string):
        """Test the content of the password reset email."""
        # Mock render_to_string to return a simple HTML message
        mock_render_to_string.return_value = '<html>Password Reset Email</html>'
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check render_to_string call
        mock_render_to_string.assert_called_once_with('emails/reset_password.html', {
            'name': self.user.name,
            'reset_url': f"{settings.FRONTEND_URL}/reset-password/{reset.key}",
            'expiry_hours': 24,
        })
        
        # Check send_mail call
        mock_send_mail.assert_called_once_with(
            "Reset your password",
            mock_send_mail.call_args[0][1],  # plain_message (complex to check exactly)
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message='<html>Password Reset Email</html>',
            fail_silently=False,
        )
        
        # Check plain message content
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(reset.key), plain_message)
        self.assertIn("24 hours", plain_message)

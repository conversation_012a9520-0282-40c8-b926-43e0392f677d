from django.core.cache import cache
from rest_framework import status
from rest_framework.generics import UpdateAPIView

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Member
from core.serializers import MembershipRosterAdminSerializer


class MembershipRosterUpdateAdminView(BaseAPIView, UpdateAPIView):
    """
    Update a specific member's details (admin only)
    """
    queryset = Member.objects.all()
    serializer_class = MembershipRosterAdminSerializer
    permission_classes = [IsStaffUser]

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        # Clear cache after update
        cache.delete('membership_roster_queryset')
        
        return APIResponse(
            data=serializer.data,
            message="Member updated successfully",
            status_code=status.HTTP_200_OK
        ) 
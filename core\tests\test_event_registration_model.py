"""
Tests for the EventRegistration model.
This file contains comprehensive tests for the EventRegistration model,
covering all fields, methods, and edge cases.
"""
from django.test import TestCase
from django.db import IntegrityError, transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from datetime import timedelta
import json

from core.models import Department, Event, EventRegistration, Member

User = get_user_model()


class EventRegistrationModelTests(TestCase):
    """Test cases for the EventRegistration model."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Test Fire Department',
            department_address1='123 Main St',
            department_city='Anytown',
            department_state='MS',
            department_zip_code='12345'
        )

        # Create a user/member
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Registration Test User',
            department=self.department
        )

        # Create an event with all fields
        self.event = Event.objects.create(
            event_name='Registration Test Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_end_date=timezone.now().date() + timedelta(days=12),
            event_location='Test Location',
            registration_fee_normal=100.00,
            registration_fee_late=115.00,
            guest_fee=25.00,
            max_participants=100,
            event_description='Test event description',
            is_active=True,
            late_registration_date=timezone.now().date() + timedelta(days=5)
        )

        # Create another event for multi-event testing
        self.event2 = Event.objects.create(
            event_name='Second Test Event',
            event_date=timezone.now().date() + timedelta(days=20),
            event_location='Another Location',
            registration_fee_normal=150.00,
            registration_fee_late=175.00,
            guest_fee=35.00
        )

        # Basic registration data
        self.registration_data = {
            'event': self.event,
            'member': self.user,
            'first_name': 'Registration',
            'last_name': 'Test',
            'title': 'Mr.',
            'fire_department': 'Test Fire Department',
            'address': '123 Main St',
            'city': 'Anytown',
            'state': 'MS',
            'zipcode': '12345',
            'phone': '555-1234',
            'email': '<EMAIL>',
            'registration_type': 'NORMAL',
            'base_amount': 100.00,
            'guest_amount': 0.00,
            'total_amount': 100.00
        }

        # Registration data with guests
        self.registration_with_guests_data = self.registration_data.copy()
        self.registration_with_guests_data.update({
            'number_of_guests': 2,
            'guest_amount': 50.00,
            'total_amount': 150.00
        })

        # Late registration data
        self.late_registration_data = self.registration_data.copy()
        self.late_registration_data.update({
            'registration_type': 'LATE',
            'base_amount': 115.00,
            'total_amount': 115.00
        })

        # Group registration data
        self.group_registration_data = self.registration_data.copy()
        self.group_registration_data.update({
            'group_registration': True,
            'number_of_participants': 3,
            'base_amount': 300.00,
            'total_amount': 300.00
        })

        # Extra participants data for JSON field
        self.extra_participants_data = {
            'participants': [
                {
                    'first_name': 'Extra',
                    'last_name': 'Person1',
                    'email': '<EMAIL>'
                },
                {
                    'first_name': 'Extra',
                    'last_name': 'Person2',
                    'email': '<EMAIL>'
                }
            ]
        }

        # Create additional members for group registration testing
        self.group_member1 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Group Member 1',
            department=self.department
        )

        self.group_member2 = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Group Member 2',
            department=self.department
        )

        # Valid invoice number format
        self.valid_invoice = '23001-001'  # YY-DDD-XXX format

        # Invalid invoice formats for testing validation
        self.invalid_invoices = [
            '123-456',      # Wrong format
            '1234-123',     # Wrong prefix length
            '12345-12',     # Wrong counter length
            '12345-ABC',    # Non-numeric counter
            'ABCDE-123',    # Non-numeric prefix
            '12345123',     # Missing hyphen
            '-123',         # Missing prefix
            '12345-',       # Missing counter
        ]

    # --- Creation Tests ---
    def test_registration_creation_minimal(self):
        """Test creating a registration with minimal required fields."""
        minimal_data = {
            'event': self.event,
            'member': self.user,
            'first_name': 'Minimal',
            'last_name': 'Reg',
            'title': 'Mx',
            'fire_department': 'Min Dept',
            'address': '1 Min St',
            'city': 'Min City',
            'state': 'MS',
            'zipcode': '54321',
            'phone': '555-0000',
            'email': '<EMAIL>',
            'registration_type': 'NORMAL',
            # Amounts should be calculated or defaulted
        }
        registration = EventRegistration.objects.create(**minimal_data)
        self.assertIsNotNone(registration.pk)
        self.assertEqual(registration.event, self.event)
        self.assertEqual(registration.member, self.user)
        self.assertEqual(registration.first_name, 'Minimal')
        self.assertEqual(registration.payment_status, 'PENDING') # Default
        self.assertEqual(registration.number_of_participants, 1) # Default
        self.assertEqual(registration.number_of_guests, 0) # Default
        self.assertEqual(registration.base_amount, 0) # Default before save calc
        self.assertEqual(registration.guest_amount, 0) # Default before save calc
        self.assertEqual(registration.total_amount, 0) # Default before save calc
        self.assertTrue(registration.registration_date <= timezone.now())
        self.assertFalse(registration.group_registration) # Default
        self.assertEqual(registration.extra_participants, {}) # Default

    def test_registration_creation_maximal(self):
        """Test creating a registration with all possible fields."""
        maximal_data = self.registration_data.copy()
        maximal_data.update({
            'number_of_participants': 2,
            'number_of_guests': 1,
            'notes': 'Some important notes.',
            'invoice_number': self.valid_invoice,
            'extra_participants': self.extra_participants_data,
            'group_registration': True,
        })
        registration = EventRegistration.objects.create(**maximal_data)

        # Add group members after creation
        registration.group_members.add(self.group_member1, self.group_member2)
        registration.save()

        # Re-fetch to confirm save
        registration = EventRegistration.objects.get(pk=registration.pk)

        self.assertEqual(registration.event, self.event)
        self.assertEqual(registration.member, self.user)
        self.assertEqual(registration.first_name, 'Registration')
        self.assertEqual(registration.number_of_participants, 2)
        self.assertEqual(registration.number_of_guests, 1)
        self.assertEqual(registration.notes, 'Some important notes.')
        self.assertEqual(registration.invoice_number, self.valid_invoice)
        self.assertEqual(registration.extra_participants, self.extra_participants_data)
        self.assertTrue(registration.group_registration)
        self.assertEqual(registration.group_members.count(), 2)
        self.assertIn(self.group_member1, registration.group_members.all())
        self.assertIn(self.group_member2, registration.group_members.all())
        # Amounts are tested separately with calculation logic

    def test_registration_creation_without_member(self):
        """Test creating a registration without linking to a Member (if allowed)."""
        # Model allows member to be null/blank
        no_member_data = self.registration_data.copy()
        del no_member_data['member']
        registration = EventRegistration.objects.create(**no_member_data)
        self.assertIsNotNone(registration.pk)
        self.assertIsNone(registration.member)
        self.assertEqual(registration.first_name, 'Registration')

    def test_registration_creation_without_event(self):
        """Test creating a registration without linking to an Event (if allowed)."""
        # Model allows event to be null/blank
        no_event_data = self.registration_data.copy()
        del no_event_data['event']
        registration = EventRegistration.objects.create(**no_event_data)
        self.assertIsNotNone(registration.pk)
        self.assertIsNone(registration.event)
        self.assertEqual(registration.first_name, 'Registration')

    # --- Field Validation and Constraints ---
    def test_registration_type_choices(self):
        """Test that registration_type only accepts valid choices."""
        registration = EventRegistration(**self.registration_data)
        registration.registration_type = 'INVALID_TYPE'
        with self.assertRaises(ValidationError) as cm:
            registration.full_clean()
        self.assertIn('registration_type', cm.exception.message_dict)

    def test_payment_status_choices(self):
        """Test that payment_status only accepts valid choices."""
        registration = EventRegistration(**self.registration_data)
        registration.payment_status = 'INVALID_STATUS'
        with self.assertRaises(ValidationError) as cm:
            registration.full_clean()
        self.assertIn('payment_status', cm.exception.message_dict)

    def test_positive_integer_fields(self):
        """Test number_of_participants and number_of_guests must be positive."""
        registration = EventRegistration(**self.registration_data)

        registration.number_of_participants = -1
        with self.assertRaises(ValidationError) as cm:
            registration.full_clean()
        self.assertIn('number_of_participants', cm.exception.message_dict)

        registration.number_of_participants = 1 # Reset
        registration.number_of_guests = -1
        with self.assertRaises(ValidationError) as cm:
            registration.full_clean()
        self.assertIn('number_of_guests', cm.exception.message_dict)

        # Test zero values (allowed)
        registration.number_of_participants = 0
        registration.number_of_guests = 0
        registration.full_clean() # Should not raise ValidationError
        self.assertEqual(registration.number_of_participants, 0)
        self.assertEqual(registration.number_of_guests, 0)

    def test_email_validation(self):
        """Test email field validation."""
        registration = EventRegistration(**self.registration_data)
        registration.email = 'invalid-email'
        with self.assertRaises(ValidationError) as cm:
            registration.full_clean()
        self.assertIn('email', cm.exception.message_dict)

        registration.email = '<EMAIL>' # Reset
        registration.full_clean() # Should not raise

    def test_invoice_number_validation_valid(self):
        """Test valid invoice number format."""
        registration = EventRegistration(**self.registration_data)
        registration.invoice_number = self.valid_invoice
        registration.full_clean() # Should not raise ValidationError
        registration.save()
        self.assertEqual(registration.invoice_number, self.valid_invoice)

    def test_invoice_number_validation_invalid(self):
        """Test invalid invoice number formats."""
        for invalid_invoice in self.invalid_invoices:
            with self.subTest(invoice=invalid_invoice):
                registration = EventRegistration(**self.registration_data)
                registration.invoice_number = invalid_invoice
                with self.assertRaises(ValidationError) as cm:
                    registration.full_clean()
                self.assertIn('invoice_number', cm.exception.message_dict)
                expected_error = 'Invoice number must be in format YYDDD-XXX where Y=year digit, D=day of year digit, X=sequence number digit'
                self.assertIn(expected_error, cm.exception.message_dict['invoice_number'])

    def test_invoice_number_nullable(self):
        """Test that invoice_number can be null or blank."""
        registration = EventRegistration(**self.registration_data)
        registration.invoice_number = None
        registration.full_clean() # Should not raise
        registration.save()
        self.assertIsNone(EventRegistration.objects.get(pk=registration.pk).invoice_number)

        registration.invoice_number = ''
        registration.full_clean() # Should not raise
        registration.save()
        # Note: Django typically saves blank CharFields as empty strings
        self.assertEqual(EventRegistration.objects.get(pk=registration.pk).invoice_number, '')

    def test_notes_nullable_and_blankable(self):
        """Test that notes field can be null or blank."""
        registration = EventRegistration(**self.registration_data)
        registration.notes = None
        registration.full_clean() # Should not raise
        registration.save()
        self.assertIsNone(EventRegistration.objects.get(pk=registration.pk).notes)

        registration.notes = ''
        registration.full_clean() # Should not raise
        registration.save()
        self.assertEqual(EventRegistration.objects.get(pk=registration.pk).notes, '')

    def test_decimal_field_precision(self):
        """Test precision and scale of decimal fields."""
        # Test max digits
        registration = EventRegistration(**self.registration_data)
        registration.total_amount = 1234567890.12 # Too many digits before decimal
        with self.assertRaises(ValidationError): # Django model validation should catch this
            registration.full_clean()

        # Test decimal places
        registration.total_amount = 123.123 # Too many decimal places
        with self.assertRaises(ValidationError):
            registration.full_clean()

        # Valid precision
        registration.total_amount = 12345678.90
        registration.full_clean() # Should not raise

    def test_extra_participants_jsonfield(self):
        """Test storing and retrieving data from the JSONField extra_participants."""
        registration = EventRegistration.objects.create(
            **self.registration_data,
            extra_participants=self.extra_participants_data
        )
        fetched_reg = EventRegistration.objects.get(pk=registration.pk)
        self.assertEqual(fetched_reg.extra_participants, self.extra_participants_data)
        self.assertEqual(len(fetched_reg.extra_participants['participants']), 2)
        self.assertEqual(fetched_reg.extra_participants['participants'][0]['email'], '<EMAIL>')

    def test_extra_participants_default_empty_dict(self):
        """Test that extra_participants defaults to an empty dict."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertEqual(registration.extra_participants, {})

    def test_group_registration_default(self):
        """Test that group_registration defaults to False."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertFalse(registration.group_registration)

    # --- Relationships ---
    def test_event_relationship(self):
        """Test the ForeignKey relationship to Event."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertEqual(registration.event, self.event)
        self.assertIn(registration, self.event.registrations.all())

    def test_member_relationship(self):
        """Test the ForeignKey relationship to Member."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertEqual(registration.member, self.user)
        self.assertIn(registration, self.user.event_registrations.all())

    def test_group_members_relationship(self):
        """Test the ManyToManyField relationship for group members."""
        registration = EventRegistration.objects.create(**self.group_registration_data)
        registration.group_members.add(self.group_member1, self.group_member2)

        self.assertEqual(registration.group_members.count(), 2)
        self.assertIn(self.group_member1, registration.group_members.all())
        self.assertIn(self.group_member2, registration.group_members.all())

        # Test reverse relationship
        self.assertIn(registration, self.group_member1.group_event_registrations.all())
        self.assertIn(registration, self.group_member2.group_event_registrations.all())

    def test_cascade_delete_event(self):
        """Test that registrations are deleted when the event is deleted."""
        registration = EventRegistration.objects.create(**self.registration_data)
        event_id = self.event.id
        registration_id = registration.id
        self.event.delete()
        with self.assertRaises(Event.DoesNotExist):
            Event.objects.get(id=event_id)
        with self.assertRaises(EventRegistration.DoesNotExist):
            EventRegistration.objects.get(id=registration_id)

    def test_cascade_delete_member(self):
        """Test that registrations are deleted when the member is deleted."""
        registration = EventRegistration.objects.create(**self.registration_data)
        user_id = self.user.id
        registration_id = registration.id
        self.user.delete()
        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=user_id)
        with self.assertRaises(EventRegistration.DoesNotExist):
            EventRegistration.objects.get(id=registration_id)

    def test_group_member_delete_does_not_cascade(self):
        """Test that deleting a group member doesn't delete the registration."""
        registration = EventRegistration.objects.create(**self.group_registration_data)
        registration.group_members.add(self.group_member1)
        group_member_id = self.group_member1.id
        registration_id = registration.id

        self.group_member1.delete()

        # Check member is gone
        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=group_member_id)

        # Check registration still exists and member removed from group
        registration = EventRegistration.objects.get(id=registration_id)
        self.assertIsNotNone(registration)
        self.assertEqual(registration.group_members.count(), 0)

    # --- Model Methods ---
    def test_str_method(self):
        """Test the __str__ method."""
        registration = EventRegistration.objects.create(**self.registration_data)
        expected_str = f"{registration.first_name} {registration.last_name} - {registration.registration_type}"
        self.assertEqual(str(registration), expected_str)

    def test_get_full_name(self):
        """Test the get_full_name method."""
        registration = EventRegistration.objects.create(**self.registration_data)
        expected_name = f"{self.registration_data['first_name']} {self.registration_data['last_name']}"
        self.assertEqual(registration.get_full_name(), expected_name)

    def test_get_full_address(self):
        """Test the get_full_address method."""
        registration = EventRegistration.objects.create(**self.registration_data)
        expected_address = f"{self.registration_data['address']}, {self.registration_data['city']}, {self.registration_data['state']} {self.registration_data['zipcode']}"
        self.assertEqual(registration.get_full_address(), expected_address)

    def test_can_be_modified(self):
        """Test the can_be_modified method based on payment_status."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertTrue(registration.can_be_modified()) # Default PENDING

        registration.payment_status = 'FAILED'
        registration.save()
        self.assertTrue(registration.can_be_modified())

        registration.payment_status = 'COMPLETED'
        registration.save()
        self.assertFalse(registration.can_be_modified())

    # --- Meta Class Options ---
    def test_ordering(self):
        """Test that registrations are ordered by registration_date descending."""
        reg1 = EventRegistration.objects.create(**self.registration_data)
        # Ensure a slight time difference for ordering
        # Note: This might be flaky in rapid test execution. Consider manually setting dates if needed.
        import time; time.sleep(0.01)
        reg_data2 = self.registration_data.copy()
        reg_data2['email'] = '<EMAIL>' # Avoid potential uniqueness issues if any
        reg_data2['member'] = self.group_member1 # Use a different member
        reg2 = EventRegistration.objects.create(**reg_data2)

        registrations = EventRegistration.objects.all()
        self.assertEqual(registrations[0], reg2)
        self.assertEqual(registrations[1], reg1)

    # --- calculate_total_amount Method Tests ---
    def test_calculate_total_amount_normal_registration(self):
        """Test amount calculation for normal registration with event rates."""
        registration = EventRegistration(
            event=self.event,
            member=self.user,
            number_of_participants=1,
            number_of_guests=0,
            **{k: v for k, v in self.registration_data.items() if k not in ['event', 'member', 'base_amount', 'guest_amount', 'total_amount']}
        )
        # Manually set event to be not late registration for this test case
        self.event.late_registration_date = timezone.now().date() + timedelta(days=1)
        self.event.save()

        calculated_total = registration.calculate_total_amount()

        expected_base = 1 * float(self.event.registration_fee_normal)
        expected_guest = 0 * float(self.event.guest_fee)
        expected_total = expected_base + expected_guest

        self.assertEqual(registration.base_amount, expected_base)
        self.assertEqual(registration.guest_amount, expected_guest)
        self.assertEqual(registration.total_amount, expected_total)
        self.assertEqual(calculated_total, expected_total)

    def test_calculate_total_amount_late_registration(self):
        """Test amount calculation for late registration with event rates."""
        registration = EventRegistration(
            event=self.event,
            member=self.user,
            number_of_participants=1,
            number_of_guests=0,
            **{k: v for k, v in self.registration_data.items() if k not in ['event', 'member', 'base_amount', 'guest_amount', 'total_amount']}
        )
        # Manually set event to be in late registration period
        self.event.late_registration_date = timezone.now().date() - timedelta(days=1)
        self.event.save()

        calculated_total = registration.calculate_total_amount()

        expected_base = 1 * float(self.event.registration_fee_late)
        expected_guest = 0 * float(self.event.guest_fee)
        expected_total = expected_base + expected_guest

        self.assertEqual(registration.base_amount, expected_base)
        self.assertEqual(registration.guest_amount, expected_guest)
        self.assertEqual(registration.total_amount, expected_total)
        self.assertEqual(calculated_total, expected_total)

    def test_calculate_total_amount_with_guests(self):
        """Test amount calculation with participants and guests."""
        registration = EventRegistration(
            event=self.event,
            member=self.user,
            number_of_participants=2,
            number_of_guests=3,
             **{k: v for k, v in self.registration_data.items() if k not in ['event', 'member', 'base_amount', 'guest_amount', 'total_amount']}
       )
        # Assume normal registration period
        self.event.late_registration_date = timezone.now().date() + timedelta(days=1)
        self.event.save()

        calculated_total = registration.calculate_total_amount()

        expected_base = 2 * float(self.event.registration_fee_normal)
        expected_guest = 3 * float(self.event.guest_fee)
        expected_total = expected_base + expected_guest

        self.assertEqual(registration.base_amount, expected_base)
        self.assertEqual(registration.guest_amount, expected_guest)
        self.assertEqual(registration.total_amount, expected_total)
        self.assertEqual(calculated_total, expected_total)

    def test_calculate_total_amount_no_event_fallback(self):
        """Test amount calculation fallback when no event is linked."""
        # This scenario relies on the hardcoded fallback values in the model method
        # Ideally, these rates should come from settings or a configuration model
        registration = EventRegistration(
            event=None, # No event linked
            member=self.user,
            number_of_participants=1,
            number_of_guests=1,
             **{k: v for k, v in self.registration_data.items() if k not in ['event', 'member', 'base_amount', 'guest_amount', 'total_amount']}
        )

        # Determine expected fallback rates based on current date vs hardcoded date
        fallback_late_date = timezone.datetime(2024, 12, 15, tzinfo=timezone.get_current_timezone()) # Make it timezone-aware
        if timezone.now() > fallback_late_date:
             expected_base_rate = 115
        else:
             expected_base_rate = 100
        expected_guest_rate = 25

        calculated_total = registration.calculate_total_amount()

        expected_base = 1 * expected_base_rate
        expected_guest = 1 * expected_guest_rate
        expected_total = expected_base + expected_guest

        self.assertEqual(registration.base_amount, expected_base)
        self.assertEqual(registration.guest_amount, expected_guest)
        self.assertEqual(registration.total_amount, expected_total)
        self.assertEqual(calculated_total, expected_total)

    def test_calculate_total_amount_zero_participants_guests(self):
        """Test calculation when participants and guests are zero."""
        registration = EventRegistration(
            event=self.event,
            member=self.user,
            number_of_participants=0,
            number_of_guests=0,
             **{k: v for k, v in self.registration_data.items() if k not in ['event', 'member', 'base_amount', 'guest_amount', 'total_amount']}
        )
        calculated_total = registration.calculate_total_amount()
        self.assertEqual(registration.base_amount, 0)
        self.assertEqual(registration.guest_amount, 0)
        self.assertEqual(registration.total_amount, 0)
        self.assertEqual(calculated_total, 0)

    # --- Save Method Override ---
    def test_save_ensures_amounts_not_null(self):
        """Test that save method sets null amounts to 0."""
        registration = EventRegistration(
            event=self.event,
            member=self.user,
            first_name='Null Amount Test',
            last_name='User',
            title='Mr.',
            fire_department='Test FD',
            address='1 Null St',
            city='Null City',
            state='MS',
            zipcode='00000',
            phone='555-NULL',
            email='<EMAIL>',
            registration_type='NORMAL',
            base_amount=None,  # Explicitly set to None
            guest_amount=None, # Explicitly set to None
            total_amount=None  # Explicitly set to None
        )
        registration.save()

        # Re-fetch from DB
        saved_reg = EventRegistration.objects.get(pk=registration.pk)

        self.assertEqual(saved_reg.base_amount, 0)
        self.assertEqual(saved_reg.guest_amount, 0)
        # Total amount should also be calculated based on the now zeroed base/guest amounts
        self.assertEqual(saved_reg.total_amount, 0)

    def test_save_calculates_total_if_null(self):
        """Test that save recalculates total_amount if it's None but base/guest are not."""
        registration = EventRegistration(
            event=self.event,
            member=self.user,
            first_name='Calc Total Test',
            last_name='User',
            title='Mx',
            fire_department='Test FD',
            address='1 Calc St',
            city='Calc City',
            state='MS',
            zipcode='11111',
            phone='555-CALC',
            email='<EMAIL>',
            registration_type='NORMAL',
            base_amount=50.50,
            guest_amount=10.25,
            total_amount=None # Explicitly set to None
        )
        registration.save()

        saved_reg = EventRegistration.objects.get(pk=registration.pk)
        expected_total = 50.50 + 10.25
        self.assertEqual(saved_reg.total_amount, expected_total)

    def test_save_preserves_existing_amounts(self):
        """Test save doesn't overwrite existing valid amounts unless they are None."""
        registration = EventRegistration.objects.create(**self.registration_data)
        original_base = registration.base_amount
        original_guest = registration.guest_amount
        original_total = registration.total_amount

        registration.notes = 'Updated notes'
        registration.save()

        saved_reg = EventRegistration.objects.get(pk=registration.pk)
        self.assertEqual(saved_reg.base_amount, original_base)
        self.assertEqual(saved_reg.guest_amount, original_guest)
        self.assertEqual(saved_reg.total_amount, original_total)

    # --- Historical Records (django-simple-history) ---
    def test_historical_record_created_on_create(self):
        """Test that a historical record is created upon registration creation."""
        registration = EventRegistration.objects.create(**self.registration_data)
        self.assertEqual(registration.history.count(), 1)
        initial_record = registration.history.latest('history_date')
        self.assertEqual(initial_record.history_type, '+') # '+' indicates creation
        self.assertEqual(initial_record.pk, registration.pk)

    def test_historical_record_created_on_update(self):
        """Test that a historical record is created when a registration is updated."""
        registration = EventRegistration.objects.create(**self.registration_data)
        initial_count = registration.history.count()

        registration.payment_status = 'COMPLETED'
        registration.notes = 'Payment received.'
        registration.save()

        self.assertEqual(registration.history.count(), initial_count + 1)
        update_record = registration.history.latest('history_date')
        self.assertEqual(update_record.history_type, '~') # '~' indicates update
        self.assertEqual(update_record.payment_status, 'COMPLETED')
        self.assertEqual(update_record.notes, 'Payment received.')

        # Check previous record state
        previous_record = registration.history.earliest('history_date')
        self.assertEqual(previous_record.payment_status, 'PENDING')
        self.assertNotEqual(previous_record.notes, 'Payment received.')

    def test_historical_record_created_on_delete(self):
        """Test that a historical record is created when a registration is deleted."""
        registration = EventRegistration.objects.create(**self.registration_data)
        registration_pk = registration.pk
        
        # Get the count before deletion - useful for comparison if needed later
        # history_count = registration.history.count()
        
        # Delete the registration
        registration.delete()

        # Query historical model directly AFTER deletion
        HistoricalModel = EventRegistration.history.model
        delete_records = HistoricalModel.objects.filter(
            pk=registration_pk, 
            history_type='-'
        )
        
        # There should be exactly one deletion record
        self.assertEqual(delete_records.count(), 1)
        delete_record = delete_records.first()

        self.assertIsNotNone(delete_record)
        self.assertEqual(delete_record.history_type, '-') # '-' indicates deletion
        self.assertEqual(delete_record.pk, registration_pk)

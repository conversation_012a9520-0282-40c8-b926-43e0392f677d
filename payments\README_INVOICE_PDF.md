# PDF Invoice Generation for MFA Backend

This module provides functionality to generate pixel-perfect PDF invoices for both membership payments and event registrations in the Mississippi Fire Chiefs Association Management System.

## Features

- Generates professional-looking invoices that match the MFA branding and format
- Supports both membership payments and event registration payments
- Handles bulk payments (one member paying for multiple members)
- Provides three different rendering methods for optimal quality:
  - ReportLab (default): For pixel-perfect precision
  - WeasyPrint: For high-quality HTML to PDF conversion
  - xhtml2pdf: For simple HTML template-based rendering

## API Endpoints

### Single Invoice PDF Generation

```
GET /api/payments/invoice/{payment_id}/pdf/
```

Generates a PDF invoice for a specific payment.

**URL Parameters:**
- `payment_id`: ID of the payment to generate an invoice for

**Query Parameters:**
- `render_method`: (Optional) The rendering method to use. Options:
  - `reportlab` (default): Uses ReportLab for pixel-perfect rendering
  - `weasyprint`: Uses WeasyPrint for high-quality HTML to PDF conversion
  - `xhtml2pdf`: Uses xhtml2pdf for simple HTML template-based rendering

**Response:**
- PDF file with appropriate Content-Type and Content-Disposition headers

### Bulk Invoice PDF Generation

```
POST /api/payments/invoice/bulk-pdf/
```

Generates PDF invoices for multiple payments.

**Request Body:**
```json
{
  "payment_ids": [1, 2, 3]
}
```

**Response:**
- PDF file with appropriate Content-Type and Content-Disposition headers

## Implementation Details

### PDF Generation Methods

#### ReportLab

The default and recommended method for pixel-perfect PDF generation. ReportLab provides precise control over layout, fonts, colors, and positioning, ensuring the invoices look exactly as designed.

#### WeasyPrint

A high-quality HTML to PDF converter that provides good rendering of HTML templates with CSS. This method is useful when you want to leverage HTML/CSS for layout but need better quality than xhtml2pdf.

#### xhtml2pdf

A simple HTML to PDF converter that works directly with Django templates. This method is the easiest to customize through HTML templates but may have some limitations in rendering complex layouts.

### Invoice Content

The invoice includes:

1. **Header**: MFA branding and "INVOICE" label
2. **Organization Address**: MFA address information
3. **Billing Information**: Who the invoice is billed to
4. **Invoice Details**: Invoice number, date, status, and due date
5. **Line Items**:
   - For membership payments: Each covered member with their membership type
   - For event registrations: Registration details and guest information if applicable
6. **Period Coverage**: The membership period or event date
7. **Payment Amount**: Total amount due
8. **Payment Instructions**: Where to send payment and contact information

### Templates

The HTML template for the invoice is located at:
```
common/templates/pdf/invoice.html
```

### Utility Functions

PDF generation utilities are available in:
```
common/utils/pdf_generator.py
```

## Usage Examples

### Generate a Membership Invoice

```python
# In a view or API endpoint
from django.http import HttpResponse
from common.utils.pdf_generator import generate_invoice_pdf_with_reportlab
from payments.models import Payment

def generate_membership_invoice(request, payment_id):
    payment = Payment.objects.get(id=payment_id)
    items = []
    
    # Generate items for each covered member
    for member in payment.covered_members.all():
        items.append({
            'quantity': 1,
            'description': f"Membership - {member.name}",
            'unit_price': payment.amount / payment.covered_members.count(),
            'amount': payment.amount / payment.covered_members.count()
        })
    
    return generate_invoice_pdf_with_reportlab(payment, items)
```

### Generate an Event Registration Invoice

```python
# In a view or API endpoint
from django.http import HttpResponse
from common.utils.pdf_generator import generate_invoice_pdf_with_reportlab
from payments.models import Payment

def generate_event_invoice(request, payment_id):
    payment = Payment.objects.get(id=payment_id)
    event_reg = payment.event_registration
    items = []
    
    # Add main registration
    items.append({
        'quantity': event_reg.number_of_participants,
        'description': f"Event Registration - {event_reg.event.name}",
        'unit_price': event_reg.base_amount / event_reg.number_of_participants,
        'amount': event_reg.base_amount
    })
    
    # Add guests if any
    if event_reg.number_of_guests > 0:
        items.append({
            'quantity': event_reg.number_of_guests,
            'description': f"Guest Registration - {event_reg.event.name}",
            'unit_price': event_reg.guest_amount / event_reg.number_of_guests,
            'amount': event_reg.guest_amount
        })
    
    return generate_invoice_pdf_with_reportlab(payment, items)
```

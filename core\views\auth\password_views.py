from django.contrib.auth import get_user_model
from rest_framework import status, permissions
from common.views import BaseAPIView, APIResponse
from common.utils import track_activity
from ...serializers.auth import (
    ChangePasswordSerializer,
    AdminChangePasswordSerializer
)
from django.db.models.deletion import transaction

Member = get_user_model()


class ChangePasswordView(BaseAPIView):
    """
    View for changing user password
    """
    permission_classes = [permissions.IsAuthenticated]

    @transaction.atomic
    @track_activity(description="User changed their password")
    def post(self, request, *args, **kwargs):
        """
        Handle POST request to change a user's password
        """
        serializer = ChangePasswordSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        # Change password
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return APIResponse(
            data=None,
            message="Password changed successfully",
            status_code=status.HTTP_200_OK
        )


class AdminChangePasswordView(BaseAPIView):
    """
    View for admins to change other users' passwords
    """
    permission_classes = [permissions.IsAdminUser]

    @transaction.atomic
    @track_activity(description="Admin changed a user's password")
    def post(self, request):
        """
        Handle POST request to change another user's password (admin only)
        """
        serializer = AdminChangePasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Get user and change password
        user_id = serializer.validated_data['user_id']
        user = Member.objects.get(id=user_id)
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return APIResponse(
            data=None,
            message=f"Password for user {user.email} changed successfully",
            status_code=status.HTTP_200_OK
        ) 
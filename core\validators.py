"""
Custom validators for the core app.
"""
import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


class ComplexityPasswordValidator:
    """
    Validate that the password contains a mix of character types.
    
    This validator requires that passwords contain at least one character from
    at least 3 of the following categories:
    - Uppercase letters
    - Lowercase letters
    - Numbers
    - Special characters
    """
    
    def __init__(self, min_categories=3):
        self.min_categories = min_categories
    
    def validate(self, password, user=None):
        """
        Validate that the password contains characters from multiple categories.
        """
        categories = 0
        
        # Check for uppercase letters
        if re.search(r'[A-Z]', password):
            categories += 1
            
        # Check for lowercase letters
        if re.search(r'[a-z]', password):
            categories += 1
            
        # Check for numbers
        if re.search(r'[0-9]', password):
            categories += 1
            
        # Check for special characters
        if re.search(r'[^A-Za-z0-9]', password):
            categories += 1
            
        if categories < self.min_categories:
            raise ValidationError(
                _(f"Password must contain characters from at least {self.min_categories} of the following "
                  f"categories: uppercase letters, lowercase letters, numbers, and special characters."),
                code='password_too_simple',
            )
    
    def get_help_text(self):
        """
        Return help text for this validator.
        """
        return _(
            f"Your password must contain characters from at least {self.min_categories} of the following "
            f"categories: uppercase letters, lowercase letters, numbers, and special characters."
        )

"""
Tests for the PDF templates in the common app.

This module contains tests for all PDF templates,
verifying that they render correctly with various contexts.
"""
from datetime import datetime, timedelta
from decimal import Decimal
from django.test import TestCase
from django.template.loader import render_to_string
from django.conf import settings


# Create a simple class to represent an invoice for testing
class TestInvoice:
    def __init__(self, invoice_number, date, due_date, amount, status, period_start, period_end, payer_name):
        self.invoice_number = invoice_number
        self.date = date
        self.due_date = due_date
        self.amount = amount
        self._status = status
        self.period_start = period_start
        self.period_end = period_end
        self.payer = self.Payer(payer_name)

    def get_status_display(self):
        return self._status

    class Payer:
        def __init__(self, name):
            self.name = name


class InvoiceTemplateTests(TestCase):
    """Test cases for the invoice.html template."""

    def setUp(self):
        """Set up test data for each test case."""
        # Use real date objects for all date fields
        current_date = datetime.now().date()
        due_date = current_date + timedelta(days=30)
        period_end = current_date + timedelta(days=365)

        # Create a test invoice with real date objects
        self.invoice = TestInvoice(
            invoice_number="INV-001",
            date=current_date,
            due_date=due_date,
            amount=Decimal('100.00'),
            status="UNPAID",
            period_start=current_date,
            period_end=period_end,
            payer_name="Test Department"
        )

        # Create invoice items
        self.items = [
            {
                'quantity': 1,
                'description': 'Annual Membership',
                'unit_price': Decimal('100.00'),
                'amount': Decimal('100.00')
            }
        ]

    def test_invoice_template_renders_correctly(self):
        """Test that the invoice.html template renders correctly with all required variables."""
        context = {
            'invoice': self.invoice,
            'items': self.items
        }

        # Render the template
        rendered = render_to_string('pdf/invoice.html', context)

        # Check that all context variables appear in the rendered template
        self.assertIn('Mississippi Firefighters Association', rendered)
        self.assertIn('INVOICE', rendered)
        self.assertIn(self.invoice.payer.name, rendered)
        self.assertIn(self.invoice.invoice_number, rendered)
        self.assertIn('UNPAID', rendered)  # Status

        # Check for invoice item details
        self.assertIn('Annual Membership', rendered)
        self.assertIn('$100.00', rendered)

        # Check for totals
        self.assertIn('SUBTOTAL', rendered)
        self.assertIn('TOTAL', rendered)
        self.assertIn('PAY THIS', rendered)

        # Check for payment instructions
        self.assertIn('DIRECT ALL INQUIRES TO', rendered)
        self.assertIn('MAKE ALL CHECKS PAYABLE TO', rendered)
        self.assertIn('Mississippi Firefighters Association', rendered)

    def test_invoice_template_with_multiple_items(self):
        """Test that the invoice.html template renders correctly with multiple items."""
        # Add multiple items
        items = [
            {
                'quantity': 1,
                'description': 'Annual Membership',
                'unit_price': Decimal('75.00'),
                'amount': Decimal('75.00')
            },
            {
                'quantity': 2,
                'description': 'Conference Tickets',
                'unit_price': Decimal('50.00'),
                'amount': Decimal('100.00')
            },
            {
                'quantity': 1,
                'description': 'Late Fee',
                'unit_price': Decimal('25.00'),
                'amount': Decimal('25.00')
            }
        ]

        # Create a new invoice with updated amount
        current_date = datetime.now().date()
        invoice = TestInvoice(
            invoice_number="INV-002",
            date=current_date,
            due_date=current_date + timedelta(days=30),
            amount=Decimal('200.00'),
            status="UNPAID",
            period_start=current_date,
            period_end=current_date + timedelta(days=365),
            payer_name="Test Department"
        )

        context = {
            'invoice': invoice,
            'items': items
        }

        # Render the template
        rendered = render_to_string('pdf/invoice.html', context)

        # Check that all items appear in the rendered template
        self.assertIn('Annual Membership', rendered)
        self.assertIn('Conference Tickets', rendered)
        self.assertIn('Late Fee', rendered)

        # Check that quantities appear
        self.assertIn('<td>1</td>', rendered)
        self.assertIn('<td>2</td>', rendered)

        # Check that unit prices appear
        self.assertIn('$75.00', rendered)
        self.assertIn('$50.00', rendered)
        self.assertIn('$25.00', rendered)

        # Check that total appears
        self.assertIn('$200.00', rendered)

    def test_invoice_template_with_large_amounts(self):
        """Test that the invoice.html template correctly formats large monetary amounts."""
        # Create a new invoice with a large amount
        current_date = datetime.now().date()
        invoice = TestInvoice(
            invoice_number="INV-003",
            date=current_date,
            due_date=current_date + timedelta(days=30),
            amount=Decimal('12345678.90'),
            status="UNPAID",
            period_start=current_date,
            period_end=current_date + timedelta(days=365),
            payer_name="Test Department"
        )

        # Create an item with a large amount
        items = [
            {
                'quantity': 1,
                'description': 'Enterprise Package',
                'unit_price': Decimal('12345678.90'),
                'amount': Decimal('12345678.90')
            }
        ]

        context = {
            'invoice': invoice,
            'items': items
        }

        # Render the template
        rendered = render_to_string('pdf/invoice.html', context)

        # Check that large amounts are formatted with commas
        self.assertIn('$12,345,678.90', rendered)

    def test_invoice_template_handles_edge_cases(self):
        """Test that the invoice.html template handles edge cases like special characters."""
        # Create a new invoice with special characters in payer name
        current_date = datetime.now().date()
        invoice = TestInvoice(
            invoice_number="INV-004",
            date=current_date,
            due_date=current_date + timedelta(days=30),
            amount=Decimal('100.00'),
            status="UNPAID",
            period_start=current_date,
            period_end=current_date + timedelta(days=365),
            payer_name="Department with <script>alert('XSS')</script>"
        )

        # Create an item with special characters
        items = [
            {
                'quantity': 1,
                'description': 'Item with <script>alert("XSS")</script>',
                'unit_price': Decimal('100.00'),
                'amount': Decimal('100.00')
            }
        ]

        context = {
            'invoice': invoice,
            'items': items
        }

        # Render the template
        rendered = render_to_string('pdf/invoice.html', context)

        # Check that HTML is properly escaped (no XSS vulnerabilities)
        self.assertIn('Department with &lt;script&gt;alert(&#x27;XSS&#x27;)&lt;/script&gt;', rendered)
        self.assertIn('Item with &lt;script&gt;alert(&quot;XSS&quot;)&lt;/script&gt;', rendered)
"""
Token-related views for authentication.
"""
from rest_framework import status
from rest_framework_simplejwt.views import TokenRefreshView as SimpleJWTTokenRefreshView
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError

from common.views import APIResponse


class CustomTokenRefreshView(SimpleJWTTokenRefreshView):
    """
    Custom token refresh view that uses our standard API response format
    """
    def post(self, request, *args, **kwargs):
        """
        Handle POST request to refresh a token
        """
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
            
            return APIResponse(
                data=serializer.validated_data,
                message="Token refreshed successfully",
                status_code=status.HTTP_200_OK
            )
        except TokenError as e:
            return APIResponse(
                message=str(e),
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        except InvalidToken as e:
            return APIResponse(
                message=str(e),
                status_code=status.HTTP_401_UNAUTHORIZED
            ) 
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIRequestFactory

from common.permissions.permissions import IsStaffUser

User = get_user_model()


class IsStaffUserTest(TestCase):
    def setUp(self):
        self.factory = APIRequestFactory()
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Staff User',
            is_staff=True
        )
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Regular User',
            is_staff=False
        )
        self.permission = IsStaffUser()
        
    def test_has_permission_staff_user(self):
        """Test that staff users have permission"""
        request = self.factory.get('/')
        request.user = self.staff_user
        
        has_permission = self.permission.has_permission(request, None)
        
        self.assertTrue(has_permission)
        
    def test_has_permission_regular_user(self):
        """Test that regular users don't have permission"""
        request = self.factory.get('/')
        request.user = self.regular_user
        
        has_permission = self.permission.has_permission(request, None)
        
        self.assertFalse(has_permission)
        
    def test_has_permission_anonymous_user(self):
        """Test that anonymous users don't have permission"""
        request = self.factory.get('/')
        request.user = None
        
        has_permission = self.permission.has_permission(request, None)
        
        self.assertFalse(has_permission) 
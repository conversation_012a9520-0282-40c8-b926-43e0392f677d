"""
Test view for downloading the first available invoice PDF
"""
from datetime import datetime
from django.http import Http404
from rest_framework import status
from rest_framework.views import APIView

from common.views import BaseAPIView, APIResponse
from common.utils.pdf_generator import generate_invoice_pdf
from payments.models import Payment


class TestInvoicePDFView(BaseAPIView):
    """Test view for downloading the first available invoice PDF"""
    permission_classes = []
    
    def get(self, request, *args, **kwargs):
        """Download the first available invoice PDF"""
        # Get the first available payment
        payment = Payment.objects.select_related('payer', 'event_registration').first()
        
        if not payment:
            return APIResponse(
                message="No payments found in the system",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        # Generate invoice items based on payment type
        invoice_items = self._generate_invoice_items(payment)
        
        # Generate PDF using WeasyPrint
        context = {
            'invoice': payment,
            'items': invoice_items,
            'period_start': datetime(payment.paid_year, 1, 1),
            'period_end': datetime(payment.paid_year, 12, 31),
        }
        filename = f"test_invoice_{payment.invoice_number}.pdf"
        return generate_invoice_pdf('pdf/invoice.html', context, filename)
    
    def _generate_invoice_items(self, payment):
        """Generate invoice items based on payment type"""
        items = []
        
        if payment.payment_for == Payment.PaymentFor.MEMBERSHIP:
            # Handle membership payment
            covered_members = payment.covered_members.all()
            
            # Add each covered member as an item
            for member in covered_members:
                items.append({
                    'quantity': 1,
                    'description': f"Membership - {member.name} ({member.get_membership_class_display()})",
                    'unit_price': payment.amount / len(covered_members) if len(covered_members) > 0 else payment.amount,
                    'amount': payment.amount / len(covered_members) if len(covered_members) > 0 else payment.amount
                })
        
        elif payment.payment_for == Payment.PaymentFor.EVENT:
            # Handle event registration payment
            event_reg = payment.event_registration
            
            if event_reg:
                # Add main registration
                items.append({
                    'quantity': event_reg.number_of_participants,
                    'description': f"Event Registration - {event_reg.event.name if event_reg.event else 'Event'} ({event_reg.get_registration_type_display()})",
                    'unit_price': event_reg.base_amount / event_reg.number_of_participants if event_reg.number_of_participants > 0 else event_reg.base_amount,
                    'amount': event_reg.base_amount
                })
                
                # Add guests if any
                if event_reg.number_of_guests > 0:
                    items.append({
                        'quantity': event_reg.number_of_guests,
                        'description': f"Guest Registration - {event_reg.event.name if event_reg.event else 'Event'}",
                        'unit_price': event_reg.guest_amount / event_reg.number_of_guests if event_reg.number_of_guests > 0 else event_reg.guest_amount,
                        'amount': event_reg.guest_amount
                    })
        
        # If no items were generated, add a default item
        if not items:
            items.append({
                'quantity': 1,
                'description': f"Payment - {payment.get_payment_for_display()}",
                'unit_price': payment.amount,
                'amount': payment.amount
            })
        
        return items

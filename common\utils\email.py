import uuid
from django.conf import settings
from django.core.mail import send_mail
from django.core.exceptions import ValidationError
from django.template.loader import render_to_string
from django.utils import timezone

from common.models import EmailVerification, PasswordReset


def send_verification_email(user):
    """
    Create a verification record and send verification email to the user

    Args:
        user: User object to send verification email to

    Returns:
        EmailVerification object
    """
    # Check if user already has a non-expired verification
    existing_verification = EmailVerification.objects.filter(
        user=user,
        verified=False,
        expires_at__gt=timezone.now()
    ).first()

    if existing_verification:
        verification = existing_verification
    else:
        # Create a new verification record
        verification = EmailVerification.objects.create(user=user)

    # Build verification URL
    frontend_url = settings.FRONTEND_URL or ""
    verification_url = f"{frontend_url}/verify-email/{verification.key}"

    # Email content
    subject = "Verify your email address"
    html_message = render_to_string('emails/verify_email.html', {
        'name': user.name,
        'verification_url': verification_url,
        'expiry_days': 30,
    })

    # Plain text message as fallback
    plain_message = (
        f"Hello {user.name},\n\n"
        f"Please verify your email address by clicking the link below:\n\n"
        f"{verification_url}\n\n"
        f"This link will expire in 30 days.\n\n"
        f"If you did not create an account, please ignore this email.\n\n"
        f"Regards,\nMFA Team"
    )

    # Send email
    send_mail(
        subject,
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )

    return verification


def verify_email(key):
    """
    Verify a user's email using the verification key

    Args:
        key: Verification key

    Returns:
        (user, error_message): Tuple of user object and error message if any
    """
    try:
        # Convert key to string if it's a UUID
        if key and not isinstance(key, str):
            key = str(key)

        # Handle empty or invalid UUID
        if not key:
            return None, "Invalid verification link"

        # Validate UUID format before querying the database
        try:
            # Try to convert the string to a UUID to validate its format
            uuid_key = uuid.UUID(key)
        except (ValueError, TypeError, AttributeError):
            return None, "Invalid verification link"

        verification = EmailVerification.objects.get(key=uuid_key)

        # Check if already used
        if verification.verified:
            return None, "Email has already been verified"

        # Check if expired
        if verification.is_expired:
            return None, "Verification link has expired"

        # Mark as verified
        verification.verified = True
        verification.save()

        # Do NOT set user.active = True here
        # Just mark email as verified, admin will activate later
        user = verification.user

        return user, None

    except (EmailVerification.DoesNotExist, ValueError, TypeError, ValidationError):
        return None, "Invalid verification link"


def send_password_reset_email(user):
    """
    Create a password reset record and send reset email to the user

    Args:
        user: User object to send password reset email to

    Returns:
        PasswordReset object
    """
    # Invalidate any existing non-expired reset tokens
    PasswordReset.objects.filter(
        user=user,
        used=False,
        expires_at__gt=timezone.now()
    ).update(used=True)

    # Create a new reset record
    reset_record = PasswordReset.objects.create(user=user)

    # Build reset URL
    frontend_url = settings.FRONTEND_URL or ""
    reset_url = f"{frontend_url}/reset-password/{reset_record.key}"

    # Email content
    subject = "Reset your password"
    html_message = render_to_string('emails/reset_password.html', {
        'name': user.name,
        'reset_url': reset_url,
        'expiry_hours': 24,
    })

    # Plain text message as fallback
    plain_message = (
        f"Hello {user.name},\n\n"
        f"We received a request to reset your password. If you didn't make this request, you can ignore this email.\n\n"
        f"To reset your password, click the link below:\n\n"
        f"{reset_url}\n\n"
        f"This link will expire in 24 hours.\n\n"
        f"Regards,\nMFA Team"
    )

    # Send email
    send_mail(
        subject,
        plain_message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )

    return reset_record


def verify_password_reset_token(key):
    """
    Verify a password reset token

    Args:
        key: Reset token key

    Returns:
        (user, error_message): Tuple of user object and error message if any
    """
    try:
        # Convert key to string if it's a UUID
        if key and not isinstance(key, str):
            key = str(key)

        # Handle empty or invalid UUID
        if not key:
            return None, "Invalid password reset link"

        # Validate UUID format before querying the database
        try:
            # Try to convert the string to a UUID to validate its format
            uuid_key = uuid.UUID(key)
        except (ValueError, TypeError, AttributeError):
            return None, "Invalid password reset link"

        reset_record = PasswordReset.objects.get(key=uuid_key)

        # Check if already used
        if reset_record.used:
            return None, "This password reset link has already been used"

        # Check if expired
        if reset_record.is_expired:
            return None, "Password reset link has expired"

        # Return the user for password reset
        user = reset_record.user

        return user, None

    except (PasswordReset.DoesNotExist, ValueError, TypeError, ValidationError):
        return None, "Invalid password reset link"
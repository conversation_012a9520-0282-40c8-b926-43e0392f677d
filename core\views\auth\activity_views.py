"""
Views for user activity tracking.
"""

from rest_framework import permissions, generics
from django_filters import rest_framework as filters
from common.views import BaseAPIView
from common.models import UserActivity
from common.pagination import StandardPagination
from core.serializers.auth import UserActivitySerializer
from common.utils import track_activity
from common.filters.activity_filters import UserActivityFilter

class UserActivityListView(BaseAPIView):
    """
    View for retrieving user activities with filtering support
    """
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = UserActivityFilter
    
    
    def get(self, request):
        """
        Get a paginated and filtered list of user activities
        Supports filtering by user, description, view_name, and time range
        """
        # Get activities for the current user only
        if not request.user.is_staff:
            activities = UserActivity.objects.filter(user=request.user)
        else:
            # Staff can see all activities
            activities = UserActivity.objects.all()
            
            # Filter by user if specified
            user_id = request.query_params.get('user')
            if user_id:
                activities = activities.filter(user_id=user_id)
        
        # Apply filtering
        filtered_activities = self.filterset_class(request.query_params, queryset=activities)
        
        # Apply pagination
        paginator = self.pagination_class()
        paginated_activities = paginator.paginate_queryset(filtered_activities.qs, request)
        
        # Serialize data
        serializer = UserActivitySerializer(paginated_activities, many=True)
        
        # Return paginated response
        return paginator.get_paginated_response(serializer.data) 
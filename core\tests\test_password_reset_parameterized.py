"""
Parameterized tests for the password reset functionality.

This module uses parameterized testing to efficiently test many different
combinations of inputs and scenarios for the password reset process.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from parameterized import parameterized

from common.models import PasswordReset
from common.utils.email import verify_password_reset_token
from core.serializers.auth.password_reset import (
    RequestPasswordResetSerializer,
    ValidateResetTokenSerializer,
    PasswordResetConfirmSerializer
)

User = get_user_model()


class ParameterizedRequestPasswordResetTests(TestCase):
    """Parameterized test cases for the request password reset functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:request-password-reset')

        # Create users
        self.active_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Active User',
            active=True
        )

        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Inactive User',
            active=False
        )

    @parameterized.expand([
        # Test case format:
        # (name, email, expected_status, expected_email_sent)

        # Valid cases
        ('active_user', '<EMAIL>', status.HTTP_200_OK, True),
        ('inactive_user', '<EMAIL>', status.HTTP_200_OK, False),
        ('nonexistent_user', '<EMAIL>', status.HTTP_200_OK, False),
        ('uppercase_email', '<EMAIL>', status.HTTP_200_OK, True),  # Case insensitive
        ('mixed_case_email', '<EMAIL>', status.HTTP_200_OK, True),  # Case insensitive
        ('email_with_whitespace', ' <EMAIL> ', status.HTTP_200_OK, True),  # Trimmed

        # Invalid cases
        ('empty_email', '', status.HTTP_400_BAD_REQUEST, False),
        ('invalid_email_format', 'not-an-email', status.HTTP_400_BAD_REQUEST, False),
        ('missing_email', None, status.HTTP_400_BAD_REQUEST, False),
    ])
    @patch('core.serializers.auth.password_reset.send_password_reset_email')
    def test_request_password_reset(self, name, email, expected_status, expected_email_sent, mock_send_email):
        """Test requesting password reset with various email inputs."""
        data = {}
        if email is not None:
            data['email'] = email

        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, expected_status)

        if expected_email_sent:
            mock_send_email.assert_called_once()
        else:
            mock_send_email.assert_not_called()

        mock_send_email.reset_mock()


class ParameterizedValidateResetTokenTests(TestCase):
    """Parameterized test cases for the validate reset token functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:validate-reset-token')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test User',
            active=True
        )

        # Create tokens with different statuses
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        self.expired_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(minutes=1)
        )

        self.used_reset = PasswordReset.objects.create(
            user=self.user,
            used=True,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # Generate a non-existent token
        self.nonexistent_token = uuid.uuid4()
        while PasswordReset.objects.filter(key=self.nonexistent_token).exists():
            self.nonexistent_token = uuid.uuid4()

    @parameterized.expand([
        # Test case format:
        # (name, token, expected_status, expected_error_field)

        # These tests will be mocked to control the verify_password_reset_token response
        ('valid_token', 'valid', status.HTTP_200_OK, None),
        ('expired_token', 'expired', status.HTTP_400_BAD_REQUEST, 'token'),
        ('used_token', 'used', status.HTTP_400_BAD_REQUEST, 'token'),
        ('nonexistent_token', 'nonexistent', status.HTTP_400_BAD_REQUEST, 'token'),

        # These test actual validation errors
        ('empty_token', '', status.HTTP_400_BAD_REQUEST, 'token'),
        ('invalid_format', 'not-a-uuid', status.HTTP_400_BAD_REQUEST, 'token'),
        ('none_token', None, status.HTTP_400_BAD_REQUEST, 'token'),
    ])
    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_validate_reset_token(self, name, token, expected_status, expected_error_field, mock_verify):
        """Test validating reset tokens with various inputs."""
        # Set up mock for token verification
        if token == 'valid':
            mock_verify.return_value = (self.user, None)
            token = self.valid_reset.key
        elif token == 'expired':
            mock_verify.return_value = (None, 'Password reset link has expired')
            token = self.expired_reset.key
        elif token == 'used':
            mock_verify.return_value = (None, 'This password reset link has already been used')
            token = self.used_reset.key
        elif token == 'nonexistent':
            mock_verify.return_value = (None, 'Invalid password reset link')
            token = self.nonexistent_token

        # For actual validation errors, we'll disable the mock for those specific tests
        if token in ('', 'not-a-uuid', None):
            mock_verify.side_effect = Exception("This shouldn't be called")

        data = {}
        if token is not None:
            data['token'] = token

        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, expected_status)

        if expected_error_field:
            self.assertIn(expected_error_field, response.data['data'])
        elif expected_status == status.HTTP_200_OK:
            self.assertEqual(response.data['data']['email'], '<EMAIL>')


class ParameterizedPasswordResetConfirmTests(TestCase):
    """Parameterized test cases for the password reset confirm functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.url = reverse('core:reset-password')

        # Create user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Test User',
            active=True
        )

        # Create tokens with different statuses
        self.valid_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # Generate a non-existent token
        self.nonexistent_token = uuid.uuid4()
        while PasswordReset.objects.filter(key=self.nonexistent_token).exists():
            self.nonexistent_token = uuid.uuid4()

    @parameterized.expand([
        # Test case format:
        # (name, token, new_password, confirm_password, expected_status, expected_error_field)

        # Valid cases
        ('valid_reset', 'valid', 'NewSecurePassword123!', 'NewSecurePassword123!', status.HTTP_200_OK, None),

        # Password mismatch
        ('password_mismatch', 'valid', 'NewSecurePassword123!', 'DifferentPassword123!', status.HTTP_400_BAD_REQUEST, 'confirm_password'),

        # Weak passwords
        ('short_password', 'valid', 'short', 'short', status.HTTP_400_BAD_REQUEST, 'new_password'),
        ('common_password', 'valid', 'password123', 'password123', status.HTTP_400_BAD_REQUEST, 'new_password'),

        # Token issues
        ('invalid_token', 'invalid', 'NewSecurePassword123!', 'NewSecurePassword123!', status.HTTP_400_BAD_REQUEST, 'token'),
        ('nonexistent_token', 'nonexistent', 'NewSecurePassword123!', 'NewSecurePassword123!', status.HTTP_400_BAD_REQUEST, 'token'),

        # Missing fields
        ('missing_token', None, 'NewSecurePassword123!', 'NewSecurePassword123!', status.HTTP_400_BAD_REQUEST, 'token'),
        ('missing_new_password', 'valid', None, 'NewSecurePassword123!', status.HTTP_400_BAD_REQUEST, 'new_password'),
        ('missing_confirm_password', 'valid', 'NewSecurePassword123!', None, status.HTTP_400_BAD_REQUEST, 'confirm_password'),

        # Empty fields
        ('empty_token', '', 'NewSecurePassword123!', 'NewSecurePassword123!', status.HTTP_400_BAD_REQUEST, 'token'),
        ('empty_new_password', 'valid', '', '', status.HTTP_400_BAD_REQUEST, 'new_password'),
        ('empty_confirm_password', 'valid', 'NewSecurePassword123!', '', status.HTTP_400_BAD_REQUEST, 'confirm_password'),
    ])
    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm(self, name, token, new_password, confirm_password, expected_status, expected_error_field, mock_verify):
        """Test confirming password reset with various inputs."""
        # Set up mock for token verification
        if token == 'valid':
            mock_verify.return_value = (self.user, None)
            token = self.valid_reset.key
        elif token == 'invalid':
            mock_verify.return_value = (None, 'Invalid password reset link')
            token = 'invalid-uuid'
        elif token == 'nonexistent':
            mock_verify.return_value = (None, 'Invalid password reset link')
            token = self.nonexistent_token

        data = {}
        if token is not None:
            data['token'] = token
        if new_password is not None:
            data['new_password'] = new_password
        if confirm_password is not None:
            data['confirm_password'] = confirm_password

        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, expected_status)

        if expected_error_field:
            self.assertIn(expected_error_field, response.data['data'])
        elif expected_status == status.HTTP_200_OK:
            # Verify the password was changed
            self.user.refresh_from_db()
            self.assertTrue(self.user.check_password(new_password))

            # Verify the token was marked as used
            self.valid_reset.refresh_from_db()
            self.assertTrue(self.valid_reset.used)


class ParameterizedPasswordResetE2ETests(TestCase):
    """Parameterized end-to-end test cases for the password reset process."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.request_url = reverse('core:request-password-reset')
        self.validate_url = reverse('core:validate-reset-token')
        self.reset_url = reverse('core:reset-password')

        # Create users with different statuses
        self.active_user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Active User',
            active=True
        )

        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='oldpassword123',
            name='Inactive User',
            active=False
        )

    @parameterized.expand([
        # Test case format:
        # (name, email, should_succeed)

        # Success case - active user
        ('active_user', '<EMAIL>', True),

        # Failure cases
        ('inactive_user', '<EMAIL>', False),
        ('nonexistent_user', '<EMAIL>', False),
    ])
    @patch('common.utils.email.send_mail')
    def test_full_password_reset_flow(self, name, email, should_succeed, mock_send_mail):
        """Test the complete password reset flow with different user types."""
        # Step 1: Request password reset
        response = self.client.post(self.request_url, {'email': email})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check if a token was created
        reset = None
        if should_succeed:
            user = User.objects.get(email=email)
            reset = PasswordReset.objects.filter(user=user, used=False).first()
            self.assertIsNotNone(reset)
        else:
            reset_count = PasswordReset.objects.all().count()
            self.assertEqual(reset_count, 0)
            # Since we can't proceed, just return
            return

        # Step 2: Validate token
        response = self.client.post(self.validate_url, {'token': reset.key})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['email'], email)

        # Step 3: Reset password
        new_password = 'NewSecurePassword123!'
        response = self.client.post(self.reset_url, {
            'token': reset.key,
            'new_password': new_password,
            'confirm_password': new_password
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify password was changed
        user.refresh_from_db()
        self.assertTrue(user.check_password(new_password))

        # Verify token was marked as used
        reset.refresh_from_db()
        self.assertTrue(reset.used)

        # Try to use the token again
        response = self.client.post(self.reset_url, {
            'token': reset.key,
            'new_password': 'AnotherPassword123!',
            'confirm_password': 'AnotherPassword123!'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Verify password wasn't changed again
        user.refresh_from_db()
        self.assertTrue(user.check_password(new_password))
        self.assertFalse(user.check_password('AnotherPassword123!'))
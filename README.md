# Mississippi Firefighter's Association (MFA) Management System - Backend

## Overview

The MFA Management System Backend is a comprehensive Django REST Framework application designed to manage the Mississippi Firefighter's Association's membership, departments, payments, and administrative operations. This system serves as the API backend for the MFA Admin Portal, providing robust data management, authentication, and business logic processing.

## Technology Stack

### Core Framework
- **Django 5.1.x**: Python web framework for rapid development
- **Django REST Framework**: Powerful toolkit for building Web APIs
- **Simple JWT**: JSON Web Token authentication for stateless API authentication
- **Django Filters**: Advanced query filtering for API endpoints
- **Django Simple History**: Model history tracking for audit purposes
- **CORS Headers**: Cross-Origin Resource Sharing support for frontend integration

### Database
- **MySQL**: Production database hosted on Digital Ocean
- **SQLite**: Development database for local testing
- **Django ORM**: Object-Relational Mapping for database interactions

### Development Tools
- **Pipenv**: Python dependency management with virtual environments
- **Git**: Version control system
- **pylint**: Python code linting for code quality

### Email & Notifications
- **SMTP**: Email delivery via Hostinger SMTP server
- **HTML Templates**: Custom email templates for notifications

### Payment Processing
- **PayPal Integration**: For processing membership dues and donations
- **Invoice Generation**: Automated invoice creation and tracking

## Project Structure

### Core Application (`core/`)
The central application handling user management, authentication, and membership:

- **Models**:
  - `Member`: Custom user model extending Django's AbstractUser with membership fields
  - `Department`: Organizational structure for member grouping
  - `EventRegistration`: Event registration tracking with invoice generation

- **Authentication System**:
  - JWT-based authentication with access and refresh tokens
  - Token blacklisting for secure logout
  - Two-step verification (email verification + admin approval)
  - Password reset functionality with secure tokens
  - Activity tracking for audit purposes

- **Member Management**:
  - Comprehensive member profiles with contact information
  - Membership status tracking (active, pending, expired)
  - Member types (regular, associate, honorary, life)
  - Department assignments and transfers
  - Contact information management and validation
  - Member search and filtering
  - Label printing for mailings

- **Department Management**:
  - Department creation and configuration
  - Member assignment to departments
  - Department-specific settings and reporting

- **Views**:
  - Admin views for member and department management
  - Authentication views for user registration and login
  - Public views for limited public access

- **Serializers**:
  - Data validation and transformation
  - Nested serializers for complex data structures
  - Custom field handling

- **URLs**:
  - RESTful API endpoint routing
  - Versioned API structure

### Payments Application (`payments/`)
Handles all financial transactions:

- **Payment Processing**:
  - PayPal integration for online payments
  - Payment status tracking (pending, completed, failed)
  - Receipt generation and email delivery
  - Invoice management and tracking

- **Payment Types**:
  - Membership dues with renewal tracking
  - Event registrations with attendee management
  - Donations with categorization

- **Models**:
  - Payment records with transaction details
  - Invoice tracking and management
  - Payment method information

- **Views**:
  - Payment creation and processing
  - Payment history and reporting
  - Receipt generation

### Common Application (`common/`)
Shared utilities and components:

- **API Response Format**:
  - Standardized response structure for consistency
  - Comprehensive error handling and reporting

- **Utilities**:
  - Custom exceptions with detailed error messages
  - Pagination for large data sets
  - Permission classes for access control
  - Filters for advanced data querying
  - Activity tracking decorators

- **Templates**:
  - Email templates for notifications
  - PDF templates for receipts and invoices

## Coding Style & Conventions

The MFA Backend follows a consistent and well-structured coding style that prioritizes readability, maintainability, and scalability. This section outlines the key coding conventions and patterns used throughout the codebase.

### Code Organization

- **Modular Architecture**: The project is organized into distinct Django applications (`core`, `payments`, `common`) with clear separation of concerns.
- **Package-by-Feature**: Within each application, code is organized by feature rather than by type, with subdirectories for models, views, serializers, and URLs.
- **Hierarchical Imports**: Import statements follow a consistent pattern from most general to most specific (standard library → third-party → local).
- **Explicit Relative Imports**: Using explicit relative imports (e.g., `from .models import Department`) for intra-application references.

### Naming Conventions

- **PEP 8 Compliance**: Following Python's PEP 8 style guide for naming conventions:
  - `snake_case` for variables, functions, methods, and modules
  - `PascalCase` for classes and exceptions
  - `UPPER_CASE` for constants
- **Descriptive Names**: Using clear, descriptive names that indicate purpose and functionality
- **Consistent Prefixes**: Using consistent prefixes for related functions (e.g., `get_`, `create_`, `update_`, `delete_`)
- **Plural for Collections**: Using plural names for collections (e.g., `members`, `departments`)

### Documentation

- **Comprehensive Docstrings**: Detailed docstrings for all classes and methods following Google's docstring format
- **Code Comments**: Inline comments for complex logic or non-obvious implementations
- **Module-Level Docstrings**: Each module begins with a docstring explaining its purpose and contents
- **Type Hints**: Gradual adoption of Python type hints for improved code clarity and IDE support

### Class and Function Design

- **Single Responsibility Principle**: Each class and function has a single, well-defined responsibility
- **Inheritance Hierarchy**: Proper use of inheritance for code reuse (e.g., `BaseAPIView` as the foundation for all API views)
- **Composition Over Inheritance**: Favoring composition over inheritance when appropriate
- **Method Chaining**: Using method chaining for query construction and filtering
- **Default Parameters**: Providing sensible defaults for function parameters

### API Design Patterns

- **Standardized Response Format**: All API responses follow a consistent format with `message`, `success`, and `data` fields
- **Custom Response Classes**: Using `APIResponse` class to ensure consistency across all endpoints
- **Decorator Pattern**: Extensive use of decorators for cross-cutting concerns like activity tracking and permissions
- **Serializer-Based Validation**: Using DRF serializers for input validation and data transformation
- **Nested Serializers**: Implementing nested serializers for complex data structures

### Database and Model Patterns

- **Abstract Base Classes**: Using abstract base classes for shared model functionality
- **Custom Model Managers**: Implementing custom model managers for specialized query operations
- **Enum-Style Choices**: Using class-based choices for enumeration fields
- **Comprehensive Field Validation**: Implementing validators at the model level
- **Consistent Timestamps**: All models include `date_created` and `date_updated` fields
- **Historical Records**: Using Django Simple History for audit trails on all models

### Error Handling

- **Custom Exception Classes**: Defining custom exceptions for domain-specific error cases
- **Centralized Error Handling**: Using middleware and custom exception handlers for consistent error responses
- **Detailed Error Messages**: Providing clear, actionable error messages
- **Graceful Degradation**: Implementing fallbacks and defaults for error scenarios

### Testing Approach

- **Test-Driven Development**: Writing tests before implementation for critical components
- **Comprehensive Test Coverage**: Aiming for high test coverage across all modules
- **Isolated Unit Tests**: Testing individual components in isolation
- **Integration Tests**: Testing interactions between components
- **API Tests**: End-to-end testing of API endpoints

### Security Practices

- **Input Validation**: Thorough validation of all user inputs
- **Parameterized Queries**: Using ORM queries to prevent SQL injection
- **Authentication Checks**: Consistent authentication and authorization checks
- **Secure Password Handling**: Following best practices for password storage and reset
- **CSRF Protection**: Implementing Cross-Site Request Forgery protection

### Performance Considerations

- **Query Optimization**: Using select_related and prefetch_related for efficient database queries
- **Pagination**: Implementing pagination for large data sets
- **Caching Strategy**: Strategic use of caching for frequently accessed data
- **Atomic Transactions**: Using database transactions for data integrity
- **Bulk Operations**: Using bulk create and update operations when appropriate

## API Design

### Authentication Endpoints
- `POST /api/auth/register/`: User registration with validation
- `POST /api/auth/login/`: User login (returns JWT tokens)
- `POST /api/auth/logout/`: User logout (blacklists refresh token)
- `POST /api/auth/change-password/`: Change user password with validation
- `POST /api/auth/user-change-password/`: Admin changing member password
- `GET /api/auth/unverified-users/`: List users pending verification with filtering
- `POST /api/auth/admin-verify-email/:id/`: Admin verifies user email
- `POST /api/auth/verify-email/:id/`: User verifies own email
- `GET /api/auth/user-activities/`: List user activity logs with filtering

### Member Management Endpoints
- `GET /api/admin/members/`: List all members with comprehensive filtering
- `POST /api/admin/members/create/`: Create new member with validation
- `GET /api/admin/members/:id/`: Get detailed member information
- `PUT /api/admin/members/:id/update/`: Update member information
- `DELETE /api/admin/members/:id/delete/`: Delete member (soft delete)
- `POST /api/admin/members/print-labels/`: Generate member mailing labels
- `GET /api/admin/members/export/`: Export member data to Excel/CSV

### Department Management Endpoints
- `GET /api/admin/departments/`: List all departments with filtering
- `POST /api/admin/departments/create/`: Create new department
- `GET /api/admin/departments/:id/`: Get department details
- `PUT /api/admin/departments/:id/update/`: Update department information
- `DELETE /api/admin/departments/:id/delete/`: Delete department
- `GET /api/admin/departments/:id/members/`: List members in a department

### Payment Endpoints
- `GET /api/payments/`: List all payments with filtering
- `POST /api/payments/create/`: Create new payment record
- `GET /api/payments/:id/`: Get payment details
- `GET /api/payments/member/:id/`: Get member payment history
- `POST /api/payments/process/`: Process a payment through PayPal
- `GET /api/payments/report/`: Generate payment reports

## Data Models

### Member Model
```python
class Member(AbstractUser):
    # Membership classification
    class MembershipStatus(models.TextChoices):
        MEMBER = 'member', 'Member'
        ASSOCIATE_MEMBER = 'associate_member', 'Associate Member'
        HONORARY_MEMBER = 'honorary_member', 'Honorary Member'
        LIFE_MEMBER = 'life_member', 'Life Member'

    # Account status
    class LeadStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        PENDING = 'pending', 'Pending'

    # Role types
    class Role(models.TextChoices):
        CAREER = 'career', 'Career'
        VOLUNTEER = 'volunteer', 'Volunteer'
        OTHER = 'other', 'Other'

    # Gender options
    class Gender(models.TextChoices):
        MALE = 'male', 'Male'
        FEMALE = 'female', 'Female'
        OTHER = 'other', 'Other'
        PREFER_NOT_TO_SAY = 'prefer_not_to_say', 'Prefer not to say'

    # Core fields
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=255)
    membership_class = models.CharField(max_length=20, choices=MembershipStatus.choices)

    # Contact information
    business_phone = models.CharField(max_length=20, blank=True)
    home_phone = models.CharField(max_length=20, blank=True)
    mobile_phone = models.CharField(max_length=20, blank=True)
    address = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    zip_code = models.CharField(max_length=20, blank=True)

    # Additional information
    county = models.CharField(max_length=255, blank=True)
    account = models.CharField(max_length=255, blank=True)
    lead_status = models.CharField(max_length=20, choices=LeadStatus.choices)
    role = models.CharField(max_length=20, choices=Role.choices)
    gender = models.CharField(max_length=20, choices=Gender.choices)
    dob = models.DateTimeField(null=True, blank=True)

    # Department relationship
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True)

    # Membership status
    membership_active = models.BooleanField(default=False)
    orig_join_date = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True)
    picture = models.TextField(blank=True)

    # Tracking
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()
```

### Department Model
```python
class Department(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()
```

### Payment Model
```python
class Payment(models.Model):
    # Payment status options
    class PaymentStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        COMPLETED = 'completed', 'Completed'
        FAILED = 'failed', 'Failed'
        REFUNDED = 'refunded', 'Refunded'

    # Payment method options
    class PaymentMethod(models.TextChoices):
        PAYPAL = 'paypal', 'PayPal'
        CREDIT_CARD = 'credit_card', 'Credit Card'
        CHECK = 'check', 'Check'
        CASH = 'cash', 'Cash'

    # Payment type options
    class PaymentType(models.TextChoices):
        MEMBERSHIP = 'membership', 'Membership'
        EVENT = 'event', 'Event'
        DONATION = 'donation', 'Donation'

    # Core fields
    member = models.ForeignKey('core.Member', on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_date = models.DateTimeField(auto_now_add=True)

    # Payment details
    payment_method = models.CharField(max_length=20, choices=PaymentMethod.choices)
    status = models.CharField(max_length=20, choices=PaymentStatus.choices)
    invoice_number = models.CharField(max_length=50, unique=True)
    payment_type = models.CharField(max_length=20, choices=PaymentType.choices)

    # Transaction details
    transaction_id = models.CharField(max_length=255, blank=True)
    notes = models.TextField(blank=True)

    # History tracking
    history = HistoricalRecords()
```

## Authentication System

### JWT Authentication
- Access tokens (short-lived, 15 minutes)
- Refresh tokens (longer-lived, 7 days)
- Token blacklisting for secure logout
- Token refresh mechanism

### User Registration Flow
1. User registers with email, name, and password
2. System validates input and creates inactive account
3. Verification email is sent with secure token
4. User clicks verification link to verify email
5. Admin reviews and approves account
6. User receives approval notification
7. User can log in with verified credentials

### Password Reset Flow
1. User requests password reset with email
2. System sends password reset link with secure token
3. User sets new password with token validation
4. System confirms password change

## Activity Tracking

User activities are tracked using a custom decorator:

```python
@track_activity("User logged in")
def some_view(request):
    # View logic
```

This creates entries in the activity log with:
- User ID and name
- Timestamp
- IP address
- Action description
- URL path

Activities are viewable in the admin interface for audit purposes.

## Development Setup

### Prerequisites
- Python 3.10+
- Pipenv
- Git
- MySQL (optional for development)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd mfa_backend

# Install dependencies
pipenv install

# Activate virtual environment
pipenv shell

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run development server
python manage.py runserver
```

### Environment Variables
- `DEBUG`: Enable/disable debug mode (default: True)
- `SECRET_KEY`: Django secret key for security
- `DATABASE_URL`: Database connection string
- `EMAIL_HOST`: SMTP server hostname
- `EMAIL_PORT`: SMTP server port
- `EMAIL_HOST_USER`: SMTP username
- `EMAIL_HOST_PASSWORD`: SMTP password
- `EMAIL_USE_TLS`: Use TLS for email (True/False)
- `EMAIL_USE_SSL`: Use SSL for email (True/False)
- `DEFAULT_FROM_EMAIL`: Default sender email
- `PAYPAL_CLIENT_ID`: PayPal API client ID
- `PAYPAL_CLIENT_SECRET`: PayPal API client secret
- `PAYPAL_MODE`: PayPal mode (sandbox/live)
- `FRONTEND_URL`: Frontend application URL for redirects

## API Response Format

All API endpoints follow a consistent response format for uniformity:

```json
{
  "message": "Success or error message",
  "data": "Actual data or null",
  "status_code": "HTTP status code"
}
```

### Success Response Example
```json
{
  "message": "Member created successfully",
  "data": {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>",
    "membership_class": "member",
    "department": {
      "id": 5,
      "name": "Central Region"
    }
  },
  "status_code": 201
}
```

### Error Response Example
```json
{
  "message": "Invalid input data",
  "data": {
    "email": ["This field is required"],
    "name": ["This field is required"]
  },
  "status_code": 400
}
```

## Error Handling

The system implements comprehensive error handling:

- **400 Bad Request**: Invalid input data with field-specific errors
## Deployment

### Production Setup
1. Set up a MySQL database on Digital Ocean
2. Configure environment variables for production
3. Collect static files: `python manage.py collectstatic`
4. Set up a production WSGI server (Gunicorn)
5. Configure a reverse proxy (Nginx)
6. Set up SSL certificates for HTTPS
7. Configure database backups

### Current Deployment
The application is deployed on Digital Ocean with:
- MySQL database service
- Ubuntu 20.04 LTS
- Nginx as reverse proxy
- Gunicorn as WSGI server
- Let's Encrypt SSL certificates

## Testing

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test API endpoints and data flow
- **Run Tests**: `python manage.py test`

## Maintenance

### Database Backups
Regular database backups are configured through Digital Ocean's automated backup system.

### Logging
Comprehensive logging is implemented for debugging and monitoring:
- Error logs
- Access logs
- Security logs
- Performance metrics

### Monitoring
System health monitoring through Digital Ocean's monitoring tools.

## Documentation

- **API Documentation**: Available at `/docs/` when the server is running
- **Code Documentation**: Docstrings and comments throughout the codebase
- **Database Schema**: Entity-relationship diagrams in the `docs/` directory

## License

Proprietary - All rights reserved

© Mississippi Firefighter's Association
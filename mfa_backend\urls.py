"""
URL configuration for mfa_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from core.views.auth.token_views import CustomTokenRefreshView
from core.views.debug import UrlsDebugView
from core.views.test_pdf import TestPdfView
from payments import urls as payment_urls

urlpatterns = [
    path('admin/', admin.site.urls),

    # Core app URLs - using the modular URL configuration
    path('api/', include('core.urls')),

    # Payment app URLs
    path('api/payments/', include(payment_urls)),

    # JWT Token URLs
    path('api/token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),

    # Debug URLs
    path('api/debug/urls/', UrlsDebugView.as_view(), name='debug-urls'),

    # Test PDF URL
    path('api/test-pdf/', TestPdfView.as_view(), name='test-pdf'),
]

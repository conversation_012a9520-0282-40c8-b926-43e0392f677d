"""
Tests for payment services to verify improvements work correctly.
"""
from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from unittest.mock import patch, MagicMock

from core.models import Member, EventRegistration
from payments.models import Payment, InvoiceDueDate
from payments.services import PaymentService, PayPalService, InvoiceService
from payments.services.paypal_service import PayPalAPIError
from payments.config import PaymentConfig


class PaymentServiceTest(TestCase):
    """Test PaymentService functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.member1 = Member.objects.create(
            email='<EMAIL>',
            name='Test Member 1'
        )
        self.member2 = Member.objects.create(
            email='<EMAIL>', 
            name='Test Member 2'
        )
        
        # Create a due date
        self.due_date = InvoiceDueDate.objects.create(
            due_date=timezone.now().date()
        )
    
    def test_generate_invoice_number(self):
        """Test invoice number generation"""
        invoice_number = PaymentService.generate_invoice_number()
        self.assertIsNotNone(invoice_number)
        self.assertIn('-', invoice_number)
        
        # Test event invoice number
        event_invoice = PaymentService.generate_invoice_number(Payment.PaymentFor.EVENT)
        self.assertIsNotNone(event_invoice)
        self.assertIn('-E', event_invoice)
    
    def test_create_membership_payment(self):
        """Test membership payment creation"""
        covered_members = [self.member1, self.member2]
        amount = Decimal('45.00')
        
        payment = PaymentService.create_membership_payment(
            payer=self.member1,
            covered_members=covered_members,
            amount=amount
        )
        
        self.assertEqual(payment.payer, self.member1)
        self.assertEqual(payment.amount, amount)
        self.assertEqual(payment.total_amount, amount * 2)  # 2 members
        self.assertEqual(payment.covered_members.count(), 2)
        self.assertEqual(payment.payment_for, Payment.PaymentFor.MEMBERSHIP)
    
    def test_calculate_total_amount(self):
        """Test total amount calculation"""
        amount = Decimal('45.00')
        total = PaymentService.calculate_total_amount(amount, 3)
        self.assertEqual(total, Decimal('135.00'))
        
        # Test with zero members
        total_zero = PaymentService.calculate_total_amount(amount, 0)
        self.assertEqual(total_zero, amount)


class PayPalServiceTest(TestCase):
    """Test PayPalService functionality"""
    
    @patch('payments.services.paypal_service.requests.request')
    def test_get_access_token_success(self, mock_request):
        """Test successful access token retrieval"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_token',
            'expires_in': 3600
        }
        mock_request.return_value = mock_response
        
        service = PayPalService()
        token = service.get_access_token()
        
        self.assertEqual(token, 'test_token')
    
    @patch('payments.services.paypal_service.requests.request')
    def test_get_access_token_failure(self, mock_request):
        """Test access token retrieval failure"""
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.json.return_value = {'error': 'unauthorized'}
        mock_request.return_value = mock_response
        
        service = PayPalService()
        
        with self.assertRaises(PayPalAPIError):
            service.get_access_token()
    
    @patch.object(PayPalService, 'get_access_token')
    @patch('payments.services.paypal_service.requests.request')
    def test_create_order_success(self, mock_request, mock_token):
        """Test successful order creation"""
        mock_token.return_value = 'test_token'
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = {
            'id': 'order_123',
            'links': [{'rel': 'approve', 'href': 'https://paypal.com/approve'}]
        }
        mock_request.return_value = mock_response
        
        service = PayPalService()
        order = service.create_order(
            amount=100.0,
            description='Test payment',
            reference_id='ref_123',
            return_url='https://example.com/return',
            cancel_url='https://example.com/cancel'
        )
        
        self.assertEqual(order['id'], 'order_123')


class InvoiceServiceTest(TestCase):
    """Test InvoiceService functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.member = Member.objects.create(
            email='<EMAIL>',
            name='Test Member'
        )
        
        self.payment = Payment.objects.create(
            payer=self.member,
            amount=Decimal('45.00'),
            total_amount=Decimal('45.00'),
            payment_for=Payment.PaymentFor.MEMBERSHIP
        )
        self.payment.covered_members.add(self.member)
    
    def test_generate_invoice_items(self):
        """Test invoice item generation"""
        items = InvoiceService.generate_invoice_items(self.payment)
        
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0].quantity, 1)
        self.assertEqual(items[0].amount, Decimal('45.00'))
        self.assertIn('Test Member', items[0].description)
    
    def test_validate_payment_for_invoice(self):
        """Test payment validation for invoice generation"""
        # Valid payment
        errors = InvoiceService.validate_payment_for_invoice(self.payment)
        self.assertEqual(len(errors), 0)
        
        # Invalid payment - no amount
        invalid_payment = Payment.objects.create(
            payer=self.member,
            amount=Decimal('0.00'),
            payment_for=Payment.PaymentFor.MEMBERSHIP
        )
        errors = InvoiceService.validate_payment_for_invoice(invalid_payment)
        self.assertGreater(len(errors), 0)
    
    def test_get_invoice_filename(self):
        """Test invoice filename generation"""
        self.payment.invoice_number = '24001-001'
        filename = InvoiceService.get_invoice_filename(self.payment)
        
        expected = PaymentConfig.INVOICE_FILENAME_FORMAT.format(invoice_number='24001-001')
        self.assertEqual(filename, expected)
        
        # Test bulk filename
        bulk_filename = InvoiceService.get_invoice_filename(self.payment, prefix='bulk_invoice')
        expected_bulk = PaymentConfig.BULK_INVOICE_FILENAME_FORMAT.format(invoice_number='24001-001')
        self.assertEqual(bulk_filename, expected_bulk)
    
    def test_generate_invoice_context(self):
        """Test invoice context generation"""
        context = InvoiceService.generate_invoice_context(self.payment)
        
        self.assertIn('invoice', context)
        self.assertIn('items', context)
        self.assertIn('total_amount', context)
        self.assertEqual(context['invoice'], self.payment)
        self.assertEqual(len(context['items']), 1)

from rest_framework import status
from rest_framework.generics import RetrieveAPIView

from common.views import BaseAPIView
from common.views import APIResponse
from common.permissions import IsStaffUser
from core.models import Member
from core.serializers import MembershipRosterAdminSerializer


class MembershipRosterDetailAdminView(BaseAPIView, RetrieveAPIView):
    """
    Retrieve a specific member's details (admin only)
    """
    queryset = Member.objects.all()
    serializer_class = MembershipRosterAdminSerializer
    permission_classes = [IsStaffUser]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        return APIResponse(
            data=serializer.data,
            message="Member details retrieved successfully",
            status_code=status.HTTP_200_OK
        ) 
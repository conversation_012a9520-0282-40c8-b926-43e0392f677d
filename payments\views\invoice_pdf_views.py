"""
Views for generating PDF invoices for payments.
Updated to use InvoiceService to eliminate code duplication.
"""
from datetime import datetime
from django.http import HttpResponse, Http404
from django.utils import timezone
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from common.views import BaseAPIView, APIResponse
from common.utils.pdf_generator import generate_invoice_pdf
from payments.models import Payment
from payments.services import InvoiceService
from core.models import Member, EventRegistration


class InvoicePDFView(BaseAPIView):
    """View for generating PDF invoices for payments"""
    permission_classes = []

    def get(self, request, payment_id, *args, **kwargs):
        """Generate a PDF invoice for a payment"""
        try:
            payment = Payment.objects.select_related('payer', 'event_registration').get(pk=payment_id)
        except Payment.DoesNotExist:
            return APIResponse(
                message="Payment not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Validate payment can generate invoice
        validation_errors = InvoiceService.validate_payment_for_invoice(payment)
        if validation_errors:
            return APIResponse(
                message="Cannot generate invoice",
                data={"errors": validation_errors},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Generate invoice context using service
        context = InvoiceService.generate_invoice_context(payment)
        filename = InvoiceService.get_invoice_filename(payment)

        return generate_invoice_pdf('pdf/invoice.html', context, filename)


class BulkInvoicePDFView(BaseAPIView):
    """View for generating bulk PDF invoices for multiple payments"""
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """Generate PDF invoices for multiple payments"""
        payment_ids = request.data.get('payment_ids', [])

        if not payment_ids:
            return APIResponse(
                message="No payment IDs provided",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Fetch all payments
        payments = Payment.objects.filter(id__in=payment_ids).select_related('payer', 'event_registration')

        if not payments:
            return APIResponse(
                message="No valid payments found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Validate all payments can generate invoices
        invalid_payments = []
        for payment in payments:
            validation_errors = InvoiceService.validate_payment_for_invoice(payment)
            if validation_errors:
                invalid_payments.append({
                    'payment_id': payment.pk,
                    'errors': validation_errors
                })

        if invalid_payments:
            return APIResponse(
                message="Some payments cannot generate invoices",
                data={"invalid_payments": invalid_payments},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # For now, just return the first payment's PDF
        # TODO: In a real implementation, you would merge multiple PDFs
        payment = payments.first()
        context = InvoiceService.generate_invoice_context(payment)
        filename = InvoiceService.get_invoice_filename(payment, prefix="bulk_invoice")

        return generate_invoice_pdf('pdf/invoice.html', context, filename)



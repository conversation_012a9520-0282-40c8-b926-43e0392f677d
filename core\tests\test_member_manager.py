"""
Tests for the MemberManager class.

This module contains comprehensive tests for the MemberManager class,
focusing on user creation, normalization, and validation.
"""
from django.test import TestCase
from django.db import IntegrityError
from django.contrib.auth import get_user_model
from django.utils import timezone

from core.models import Department, Member

User = get_user_model()


class MemberManagerTests(TestCase):
    """Test cases for the MemberManager class."""

    def setUp(self):
        """Set up test data."""
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )
        
        self.base_user_data = {
            'name': 'Test User',
            'department': self.department,
            'membership_class': Member.MembershipStatus.MEMBER,
            'active': True
        }

    def test_create_user_minimal(self):
        """Test create_user with minimal data."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.check_password('password123'))
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_user_with_extra_fields(self):
        """Test create_user with extra fields."""
        user_data = {
            'email': '<EMAIL>',
            'password': 'password123',
            **self.base_user_data
        }
        
        user = User.objects.create_user(**user_data)
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.name, 'Test User')
        self.assertEqual(user.department, self.department)
        self.assertEqual(user.membership_class, Member.MembershipStatus.MEMBER)
        self.assertTrue(user.active)

    def test_create_user_email_normalization(self):
        """Test create_user normalizes email addresses."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )
        
        self.assertEqual(user.email, '<EMAIL>')  # Domain part should be lowercase

    def test_create_user_without_password(self):
        """Test create_user with no password should not set a password."""
        user = User.objects.create_user(
            email='<EMAIL>'
        )
        
        # Should have no usable password
        self.assertFalse(user.has_usable_password())

    def test_create_user_empty_email(self):
        """Test create_user with empty email raises error."""
        with self.assertRaises(ValueError):
            User.objects.create_user(email='')

    def test_create_user_none_email(self):
        """Test create_user with None email raises error."""
        with self.assertRaises(ValueError):
            User.objects.create_user(email=None)

    def test_create_superuser(self):
        """Test create_superuser sets the right flags."""
        admin = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword'
        )
        
        self.assertEqual(admin.email, '<EMAIL>')
        self.assertTrue(admin.is_staff)
        self.assertTrue(admin.is_superuser)
        self.assertTrue(admin.active)
        self.assertTrue(admin.membership_active)

    def test_create_superuser_with_extra_fields(self):
        """Test create_superuser with extra fields."""
        admin = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword',
            name='Admin User',
            department=self.department
        )
        
        self.assertEqual(admin.email, '<EMAIL>')
        self.assertEqual(admin.name, 'Admin User')
        self.assertEqual(admin.department, self.department)
        self.assertTrue(admin.is_staff)
        self.assertTrue(admin.is_superuser)

    def test_create_superuser_overriding_defaults(self):
        """Test create_superuser won't override is_staff and is_superuser if provided."""
        with self.assertRaises(ValueError):
            User.objects.create_superuser(
                email='<EMAIL>',
                password='adminpassword',
                is_staff=False  # This should raise ValueError
            )
        
        with self.assertRaises(ValueError):
            User.objects.create_superuser(
                email='<EMAIL>',
                password='adminpassword',
                is_superuser=False  # This should raise ValueError
            )

    def test_normal_vs_superuser_defaults(self):
        """Compare defaults between normal and superuser creation."""
        normal_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )
        
        super_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='password123'
        )
        
        # Normal user defaults
        self.assertFalse(normal_user.is_staff)
        self.assertFalse(normal_user.is_superuser)
        self.assertFalse(normal_user.active)
        self.assertFalse(normal_user.membership_active)
        
        # Superuser defaults
        self.assertTrue(super_user.is_staff)
        self.assertTrue(super_user.is_superuser)
        self.assertTrue(super_user.active)
        self.assertTrue(super_user.membership_active) 
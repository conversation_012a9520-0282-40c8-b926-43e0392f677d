from django.contrib import admin
from .models import EmailVerification, PasswordReset, UserActivity

@admin.register(EmailVerification)
class EmailVerificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'key', 'verified', 'created_at', 'expires_at', 'is_expired')
    list_filter = ('verified', 'created_at', 'expires_at')
    search_fields = ('user__email', 'key')
    readonly_fields = ('key', 'created_at', 'expires_at', 'is_expired')
    
    def is_expired(self, obj):
        return obj.is_expired
    is_expired.boolean = True

@admin.register(PasswordReset)
class PasswordResetAdmin(admin.ModelAdmin):
    list_display = ('user', 'key', 'used', 'created_at', 'expires_at', 'is_expired')
    list_filter = ('used', 'created_at', 'expires_at')
    search_fields = ('user__email', 'key')
    readonly_fields = ('key', 'created_at', 'expires_at', 'is_expired')
    
    def is_expired(self, obj):
        return obj.is_expired
    is_expired.boolean = True

@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    list_display = ('user', 'description', 'timestamp')
    list_filter = ('timestamp', 'user')
    search_fields = ('user__email', 'user__name', 'description')
    readonly_fields = ('user', 'description', 'timestamp')
    date_hierarchy = 'timestamp'

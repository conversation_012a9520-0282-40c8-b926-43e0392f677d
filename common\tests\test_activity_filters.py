from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from common.models import UserActivity
from common.filters.activity_filters import UserActivityFilter

User = get_user_model()


class UserActivityFilterTests(TestCase):
    """Test cases for the UserActivityFilter"""

    def setUp(self):
        """Set up test data"""
        # Create test users
        self.user1 = User.objects.create(
            name="<PERSON>",
            email="<EMAIL>"
        )
        self.user2 = User.objects.create(
            name="<PERSON>",
            email="<EMAIL>"
        )
        self.user3 = User.objects.create(
            name="<PERSON>",
            email="<EMAIL>"
        )

        # Create activities at different times
        now = timezone.now()

        # Activities for user1
        self.activity1 = UserActivity.objects.create(
            user=self.user1,
            description="Login attempt",
            timestamp=now - timedelta(days=1)
        )
        self.activity2 = UserActivity.objects.create(
            user=self.user1,
            description="Profile update",
            timestamp=now - timedelta(hours=12)
        )

        # Activities for user2
        self.activity3 = UserActivity.objects.create(
            user=self.user2,
            description="Password reset",
            timestamp=now - timedelta(hours=6)
        )
        self.activity4 = UserActivity.objects.create(
            user=self.user2,
            description="Login successful",
            timestamp=now - timedelta(hours=2)
        )

        # Activities for user3
        self.activity5 = UserActivity.objects.create(
            user=self.user3,
            description="Profile viewed",
            timestamp=now
        )

    def test_user_id_filter(self):
        """Test filtering activities by user ID"""
        # Filter by user1's ID
        filterset = UserActivityFilter({'user': self.user1.id}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), {self.activity2, self.activity1})

        # Filter by user2's ID
        filterset = UserActivityFilter({'user': self.user2.id}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), {self.activity4, self.activity3})

        # Filter by user3's ID
        filterset = UserActivityFilter({'user': self.user3.id}, UserActivity.objects.all())
        self.assertEqual(list(filterset.qs), [self.activity5])

    def test_user_email_filter(self):
        """Test filtering activities by user email"""
        # Full email match
        filterset = UserActivityFilter({'user_email': '<EMAIL>'}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), set([self.activity2, self.activity1]))

        # Partial email match
        filterset = UserActivityFilter({'user_email': 'jane'}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), set([self.activity4, self.activity3]))

        # Case insensitive email match
        filterset = UserActivityFilter({'user_email': '<EMAIL>'}, UserActivity.objects.all())
        self.assertEqual(list(filterset.qs), [self.activity5])

    def test_user_name_filter(self):
        """Test filtering activities by user name"""
        # Full name match
        filterset = UserActivityFilter({'user_name': 'John Doe'}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), set([self.activity2, self.activity1]))

        # Partial name match
        filterset = UserActivityFilter({'user_name': 'Smith'}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), set([self.activity4, self.activity3]))

        # Case insensitive name match
        filterset = UserActivityFilter({'user_name': 'bob'}, UserActivity.objects.all())
        self.assertEqual(list(filterset.qs), [self.activity5])

    def test_description_filter(self):
        """Test filtering activities by description"""
        # Exact description match
        filterset = UserActivityFilter({'description': 'Login attempt'}, UserActivity.objects.all())
        self.assertEqual(list(filterset.qs), [self.activity1])

        # Partial description match
        filterset = UserActivityFilter({'description': 'Login'}, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match both login-related activities

        # Case insensitive description match
        filterset = UserActivityFilter({'description': 'PROFILE'}, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match both profile-related activities

    def test_time_range_filter(self):
        """Test filtering activities by time range"""
        now = timezone.now()

        # Test from_date filter - activities newer than three_hours_ago
        three_hours_ago = now - timedelta(hours=3)
        
        # Get activities newer than three_hours_ago using direct ORM query
        expected_recent_qs = UserActivity.objects.filter(timestamp__gte=three_hours_ago)
        expected_recent_activities = set(expected_recent_qs)
        expected_recent_count = expected_recent_qs.count()
        
        # Use ISO format for datetime filters
        filterset = UserActivityFilter({
            'from_date': three_hours_ago.isoformat()
        }, UserActivity.objects.all())
        
        self.assertEqual(set(filterset.qs), expected_recent_activities)
        self.assertEqual(filterset.qs.count(), expected_recent_count)

        # Test to_date filter - activities older than twelve_hours_ago
        twelve_hours_ago = now - timedelta(hours=12)
        
        # Get activities older than twelve_hours_ago
        expected_older_qs = UserActivity.objects.filter(timestamp__lte=twelve_hours_ago)
        expected_older_activities = set(expected_older_qs)
        expected_older_count = expected_older_qs.count()
        
        filterset = UserActivityFilter({
            'to_date': twelve_hours_ago.isoformat()
        }, UserActivity.objects.all())
        
        self.assertEqual(set(filterset.qs), expected_older_activities)
        self.assertEqual(filterset.qs.count(), expected_older_count)

        # Test both from_date and to_date - activities between twelve_hours_ago and one_hour_ago
        one_hour_ago = now - timedelta(hours=1)
        
        # Get activities between twelve_hours_ago and one_hour_ago
        expected_between_qs = UserActivity.objects.filter(
            timestamp__gte=twelve_hours_ago,
            timestamp__lte=one_hour_ago
        )
        expected_between_activities = set(expected_between_qs)
        expected_between_count = expected_between_qs.count()
        
        filterset = UserActivityFilter({
            'from_date': twelve_hours_ago.isoformat(),
            'to_date': one_hour_ago.isoformat()
        }, UserActivity.objects.all())
        
        self.assertEqual(set(filterset.qs), expected_between_activities)
        self.assertEqual(filterset.qs.count(), expected_between_count)

    def test_multiple_filters(self):
        """Test applying multiple filters simultaneously"""
        now = timezone.now()

        # Combine user and time filters
        filterset = UserActivityFilter({
            'user': self.user1.id,
            'from_date': (now - timedelta(hours=24)).isoformat()
        }, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 2)

        # Filter by description
        filterset = UserActivityFilter({
            'description': 'Login'
        }, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 2)

        # Complex combination of filters
        filterset = UserActivityFilter({
            'user_email': 'example.com',
            'description': 'Profile',
            'from_date': (now - timedelta(hours=24)).isoformat(),
            'to_date': now.isoformat()
        }, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 2)

    def test_empty_filters(self):
        """Test behavior with empty filter values"""
        # Empty filter dict
        filterset = UserActivityFilter({}, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 5)  # Should return all activities

        # Empty string values
        filterset = UserActivityFilter({
            'user_email': '',
            'description': ''
        }, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 5)  # Should return all activities

    def test_invalid_date_formats(self):
        """Test handling of invalid date formats"""
        # Invalid from_date
        filterset = UserActivityFilter({
            'from_date': 'invalid-date'
        }, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 5)  # Should ignore invalid date and return all

        # Invalid to_date
        filterset = UserActivityFilter({
            'to_date': 'invalid-date'
        }, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 5)  # Should ignore invalid date and return all

    def test_case_sensitivity(self):
        """Test case sensitivity of various filters"""
        # Test case insensitive email filter
        filterset = UserActivityFilter({'user_email': '<EMAIL>'}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), set([self.activity2, self.activity1]))

        # Test case insensitive description filter
        filterset = UserActivityFilter({'description': 'LOGIN'}, UserActivity.objects.all())
        self.assertEqual(filterset.qs.count(), 2)  # Should match both login-related activities

    def test_whitespace_handling(self):
        """Test handling of whitespace in filter values"""
        # Leading and trailing whitespace in email
        filterset = UserActivityFilter({'user_email': '  <EMAIL>  '}, UserActivity.objects.all())
        self.assertEqual(set(filterset.qs), set([self.activity2, self.activity1]))

        # Leading and trailing whitespace in description
        filterset = UserActivityFilter({'description': '  Login attempt  '}, UserActivity.objects.all())
        self.assertEqual(list(filterset.qs), [self.activity1])

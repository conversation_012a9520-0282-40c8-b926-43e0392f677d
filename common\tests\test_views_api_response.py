"""
Tests for the APIResponse class in the common app.

This module contains comprehensive tests for the APIResponse class,
including success and error responses, custom status codes, and edge cases.
"""
from django.test import TestCase
from rest_framework import status

from common.views import APIResponse


class APIResponseTests(TestCase):
    """Test cases for the APIResponse class."""

    def test_success_response_structure(self):
        """Test that a success response has the correct structure."""
        data = {'key': 'value'}
        response = APIResponse(message='Success message', data=data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], 'Success message')
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], data)

    def test_error_response_structure(self):
        """Test that an error response has the correct structure."""
        response = APIResponse(
            message='Error message',
            data=None,
            status_code=status.HTTP_400_BAD_REQUEST
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['message'], 'Error message')
        self.assertEqual(response.data['success'], False)
        self.assertIsNone(response.data['data'])

    def test_custom_status_code(self):
        """Test that a custom status code is properly set."""
        response = APIResponse(
            message='Custom status',
            data=None,
            status_code=status.HTTP_201_CREATED
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['success'], True)  # 201 is a success code

    def test_boundary_success_status_codes(self):
        """Test boundary cases for success status codes."""
        # Test highest success status code (399)
        response = APIResponse(status_code=399)
        self.assertEqual(response.data['success'], True)
        
        # Test lowest error status code (400)
        response = APIResponse(status_code=400)
        self.assertEqual(response.data['success'], False)
    
    def test_default_values(self):
        """Test default values for APIResponse."""
        response = APIResponse()
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], None)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], None)
    
    def test_with_empty_message(self):
        """Test APIResponse with an empty message."""
        response = APIResponse(message='')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['message'], '')
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data'], None)
    
    def test_with_empty_data(self):
        """Test APIResponse with empty data structures."""
        # Empty dict
        response = APIResponse(data={})
        self.assertEqual(response.data['data'], {})
        
        # Empty list
        response = APIResponse(data=[])
        self.assertEqual(response.data['data'], [])
        
        # Empty string
        response = APIResponse(data='')
        self.assertEqual(response.data['data'], '')
    
    def test_with_boolean_data(self):
        """Test APIResponse with boolean data."""
        # True
        response = APIResponse(data=True)
        self.assertEqual(response.data['data'], True)
        
        # False
        response = APIResponse(data=False)
        self.assertEqual(response.data['data'], False)
    
    def test_with_numeric_data(self):
        """Test APIResponse with numeric data."""
        # Integer
        response = APIResponse(data=42)
        self.assertEqual(response.data['data'], 42)
        
        # Float
        response = APIResponse(data=3.14)
        self.assertEqual(response.data['data'], 3.14)
        
        # Zero
        response = APIResponse(data=0)
        self.assertEqual(response.data['data'], 0)
    
    def test_with_none_data(self):
        """Test APIResponse with None data."""
        response = APIResponse(data=None)
        self.assertIsNone(response.data['data'])
    
    def test_with_complex_data(self):
        """Test APIResponse with complex nested data structures."""
        complex_data = {
            'string': 'value',
            'number': 42,
            'boolean': True,
            'null': None,
            'list': [1, 2, 3],
            'nested': {
                'key': 'value',
                'another_list': ['a', 'b', 'c']
            }
        }
        
        response = APIResponse(data=complex_data)
        self.assertEqual(response.data['data'], complex_data)
    
    def test_all_http_status_codes(self):
        """Test APIResponse with various HTTP status codes."""
        # Test a selection of common status codes
        status_codes = [
            status.HTTP_200_OK,
            status.HTTP_201_CREATED,
            status.HTTP_204_NO_CONTENT,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_403_FORBIDDEN,
            status.HTTP_404_NOT_FOUND,
            status.HTTP_500_INTERNAL_SERVER_ERROR
        ]
        
        for code in status_codes:
            response = APIResponse(status_code=code)
            self.assertEqual(response.status_code, code)
            self.assertEqual(response.data['success'], code < 400)
    
    def test_with_unicode_message(self):
        """Test APIResponse with Unicode characters in the message."""
        unicode_message = "こんにちは世界! Привет мир! مرحبا بالعالم!"
        response = APIResponse(message=unicode_message)
        
        self.assertEqual(response.data['message'], unicode_message)
    
    def test_with_very_long_message(self):
        """Test APIResponse with a very long message."""
        long_message = "A" * 10000  # 10,000 character message
        response = APIResponse(message=long_message)
        
        self.assertEqual(response.data['message'], long_message)
    
    def test_with_special_characters(self):
        """Test APIResponse with special characters in the message."""
        special_chars = "!@#$%^&*()_+-=[]{}|;:'\",.<>/?`~"
        response = APIResponse(message=special_chars)
        
        self.assertEqual(response.data['message'], special_chars)

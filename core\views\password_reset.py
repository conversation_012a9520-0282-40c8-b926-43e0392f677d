from rest_framework import status, permissions

from common.utils import track_activity
from common.views import BaseAPIView, APIResponse
from core.serializers.auth.password_reset import (
    RequestPasswordResetSerializer,
    ValidateResetTokenSerializer,
    PasswordResetConfirmSerializer
)


class RequestPasswordResetView(BaseAPIView):
    """
    View for requesting a password reset
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User requested password reset")
    def post(self, request):
        """
        Send a password reset email to a user
        """
        serializer = RequestPasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()

            return APIResponse(
                message="If your email address exists in our system, you will receive password reset instructions shortly",
                data={'email': serializer.validated_data['email']},
                status_code=status.HTTP_200_OK
            )

        return APIResponse(
            message="Invalid request",
            data=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )


class ValidatePasswordResetTokenView(BaseAPIView):
    """
    View for validating a password reset token
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User validated password reset token")
    def post(self, request):
        """
        Validate a password reset token
        """
        try:
            # Clean token if it's a string with whitespace
            data = request.data.copy()
            if 'token' in data and isinstance(data['token'], str):
                data['token'] = data['token'].strip()

            serializer = ValidateResetTokenSerializer(data=data)
            if serializer.is_valid():
                # Get user data if available in serializer
                response_data = None
                if 'user' in serializer.context:
                    response_data = {"email": serializer.context['user'].email}

                return APIResponse(
                    message="Valid",
                    data=response_data,
                    status_code=status.HTTP_200_OK
                )

            return APIResponse(
                message="Invalid token",
                data=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Log the exception but return a user-friendly error
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error validating reset token: {str(e)}")

            return APIResponse(
                message="Invalid token",
                data={'token': ['Invalid token format']},
                status_code=status.HTTP_400_BAD_REQUEST
            )

# Alias for backward compatibility with tests
ValidateResetTokenView = ValidatePasswordResetTokenView


class PasswordResetConfirmView(BaseAPIView):
    """
    View for confirming a password reset
    """
    permission_classes = [permissions.AllowAny]

    @track_activity(description="User reset their password")
    def post(self, request):
        """
        Reset a user's password using a reset token
        """
        try:
            # Create a copy of the data to manipulate
            data = request.data.copy()

            # Map field names if needed
            if 'password' in data and 'new_password' not in data:
                data['new_password'] = data['password']

            if 'password_confirm' in data and 'confirm_password' not in data:
                data['confirm_password'] = data['password_confirm']

            # Check if passwords are provided
            new_password = data.get('new_password')
            confirm_password = data.get('confirm_password')

            if new_password is None:
                return APIResponse(
                    message="New password is required",
                    data={'new_password': ["This field is required."]},
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            if confirm_password is None:
                return APIResponse(
                    message="Confirm password is required",
                    data={'confirm_password': ["This field is required."]},
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Check if passwords match
            if new_password != confirm_password:
                return APIResponse(
                    message="Password fields didn't match",
                    data={'confirm_password': ["Password fields didn't match."]},
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Clean token for serializer
            if 'token' in data and data['token']:
                # Ensure token is valid UUID
                try:
                    import uuid
                    data['token'] = uuid.UUID(str(data['token']))
                except (ValueError, TypeError, AttributeError):
                    return APIResponse(
                        message="Invalid token",
                        data={'token': ['Invalid token format']},
                        status_code=status.HTTP_400_BAD_REQUEST
                    )

            serializer = PasswordResetConfirmSerializer(data=data)
            if serializer.is_valid():
                user = serializer.save()

                return APIResponse(
                    message="Password has been reset successfully. You can now login with your new password.",
                    data={"email": user.email},
                    status_code=status.HTTP_200_OK
                )

            # Check if the token is invalid
            if 'token' in serializer.errors:
                return APIResponse(
                    message="Invalid token",
                    data={'token': ['Invalid password reset link']},
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            return APIResponse(
                message="Password reset failed",
                data=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Log the exception but return a user-friendly error
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in password reset: {str(e)}")

            return APIResponse(
                message="Invalid token",
                data={'error': str(e)},
                status_code=status.HTTP_400_BAD_REQUEST
            )
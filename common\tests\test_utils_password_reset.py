"""
Tests for the password reset utility functions in the common app.

This module contains comprehensive tests for the password reset utility functions,
including send_password_reset_email and verify_password_reset_token.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch, MagicMock, call

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings

from common.utils.email import send_password_reset_email, verify_password_reset_token
from common.models import PasswordReset

User = get_user_model()


class SendPasswordResetEmailTests(TestCase):
    """Test cases for the send_password_reset_email function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Store the expected reset URL format for testing
        self.reset_url_format = f"{settings.FRONTEND_URL}/reset-password/"

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_creates_reset(self, mock_send_mail):
        """Test that send_password_reset_email creates a reset record."""
        # Ensure no resets exist before the test
        self.assertEqual(PasswordReset.objects.count(), 0)
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a reset was created
        self.assertEqual(PasswordReset.objects.count(), 1)
        self.assertEqual(reset.user, self.user)
        self.assertFalse(reset.used)
        self.assertIsNotNone(reset.key)
        self.assertIsNotNone(reset.expires_at)
        
        # Verify expiration date is set correctly (24 hours from now)
        expected_expiry = timezone.now() + timedelta(hours=24)
        self.assertAlmostEqual(
            reset.expires_at.timestamp(),
            expected_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check email arguments in detail
        args = mock_send_mail.call_args[0]
        kwargs = mock_send_mail.call_args[1]
        
        # Subject should be about password reset
        self.assertEqual(args[0], "Reset your password")
        
        # Plain text message should contain key info
        plain_message = args[1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(reset.key), plain_message)
        self.assertIn(f"{self.reset_url_format}{reset.key}", plain_message)
        self.assertIn("24 hours", plain_message)
        
        # From email should be the default from email
        self.assertEqual(args[2], settings.DEFAULT_FROM_EMAIL)
        
        # Recipient should be the user's email
        self.assertEqual(args[3], [self.user.email])
        
        # HTML message should contain the reset key
        html_message = kwargs['html_message']
        self.assertIn(str(reset.key), html_message)
        
        # Email should not be sent silently
        self.assertFalse(kwargs['fail_silently'])

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_invalidates_existing_resets(self, mock_send_mail):
        """Test that send_password_reset_email invalidates existing unexpired resets."""
        # Create an existing reset
        existing_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=12)  # Not expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, existing_reset)
        
        # Check that the existing reset was marked as used
        existing_reset.refresh_from_db()
        self.assertTrue(existing_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_does_not_invalidate_expired_resets(self, mock_send_mail):
        """Test that send_password_reset_email doesn't invalidate already expired resets."""
        # Create an expired reset
        expired_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() - timedelta(hours=1)  # Already expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, expired_reset)
        
        # Check that the expired reset was NOT marked as used (it's already expired)
        expired_reset.refresh_from_db()
        self.assertFalse(expired_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_does_not_invalidate_used_resets(self, mock_send_mail):
        """Test that send_password_reset_email doesn't attempt to invalidate already used resets."""
        # Create an already used reset
        used_reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=True,
            expires_at=timezone.now() + timedelta(hours=12)  # Not expired
        )
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check that a new reset was created
        self.assertEqual(PasswordReset.objects.count(), 2)
        self.assertNotEqual(reset, used_reset)
        
        # Check that the used reset is still marked as used
        used_reset.refresh_from_db()
        self.assertTrue(used_reset.used)
        
        # Check that the email was sent with the new reset key
        mock_send_mail.assert_called_once()
        self.assertIn(str(reset.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(reset.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.render_to_string')
    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_content(self, mock_send_mail, mock_render_to_string):
        """Test the content of the password reset email."""
        # Mock render_to_string to return a simple HTML message
        mock_render_to_string.return_value = '<html>Password Reset Email</html>'
        
        # Call the function
        reset = send_password_reset_email(self.user)
        
        # Check render_to_string call
        mock_render_to_string.assert_called_once_with('emails/reset_password.html', {
            'name': self.user.name,
            'reset_url': f"{settings.FRONTEND_URL}/reset-password/{reset.key}",
            'expiry_hours': 24,
        })
        
        # Check send_mail call
        mock_send_mail.assert_called_once_with(
            "Reset your password",
            mock_send_mail.call_args[0][1],  # plain_message (complex to check exactly)
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message='<html>Password Reset Email</html>',
            fail_silently=False,
        )
        
        # Check plain message content
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(reset.key), plain_message)
        self.assertIn("24 hours", plain_message)

    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_with_no_name(self, mock_send_mail):
        """Test send_password_reset_email with a user that has no name."""
        # Create a user with no name
        user_no_name = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name=''  # Empty name
        )
        
        # Call the function
        reset = send_password_reset_email(user_no_name)
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check that the email content handles empty name gracefully
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn("Hello", plain_message)  # Should still have a greeting

    @patch('common.utils.email.send_mail', side_effect=Exception("SMTP error"))
    def test_send_password_reset_email_handles_email_error(self, mock_send_mail):
        """Test that send_password_reset_email handles email sending errors."""
        # Call the function, which should raise the exception from send_mail
        with self.assertRaises(Exception) as context:
            send_password_reset_email(self.user)
        
        # Check that the exception was raised
        self.assertEqual(str(context.exception), "SMTP error")
        
        # Check that a reset was still created despite the email error
        self.assertEqual(PasswordReset.objects.count(), 1)
        reset = PasswordReset.objects.first()
        self.assertEqual(reset.user, self.user)

    @override_settings(FRONTEND_URL=None)
    @patch('common.utils.email.send_mail')
    def test_send_password_reset_email_missing_frontend_url(self, mock_send_mail):
        """Test send_password_reset_email behavior when FRONTEND_URL setting is missing."""
        # This should still work, but might use a default or empty URL
        reset = send_password_reset_email(self.user)
        
        # Check that the reset was created
        self.assertEqual(PasswordReset.objects.count(), 1)
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check that the URL in the email doesn't contain "None"
        plain_message = mock_send_mail.call_args[0][1]
        self.assertNotIn("None", plain_message)


class VerifyPasswordResetTokenTests(TestCase):
    """Test cases for the verify_password_reset_token function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

    def test_verify_password_reset_token_success(self):
        """Test successful password reset token verification."""
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
        
        # Check that the reset was NOT marked as used (that happens later)
        self.reset.refresh_from_db()
        self.assertFalse(self.reset.used)

    def test_verify_password_reset_token_invalid_key(self):
        """Test password reset token verification with invalid key."""
        # Call the function with a non-existent key
        user, error = verify_password_reset_token(str(uuid.uuid4()))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_already_used(self):
        """Test password reset token verification with already used key."""
        # Mark the reset as used
        self.reset.used = True
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "This password reset link has already been used")

    def test_verify_password_reset_token_expired(self):
        """Test password reset token verification with expired key."""
        # Set the reset to be expired
        self.reset.expires_at = timezone.now() - timedelta(hours=1)
        self.reset.save()
        
        # Call the function
        user, error = verify_password_reset_token(str(self.reset.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Password reset link has expired")

    def test_verify_password_reset_token_with_invalid_uuid_format(self):
        """Test password reset token verification with an invalid UUID format."""
        # Call the function with an invalid UUID
        user, error = verify_password_reset_token("not-a-uuid")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_with_none_key(self):
        """Test password reset token verification with a None key."""
        # Call the function with None
        user, error = verify_password_reset_token(None)
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_with_empty_key(self):
        """Test password reset token verification with an empty key."""
        # Call the function with an empty string
        user, error = verify_password_reset_token("")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

    def test_verify_password_reset_token_with_deleted_user(self):
        """Test password reset token verification when the user has been deleted."""
        # Create a reset for a user that will be deleted
        temp_user = User.objects.create_user(
            email='<EMAIL>',
            password='temppassword123',
            name='Temp User'
        )
        
        temp_reset = PasswordReset.objects.create(
            user=temp_user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # Delete the user
        temp_user_id = temp_user.id
        temp_reset_key = temp_reset.key
        temp_user.delete()
        
        # Call the function
        user, error = verify_password_reset_token(str(temp_reset_key))
        
        # Check the result - should fail because the user is gone
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid password reset link")

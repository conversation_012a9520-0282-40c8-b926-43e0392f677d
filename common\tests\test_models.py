"""
Tests for the common app models.
"""
import uuid
from datetime import timed<PERSON><PERSON>
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from common.models import EmailVerification, PasswordReset, UserActivity
from freezegun import freeze_time

User = get_user_model()


class EmailVerificationModelTest(TestCase):
    """Test cases for the EmailVerification model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        self.verification = EmailVerification.objects.create(
            user=self.user,
        )

    def test_email_verification_creation(self):
        """Test EmailVerification model creation with proper defaults"""
        self.assertEqual(self.verification.user, self.user)
        self.assertFalse(self.verification.verified)
        self.assertIsNotNone(self.verification.key)
        self.assertIsNotNone(self.verification.created_at)
        self.assertIsNotNone(self.verification.expires_at)
        self.assertEqual(self.verification.expires_at.date(), 
                         (timezone.now() + timedelta(days=30)).date())

    def test_string_representation(self):
        """Test EmailVerification string representation"""
        self.assertEqual(
            str(self.verification),
            f"{self.user.email} - Unverified"
        )
        
        # Verify the verification
        self.verification.verified = True
        self.verification.save()
        self.assertEqual(
            str(self.verification),
            f"{self.user.email} - Verified"
        )

    def test_is_expired_property(self):
        """Test is_expired property returns correct values"""
        # Not expired initially
        self.assertFalse(self.verification.is_expired)
        
        # Expired when verified
        self.verification.verified = True
        self.verification.save()
        self.assertTrue(self.verification.is_expired)
        
        # Expired when past expiry date
        self.verification.verified = False
        future_date = timezone.now() - timedelta(days=1)
        self.verification.expires_at = future_date
        self.verification.save()
        self.assertTrue(self.verification.is_expired)


class PasswordResetModelTest(TestCase):
    """Test cases for the PasswordReset model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        self.reset = PasswordReset.objects.create(
            user=self.user,
        )

    def test_password_reset_creation(self):
        """Test PasswordReset model creation with proper defaults"""
        self.assertEqual(self.reset.user, self.user)
        self.assertFalse(self.reset.used)
        self.assertIsNotNone(self.reset.key)
        self.assertIsNotNone(self.reset.created_at)
        self.assertIsNotNone(self.reset.expires_at)
        self.assertEqual(self.reset.expires_at.date(), 
                         (timezone.now() + timedelta(hours=24)).date())

    def test_string_representation(self):
        """Test PasswordReset string representation"""
        self.assertEqual(
            str(self.reset),
            f"{self.user.email} - Unused"
        )
        
        # Mark as used
        self.reset.used = True
        self.reset.save()
        self.assertEqual(
            str(self.reset),
            f"{self.user.email} - Used"
        )

    def test_is_expired_property(self):
        """Test is_expired property returns correct values"""
        # Not expired initially
        self.assertFalse(self.reset.is_expired)
        
        # Expired when used
        self.reset.used = True
        self.reset.save()
        self.assertTrue(self.reset.is_expired)
        
        # Expired when past expiry date
        self.reset.used = False
        future_date = timezone.now() - timedelta(hours=1)
        self.reset.expires_at = future_date
        self.reset.save()
        self.assertTrue(self.reset.is_expired)


class UserActivityModelTest(TestCase):
    """Test cases for the UserActivity model."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        self.activity = UserActivity.objects.create(
            user=self.user,
            description="Test activity"
        )

    def test_user_activity_creation(self):
        """Test UserActivity model creation with proper values"""
        self.assertEqual(self.activity.user, self.user)
        self.assertEqual(self.activity.description, "Test activity")
        self.assertIsNotNone(self.activity.timestamp)

    def test_string_representation(self):
        """Test UserActivity string representation"""
        expected = f"{self.user.email} - Test activity - {self.activity.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
        self.assertEqual(str(self.activity), expected)

    def test_meta_ordering(self):
        """Test that UserActivity objects are ordered by timestamp in descending order"""
        # Create a second activity that would come after the first one
        with freeze_time(timezone.now() + timedelta(hours=1)):
            second_activity = UserActivity.objects.create(
                user=self.user,
                description="Second activity"
            )
        
        activities = UserActivity.objects.all()
        self.assertEqual(activities.first(), second_activity)
        self.assertEqual(activities.last(), self.activity)

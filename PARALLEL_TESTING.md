# Parallel Testing for MFA Backend

This document explains how to run tests in parallel to speed up the testing process.

## Prerequisites

Make sure you have installed the required packages:

```bash
pip install pytest pytest-django pytest-xdist tblib
```

## Running Tests in Parallel

### Option 1: Using <PERSON>jan<PERSON>'s Built-in Test Runner

D<PERSON><PERSON>'s test runner supports parallel execution with the `--parallel` flag:

```bash
# Run all core tests with 8 parallel processes
python manage.py test core.tests --parallel=8

# Run specific test module with 8 parallel processes
python manage.py test core.tests.test_views_departments --parallel=8
```

You can also use the provided batch file:

```bash
# Windows
run_django_tests.bat

# Linux/Mac
./run_django_tests.sh
```

### Option 2: Using pytest with xdist

pytest with the xdist plugin provides more advanced parallel testing capabilities:

```bash
# Run all core tests with 8 parallel processes
pytest core/tests -xvs -n 8

# Run specific test module with 8 parallel processes
pytest core/tests/test_views_departments.py -xvs -n 8
```

You can also use the provided script:

```bash
# Windows
run_parallel_tests.bat

# Linux/Mac
python run_parallel_tests.py
```

## Troubleshooting

### Pickling Errors

If you encounter errors about pickling tracebacks, make sure you have installed the `tblib` package:

```bash
pip install tblib
```

### Database Conflicts

When running tests in parallel, you might encounter database conflicts, especially with SQLite. Consider using a more robust database like PostgreSQL for testing if you encounter persistent issues.

### Test Independence

Make sure your tests are independent and don't rely on side effects from other tests. Tests that modify global state or depend on specific database state might fail when run in parallel.

## Performance Tips

1. **Adjust Process Count**: The optimal number of parallel processes depends on your CPU. Start with the number of CPU cores you have and adjust as needed.

2. **Use TestCase, not TransactionTestCase**: `TestCase` uses database transactions which are faster than `TransactionTestCase`.

3. **Minimize Database Operations**: Tests that perform fewer database operations will run faster.

4. **Use Fixtures Wisely**: Loading fixtures can be slow. Consider using factory methods instead.

5. **Profile Your Tests**: Identify and optimize slow tests to get the most benefit from parallel execution.

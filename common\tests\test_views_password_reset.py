"""
Tests for the password reset views in the common app.

This module contains comprehensive tests for the password reset views:
- RequestPasswordResetView
- ValidatePasswordResetTokenView
- PasswordResetConfirmView
"""
import uuid
from datetime import timedelta
from unittest.mock import patch, MagicMock

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

from common.models import PasswordReset
from common.views import (
    RequestPasswordResetView,
    ValidatePasswordResetTokenView,
    PasswordResetConfirmView
)

User = get_user_model()


class RequestPasswordResetViewTests(TestCase):
    """Test cases for the RequestPasswordResetView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User',
            active=True
        )

        # URL for requesting password reset
        self.url = reverse('core:request-password-reset')

    @patch('core.serializers.auth.password_reset.RequestPasswordResetSerializer.save')
    def test_request_password_reset_success(self, mock_save):
        """Test successful password reset request."""
        # Make the request
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('If your email address exists in our system, you will receive password reset instructions shortly', response.data['message'])

        # Check that the serializer save method was called
        mock_save.assert_called_once()

    def test_request_password_reset_invalid_email(self):
        """Test password reset request with invalid email format."""
        # Make the request with invalid email
        response = self.client.post(self.url, {'email': 'not-an-email'})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid request', response.data['message'])

    def test_request_password_reset_nonexistent_user(self):
        """Test password reset request for nonexistent user."""
        # Make the request with email that doesn't exist
        response = self.client.post(self.url, {'email': '<EMAIL>'})

        # Check the response - should be 200 OK for security reasons
        # even though the user doesn't exist
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('If your email address exists in our system, you will receive password reset instructions shortly', response.data['message'])

    def test_request_password_reset_missing_email(self):
        """Test password reset request with missing email."""
        # Make the request without email
        response = self.client.post(self.url, {})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid request', response.data['message'])

    def test_request_password_reset_method_not_allowed(self):
        """Test that only POST method is allowed."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_activity_tracking(self, mock_create_activity):
        """Test that user activity is tracked for password reset request."""
        # Make the request
        self.client.post(self.url, {'email': '<EMAIL>'})

        # User is not authenticated in the request context so activity tracking
        # is expected not to be called
        mock_create_activity.assert_not_called()


class ValidatePasswordResetTokenViewTests(TestCase):
    """Test cases for the ValidatePasswordResetTokenView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User',
            active=True
        )

        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # URL for validating password reset token
        self.url = reverse('core:validate-reset-token')

    def test_validate_password_reset_token_success(self):
        """Test successful validation of password reset token."""
        # Make the request
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], 'Valid')

    def test_validate_password_reset_token_invalid(self):
        """Test validation with an invalid token."""
        # Make the request with invalid token
        response = self.client.post(self.url, {'token': str(uuid.uuid4())})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Invalid token')

    def test_validate_password_reset_token_used(self):
        """Test validation with a used token."""
        # Mark the token as used
        self.reset.used = True
        self.reset.save()

        # Make the request
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Invalid token')

    def test_validate_password_reset_token_expired(self):
        """Test validation with an expired token."""
        # Set the token to be expired
        self.reset.expires_at = timezone.now() - timedelta(hours=1)
        self.reset.save()

        # Make the request
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Invalid token')

    def test_validate_password_reset_token_missing_token(self):
        """Test validation with missing token."""
        # Make the request without token
        response = self.client.post(self.url, {})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Invalid token')

    def test_validate_password_reset_token_method_not_allowed(self):
        """Test that only POST method is allowed."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_activity_tracking(self, mock_create_activity):
        """Test that user activity is tracked for token validation."""
        # Make the request
        self.client.post(self.url, {'token': str(self.reset.key)})

        # User is not authenticated in the request context so activity tracking
        # is expected not to be called
        mock_create_activity.assert_not_called()


class PasswordResetConfirmViewTests(TestCase):
    """Test cases for the PasswordResetConfirmView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User',
            active=True
        )

        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # URL for confirming password reset
        self.url = reverse('core:reset-password')

        # Valid data for password reset
        self.valid_data = {
            'token': str(self.reset.key),
            'new_password': 'newpassword123',
            'confirm_password': 'newpassword123'
        }

    @patch('core.serializers.auth.password_reset.PasswordResetConfirmSerializer.is_valid')
    @patch('core.serializers.auth.password_reset.PasswordResetConfirmSerializer.save')
    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_success(self, mock_verify_token, mock_save, mock_is_valid):
        """Test successful password reset confirmation."""
        # Mock the verify_password_reset_token function
        mock_verify_token.return_value = (self.user, None)

        # Mock the serializer is_valid and save methods
        mock_is_valid.return_value = True
        mock_save.return_value = self.user

        # Make the request
        response = self.client.post(self.url, self.valid_data)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['data']['email'], self.user.email)
        self.assertIn('has been reset successfully', response.data['message'])

    def test_password_reset_confirm_invalid_token(self):
        """Test password reset confirmation with an invalid token."""
        # Data with invalid token
        data = {
            'token': str(uuid.uuid4()),
            'new_password': 'newpassword123',
            'confirm_password': 'newpassword123'
        }

        # Make the request
        response = self.client.post(self.url, data)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Password reset failed')

    def test_password_reset_confirm_password_mismatch(self):
        """Test password reset confirmation with mismatched passwords."""
        # Data with mismatched passwords
        data = {
            'token': str(self.reset.key),
            'new_password': 'newpassword123',
            'confirm_password': 'differentpassword'
        }

        # Make the request
        response = self.client.post(self.url, data)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Password fields didn't match")

    def test_password_reset_confirm_weak_password(self):
        """Test password reset confirmation with a weak password."""
        # Data with a weak password
        data = {
            'token': str(self.reset.key),
            'new_password': '123',  # Too short
            'confirm_password': '123'
        }

        # Make the request
        response = self.client.post(self.url, data)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'Password reset failed')

    def test_password_reset_confirm_missing_fields(self):
        """Test password reset confirmation with missing fields."""
        # Make the request without required fields
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], 'New password is required')

    def test_password_reset_confirm_method_not_allowed(self):
        """Test that only POST method is allowed."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_activity_tracking(self, mock_verify_token, mock_create_activity):
        """Test that user activity is tracked for password reset confirmation."""
        # Mock the verify_password_reset_token function
        mock_verify_token.return_value = (self.user, None)

        # Make the request
        self.client.post(self.url, self.valid_data)

        # User is not authenticated in the request context so activity tracking
        # is expected not to be called
        mock_create_activity.assert_not_called()

"""
Views for EventConfig model.
"""
import logging
from rest_framework import status, mixins, generics
from rest_framework.permissions import IsAdminUser
from django_filters import rest_framework as filters
from django.db import IntegrityError
from django.core.exceptions import ValidationError

from common.views import BaseAPIView, APIResponse
from common.pagination import StandardPagination
from core.models.event_config import EventConfig
from core.serializers import (
    EventConfigSerializer,
    EventConfigDetailSerializer
)

logger = logging.getLogger(__name__)


class EventConfigFilter(filters.FilterSet):
    """
    Filter for EventConfig model
    """
    name = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()

    class Meta:
        model = EventConfig
        fields = ['name', 'is_active']


class EventConfigListCreateView(BaseAPIView, generics.GenericAPIView):
    """
    List and create EventConfig
    """
    permission_classes = [IsAdminUser]
    serializer_class = EventConfigSerializer
    filterset_class = EventConfigFilter
    pagination_class = StandardPagination
    queryset = EventConfig.objects.all().order_by('-is_active', 'name')

    def get_paginated_response(self, data):
        """
        Custom paginated response to match the expected format in tests.
        """
        # Create a standard paginated response
        paginated_response = self.paginator.get_paginated_response(data)
        # Return with the custom APIResponse format - data inside 'data' key
        return APIResponse(data=paginated_response.data.get('results', []))

    def get(self, request):
        """List all event configurations."""
        try:
            queryset = self.get_queryset()
            
            # Apply filters
            filtered_queryset = self.filter_queryset(queryset)
            
            # Apply pagination
            page = self.paginate_queryset(filtered_queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
                
            serializer = self.get_serializer(filtered_queryset, many=True)
            return APIResponse(data=serializer.data)
        except Exception as e:
            logger.error(f"Error in EventConfigListCreateView.get: {str(e)}")
            return APIResponse(
                message=f"Error retrieving event configurations: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
    
    def post(self, request):
        """Create a new event configuration."""
        try:
            # Log request data for debugging
            logger.debug(f"EventConfigListCreateView.post data: {request.data}")
            
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"Validation errors: {serializer.errors}")
                return APIResponse(
                    message=f"Validation errors: {serializer.errors}",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            serializer.save()
            
            return APIResponse(
                data=serializer.data,
                status_code=status.HTTP_201_CREATED,
                message="Event configuration created successfully."
            )
        except IntegrityError as e:
            # Handle integrity errors (like unique constraint violations)
            logger.error(f"IntegrityError in EventConfigListCreateView.post: {str(e)}")
            return APIResponse(
                message=f"Could not create event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except ValidationError as e:
            # Handle validation errors
            logger.error(f"ValidationError in EventConfigListCreateView.post: {str(e)}")
            return APIResponse(
                message=f"Validation error: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle any other unexpected errors
            logger.error(f"Unexpected error in EventConfigListCreateView.post: {str(e)}")
            return APIResponse(
                message=f"Error creating event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )


class EventConfigDetailView(BaseAPIView, generics.GenericAPIView):
    """
    Retrieve, update, and delete EventConfig
    """
    permission_classes = [IsAdminUser]
    serializer_class = EventConfigDetailSerializer
    queryset = EventConfig.objects.all()

    def get_object(self, config_id):
        """Get EventConfig object by ID."""
        try:
            return EventConfig.objects.get(pk=config_id)
        except EventConfig.DoesNotExist:
            return None

    def get(self, request, config_id):
        """Retrieve event configuration details."""
        try:
            config = self.get_object(config_id)
            if not config:
                return APIResponse(
                    message="Event configuration not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )
                
            serializer = self.get_serializer(config)
            return APIResponse(data=serializer.data)
        except Exception as e:
            logger.error(f"Error in EventConfigDetailView.get: {str(e)}")
            return APIResponse(
                message=f"Error retrieving event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
    
    def put(self, request, config_id):
        """Update an existing event configuration."""
        try:
            config = self.get_object(config_id)
            if not config:
                return APIResponse(
                    message="Event configuration not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )
                
            serializer = self.get_serializer(config, data=request.data)
            if not serializer.is_valid():
                logger.error(f"Validation errors in put: {serializer.errors}")
                return APIResponse(
                    message=f"Validation errors: {serializer.errors}",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            serializer.save()
            
            return APIResponse(
                data=serializer.data,
                message="Event configuration updated successfully."
            )
        except IntegrityError as e:
            # Handle integrity errors (like unique constraint violations)
            logger.error(f"IntegrityError in EventConfigDetailView.put: {str(e)}")
            return APIResponse(
                message=f"Could not update event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except ValidationError as e:
            # Handle validation errors
            logger.error(f"ValidationError in EventConfigDetailView.put: {str(e)}")
            return APIResponse(
                message=f"Validation error: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle any other unexpected errors
            logger.error(f"Unexpected error in EventConfigDetailView.put: {str(e)}")
            return APIResponse(
                message=f"Error updating event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
    
    def patch(self, request, config_id):
        """Partially update an existing event configuration."""
        try:
            config = self.get_object(config_id)
            if not config:
                return APIResponse(
                    message="Event configuration not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )
                
            serializer = self.get_serializer(config, data=request.data, partial=True)
            if not serializer.is_valid():
                logger.error(f"Validation errors in patch: {serializer.errors}")
                return APIResponse(
                    message=f"Validation errors: {serializer.errors}",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            serializer.save()
            
            return APIResponse(
                data=serializer.data,
                message="Event configuration updated successfully."
            )
        except IntegrityError as e:
            # Handle integrity errors (like unique constraint violations)
            logger.error(f"IntegrityError in EventConfigDetailView.patch: {str(e)}")
            return APIResponse(
                message=f"Could not update event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except ValidationError as e:
            # Handle validation errors
            logger.error(f"ValidationError in EventConfigDetailView.patch: {str(e)}")
            return APIResponse(
                message=f"Validation error: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Handle any other unexpected errors
            logger.error(f"Unexpected error in EventConfigDetailView.patch: {str(e)}")
            return APIResponse(
                message=f"Error updating event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
    
    def delete(self, request, config_id):
        """Delete an event configuration."""
        try:
            config = self.get_object(config_id)
            if not config:
                return APIResponse(
                    message="Event configuration not found.",
                    status_code=status.HTTP_404_NOT_FOUND
                )
                
            # Check if this is the active configuration
            if config.is_active:
                return APIResponse(
                    message="Cannot delete the active configuration. Make another configuration active first.",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            # Check if any events are using this configuration
            if config.events.exists():
                return APIResponse(
                    message="Cannot delete configuration as it is being used by events.",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            config.delete()
            return APIResponse(
                message="Event configuration deleted successfully.",
                status_code=status.HTTP_204_NO_CONTENT
            )
        except Exception as e:
            logger.error(f"Error in EventConfigDetailView.delete: {str(e)}")
            return APIResponse(
                message=f"Error deleting event configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )


class EventConfigActiveView(BaseAPIView, generics.GenericAPIView):
    """
    Get or set the active event configuration
    """
    permission_classes = [IsAdminUser]
    serializer_class = EventConfigDetailSerializer
    queryset = EventConfig.objects.all()

    def get(self, request):
        """Get the currently active event configuration."""
        try:
            active_config = EventConfig.get_active_config()
            serializer = self.get_serializer(active_config)
            return APIResponse(data=serializer.data)
        except Exception as e:
            logger.error(f"Error in EventConfigActiveView.get: {str(e)}")
            return APIResponse(
                message=f"Error retrieving active configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )
    
    def post(self, request, config_id):
        """Set a configuration as active."""
        try:
            config = EventConfig.objects.get(pk=config_id)
            
            # Set this config as active
            config.is_active = True
            config.save()
            
            serializer = self.get_serializer(config)
            return APIResponse(
                data=serializer.data,
                message="Event configuration set as active successfully."
            )
        except EventConfig.DoesNotExist:
            return APIResponse(
                message="Event configuration not found.",
                status_code=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in EventConfigActiveView.post: {str(e)}")
            return APIResponse(
                message=f"Error setting active configuration: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            ) 
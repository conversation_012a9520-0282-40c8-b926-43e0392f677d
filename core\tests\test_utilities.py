"""
Tests for the core app utilities.
"""
from django.test import TestCase, RequestFactory, override_settings
from django.urls import reverse
from django.http import Http404
from rest_framework.views import APIView, set_rollback
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied, AuthenticationFailed
from rest_framework.test import APIClient, APIRequestFactory, APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.pagination import PageNumberPagination
from rest_framework.request import Request
from unittest.mock import patch, MagicMock
from django.contrib.auth.models import AnonymousUser

from django.contrib.auth import get_user_model
from core.models import Event, Department, Member
from common.models import UserActivity
from common.utils import track_activity, handle_api_exception
from common.exception_handlers.exception_handlers import custom_exception_handler as exception_handler
from django.utils import timezone
from datetime import timedelta
from core.serializers.auth import LoginSerializer
from core.exceptions import CustomAPIException

User = get_user_model()


class ActivityTrackingTests(APITestCase):
    """Test cases for the Activity Tracking functionality."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Test User',
            is_email_verified=True  # Use is_email_verified instead of is_verified
        )
        self.url = reverse('core:login')

    def test_activity_tracking_on_login(self):
        """Test that activity is tracked on successful login."""
        data = {'email': '<EMAIL>', 'password': 'password123'}
        request = self.factory.post(self.url, data)
        request.user = self.user  # Assign the user object directly

        # Simulate the view logic that would call track_activity
        @track_activity('User logged in')
        def mock_login_view(request, *args, **kwargs):
            # Simulate successful login logic
            return {'message': 'Login successful'}

        mock_login_view(request)

        # Check if activity log was created
        self.assertTrue(UserActivity.objects.filter(user=self.user, description='User logged in').exists())
        log_entry = UserActivity.objects.get(user=self.user, description='User logged in')
        self.assertIsNotNone(log_entry.timestamp)

    def test_activity_tracking_with_exception(self):
        """Test that activity tracking handles exceptions correctly."""
        request = self.factory.get('/some-url')
        # Use the actual user object instead of a mock
        request.user = self.user

        @track_activity('Test action with exception')
        def view_raising_exception(request):
            raise ValueError("Something went wrong")

        with self.assertRaises(ValueError):
            view_raising_exception(request)

        # Check that no activity log was created due to the exception before the action completed
        self.assertFalse(UserActivity.objects.filter(user=self.user, description='Test action with exception').exists())

    def test_activity_tracking_anonymous_user(self):
        """Test that activity tracking handles anonymous users gracefully."""
        request = self.factory.get('/some-url')
        request.user = AnonymousUser()

        @track_activity('Anonymous action')
        def anonymous_view(request):
            return {'message': 'Action attempted by anonymous user'}

        anonymous_view(request)

        # Check that no activity log was created for AnonymousUser
        self.assertFalse(UserActivity.objects.filter(description='Anonymous action').exists())

    def test_activity_tracking_with_custom_description(self):
        """Test activity tracking with a custom description format."""
        request = self.factory.get('/some-url')
        # Use the actual user object instead of a mock
        request.user = self.user

        item_name = "Specific Item"

        # Define a proper description function that accepts request and item
        def get_description(request, item):
            return f'User accessed {item}'

        @track_activity(description=get_description)
        def custom_description_view(request, item):
            # Simulate view logic
            return {'message': f'Accessed {item}'}

        custom_description_view(request, item_name)

        expected_description = f'User accessed {item_name}'
        self.assertTrue(UserActivity.objects.filter(user=self.user, description=expected_description).exists())


class ExceptionHandlingTests(APITestCase):
    """Test cases for the custom exception handler."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='password',
            name='Test User'
        )
        self.request = self.factory.get('/test-url')
        self.request.user = self.user

    @patch('core.utils.capture_exception')
    def test_unknown_error_handling(self, mock_capture_exception):
        """Test the handling of unknown (non-API) exceptions."""
        exception = ValueError("An unexpected error occurred")
        response = handle_api_exception(exception, {})  # Pass context as an empty dict

        self.assertIsNotNone(response)  # Ensure a response is returned
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.data['success'], False)

        # Check for either the legacy format or the APIResponse format
        if 'error' in response.data:
            # Legacy format for backward compatibility
            self.assertEqual(response.data['error']['code'], 'server_error')
            self.assertIn('An unexpected error occurred', response.data['error']['message'])
        else:
            # APIResponse format
            self.assertIn('An unexpected error occurred', response.data['message'])
            self.assertIsNone(response.data['data'])

        mock_capture_exception.assert_called_once_with(exception)


class PaginationTests(TestCase):
    """Test cases for pagination functionality."""

    def setUp(self):
        """Set up test data."""
        self.factory = APIRequestFactory()

        # Import required DRF classes
        from rest_framework.request import Request
        from django.http import QueryDict

        # Save the Request class for later use
        self.Request = Request

        class CustomPageNumberPagination(PageNumberPagination):
            page_size = 10
            page_size_query_param = 'page_size'
            max_page_size = 100

        self.pagination = CustomPageNumberPagination()

        # Create test users
        for i in range(30):  # Create 30 users for pagination testing
            User.objects.create_user(
                email=f'user{i}@example.com',
                password='securepassword123',
                name=f'User {i}',
                active=True
            )

        # Create test events
        for i in range(30):  # Create 30 events for pagination testing
            Event.objects.create(
                event_name=f'Event {i}',
                event_date=timezone.now().date() + timedelta(days=i),
                event_location=f'Location {i}',
                is_active=True
            )

    def _get_wrapped_request(self, path):
        """Helper method to create a proper DRF Request object for pagination."""
        django_request = self.factory.get(path)
        return self.Request(django_request)

    def test_default_pagination(self):
        """Test pagination with default settings."""
        request = self._get_wrapped_request('/')
        queryset = User.objects.all().order_by('id')

        # Paginate the queryset
        paginated_queryset = self.pagination.paginate_queryset(queryset, request)

        # Check pagination
        self.assertEqual(len(paginated_queryset), 10)  # Default page size is 10
        self.assertEqual(self.pagination.page.paginator.count, 30)  # Total count is 30
        self.assertEqual(self.pagination.page.paginator.num_pages, 3)  # 30 items / 10 per page = 3 pages

    def test_custom_page_size(self):
        """Test pagination with custom page size."""
        request = self._get_wrapped_request('/?page_size=5')
        queryset = User.objects.all().order_by('id')

        # Paginate the queryset
        paginated_queryset = self.pagination.paginate_queryset(queryset, request)

        # Check pagination
        self.assertEqual(len(paginated_queryset), 5)  # Custom page size is 5
        self.assertEqual(self.pagination.page.paginator.count, 30)  # Total count is 30
        self.assertEqual(self.pagination.page.paginator.num_pages, 6)  # 30 items / 5 per page = 6 pages

    def test_page_size_limit(self):
        """Test pagination with page size exceeding the limit."""
        request = self._get_wrapped_request('/?page_size=200')  # Exceeds max_page_size of 100
        queryset = User.objects.all().order_by('id')

        # Paginate the queryset
        paginated_queryset = self.pagination.paginate_queryset(queryset, request)

        # Check pagination - adjusted expectation to match actual count (30)
        self.assertEqual(len(paginated_queryset), 30)  # Should be limited to dataset size (30)
        self.assertEqual(self.pagination.page.paginator.count, 30)  # Total count is 30
        self.assertEqual(self.pagination.page.paginator.num_pages, 1)  # 30 items / 100 per page = 1 page

    def test_invalid_page_number(self):
        """Test pagination with invalid page number."""
        request = self._get_wrapped_request('/?page=999')  # Page that doesn't exist
        queryset = User.objects.all().order_by('id')

        # Paginate the queryset - should raise an exception
        with self.assertRaises(Exception):
            self.pagination.paginate_queryset(queryset, request)

    def test_negative_page_number(self):
        """Test pagination with negative page number."""
        request = self._get_wrapped_request('/?page=-1')  # Negative page number
        queryset = User.objects.all().order_by('id')

        # Paginate the queryset - should raise an exception
        with self.assertRaises(Exception):
            self.pagination.paginate_queryset(queryset, request)

    def test_non_integer_page_number(self):
        """Test pagination with non-integer page number."""
        request = self._get_wrapped_request('/?page=abc')  # Non-integer page number
        queryset = User.objects.all().order_by('id')

        # Expecting NotFound exception for invalid page number
        with self.assertRaises(NotFound):
            self.pagination.paginate_queryset(queryset, request)

    def test_pagination_response_format(self):
        """Test the format of the pagination response."""
        request = self._get_wrapped_request('/')
        queryset = User.objects.all().order_by('id')

        # Paginate the queryset
        self.pagination.paginate_queryset(queryset, request)
        response = self.pagination.get_paginated_response([{'id': user.id} for user in queryset[:10]])

        # Check response format
        self.assertIn('count', response.data)  # Should include total count
        self.assertIn('next', response.data)  # Should include next page URL
        self.assertIn('previous', response.data)  # Should include previous page URL
        self.assertIn('results', response.data)  # Should include results

        # Check values
        self.assertEqual(response.data['count'], 30)  # Total count is 30
        self.assertIsNotNone(response.data['next'])  # Next page should exist
        self.assertIsNone(response.data['previous'])  # Previous page should not exist (we're on page 1)
        self.assertEqual(len(response.data['results']), 10)  # Should have 10 results

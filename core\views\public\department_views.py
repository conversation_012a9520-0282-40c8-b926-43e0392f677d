from rest_framework import status
from rest_framework.generics import ListAP<PERSON><PERSON>iew, RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter
from django.shortcuts import get_object_or_404
from django.db.models import Exists, OuterRef

from common.views import BaseAPIView
from common.views import APIResponse
from common.pagination import StandardPagination
from common.filters import PublicDepartmentFilter
from common.filters.public_member_filters import PublicMemberFilter
from core.models import Department, Member
from core.serializers import DepartmentsSerializer, MemberPublicSerializer


class DepartmentPublicListView(BaseAPIView, ListAPIView):
    """
    List all departments (public view)
    """
    queryset = Department.objects.all().order_by('name')
    serializer_class = DepartmentsSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = PublicDepartmentFilter

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set the model in the filter class
        self.filterset_class.Meta.model = Department

    def get_paginated_response(self, data):
        """
        Custom paginated response to match the expected format.
        """
        paginated_response = self.paginator.get_paginated_response(data)
        return APIResponse(data=paginated_response.data)

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return APIResponse(
                data=serializer.data,
                message="Departments retrieved successfully",
                status_code=status.HTTP_200_OK
            )
        except Exception as e:
            return APIResponse(
                message=f"Error retrieving departments: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )


class DepartmentPublicDetailView(BaseAPIView, RetrieveAPIView):
    """
    Retrieve a specific department's details (public view)
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentsSerializer
    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return APIResponse(
            data=serializer.data,
            message="Department details retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class DepartmentPublicMembersAPIView(BaseAPIView, ListAPIView):
    """
    List all active members in a specific department (public view)
    Only returns limited member information for public access
    """
    serializer_class = MemberPublicSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardPagination
    filter_backends = (DjangoFilterBackend, OrderingFilter)
    filterset_class = PublicMemberFilter
    ordering_fields = ['name', 'department__name', 'city']
    ordering = ['name']

    def get_queryset(self):
        # Check if this is a schema generation request
        if getattr(self, 'swagger_fake_view', False):
            # Return empty queryset for schema generation
            return Member.objects.none()

        # Get department ID from URL
        department_id = self.kwargs['pk']

        # Get the base queryset filtered by department, only active and non-deceased members
        queryset = Member.objects.filter(
            department_id=department_id,
            active=True,
            is_deceased=False
        ).select_related('department')

        return queryset.order_by('name')

    def get_paginated_response(self, data, department):
        """
        Custom paginated response to match the expected format.
        """
        # Create a standard paginated response
        paginated_response = self.paginator.get_paginated_response(data)

        # Customize the response
        response_data = {
            'results': paginated_response.data.get('results', []),
            'count': paginated_response.data.get('count', 0),
            'next': paginated_response.data.get('next'),
            'previous': paginated_response.data.get('previous')
        }

        return APIResponse(
            data=response_data,
            message=f"Members in department '{department.name}' retrieved successfully",
            status_code=status.HTTP_200_OK
        )

    def list(self, request, *args, **kwargs):
        try:
            # Get the department
            department = get_object_or_404(Department, pk=kwargs['pk'])

            # Apply filters from filter backends
            queryset = self.filter_queryset(self.get_queryset())

            # Paginate the queryset
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data, department)

            serializer = self.get_serializer(queryset, many=True)
            return APIResponse(
                data={
                    'results': serializer.data,
                    'count': len(serializer.data),
                    'next': None,
                    'previous': None
                },
                message=f"Members in department '{department.name}' retrieved successfully",
                status_code=status.HTTP_200_OK
            )
        except Exception as e:
            return APIResponse(
                message=f"Error retrieving department members: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST
            )

from django.urls import path

# Import admin views - Members
from core.views import (
    MembershipRosterCreateAdminView,
    MembershipRosterListAdminView,
    MembershipRosterDetailAdminView,
    MembershipRosterUpdateAdminView,
    MembershipRosterDeleteAdminView,
    MembershipClassTypesAPIView,
    MergeMembersView,
    PrintLabelsView,
    MembershipRosterExportAdminView,
    EventRosterExportView,
)

# Import admin views - Departments
from core.views import (
    DepartmentCreateAPIView,
    DepartmentListView,
    DepartmentDetailAPIView,
    DepartmentUpdateAPIView,
    DepartmentDeleteAPIView,
    DepartmentMembersAPIView,
    DepartmentExportView,
    DepartmentMemberExportView,
    DepartmentPaymentExportView,
)

# Import department payment views
from core.views.department_payment_views import DepartmentPaymentsView

# Admin URLs
admin_urlpatterns = [
    # Member URLs
    path('members/', MembershipRosterListAdminView.as_view(), name='admin-member-list'), # API Done
    path('members/create/', MembershipRosterCreateAdminView.as_view(), name='admin-member-create'), # API Done
    path('members/<int:pk>/', MembershipRosterDetailAdminView.as_view(), name='admin-member-detail'),
    path('members/<int:pk>/update/', MembershipRosterUpdateAdminView.as_view(), name='admin-member-update'), # API Done
    path('members/<int:pk>/delete/', MembershipRosterDeleteAdminView.as_view(), name='admin-member-delete'), # API Done
    path('members/merge/', MergeMembersView.as_view(), name='admin-member-merge'),
    path('members/print-labels/', PrintLabelsView.as_view(), name='admin-member-print-labels'),
    path('members/export/', MembershipRosterExportAdminView.as_view(), name='admin-member-export'),
    path('membership-classes/', MembershipClassTypesAPIView.as_view(), name='admin-membership-classes'), # API Done

    # Department URLs
    path('departments/', DepartmentListView.as_view(), name='admin-department-list'), # API Done
    path('departments/create/', DepartmentCreateAPIView.as_view(), name='admin-department-create'), # API Done
    path('departments/<int:pk>/', DepartmentDetailAPIView.as_view(), name='admin-department-detail'),
    path('departments/<int:pk>/update/', DepartmentUpdateAPIView.as_view(), name='admin-department-update'), # API Done
    path('departments/<int:pk>/delete/', DepartmentDeleteAPIView.as_view(), name='admin-department-delete'), # API Done
    path('departments/<int:pk>/members/', DepartmentMembersAPIView.as_view(), name='admin-department-members'),
    path('departments/<int:department_id>/members/export/', DepartmentMemberExportView.as_view(), name='admin-department-members-export'),
    path('departments/<int:department_id>/payments/', DepartmentPaymentsView.as_view(), name='admin-department-payments'),
    path('departments/<int:department_id>/payments/export/', DepartmentPaymentExportView.as_view(), name='admin-department-payments-export'),
    path('departments/export/', DepartmentExportView.as_view(), name='admin-department-export'),
    
    # Event URLs
    path('events/<int:event_id>/export/', EventRosterExportView.as_view(), name='admin-event-roster-export'),
]
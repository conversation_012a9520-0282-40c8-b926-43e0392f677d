"""
Tests for Member model validation and constraints.

This module contains tests specifically focusing on validation, constraints,
and field-level checks for the Member model.
"""
from django.test import TestCase
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from core.models import Department, Member

User = get_user_model()


class MemberValidationTests(TestCase):
    """Test cases for Member model validation and constraints."""

    def setUp(self):
        """Set up test data."""
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        self.valid_user_data = {
            'email': '<EMAIL>',
            'password': 'password123',
            'name': 'Valid User',
            'department': self.department,
            'membership_class': Member.MembershipStatus.MEMBER,
            'lead_status': Member.LeadStatus.ACTIVE,
            'role': Member.Role.VOLUNTEER,
            'gender': Member.Gender.MALE
        }

    def test_email_field_unique_constraint(self):
        """Test that email must be unique."""
        # Create first user
        User.objects.create_user(**self.valid_user_data)

        # Attempt to create second user with same email
        second_user_data = self.valid_user_data.copy()
        second_user_data['name'] = 'Second User'

        with transaction.atomic():
            with self.assertRaises(IntegrityError):
                User.objects.create_user(**second_user_data)

    def test_email_field_max_length(self):
        """Test email field max length validation."""
        # Create a user with very long email
        # RFC 5321 limits total email length to 254 characters
        # Let's create an email that exceeds this limit
        long_local_part = 'a' * 245  # This will make total length > 254 with domain
        long_email = f"{long_local_part}@example.com"

        user_data = self.valid_user_data.copy()
        user_data['email'] = long_email

        # Django's EmailField should validate this
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_email_field_formats(self):
        """Test email field format validation."""
        invalid_emails = [
            'plainaddress',
            '@missinglocal.org',
            'missingatmark.net',
            'missing.dot@com',
            '<EMAIL>',
            '<NAME_EMAIL>',
            'spaces in domain@domain .com',
        ]

        for invalid_email in invalid_emails:
            user_data = self.valid_user_data.copy()
            user_data['email'] = invalid_email
            user = User(**user_data)

            with self.assertRaises(ValidationError):
                user.full_clean()

    def test_name_field_max_length(self):
        """Test name field max length validation."""
        # Max length is 255
        name_exact_max = 'A' * 255

        # This should work
        user_data = self.valid_user_data.copy()
        user_data['name'] = name_exact_max
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Too long
        name_too_long = 'A' * 256
        user_data['name'] = name_too_long
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_membership_class_choices(self):
        """Test membership_class field choices validation."""
        # Valid choice
        user_data = self.valid_user_data.copy()
        user_data['membership_class'] = Member.MembershipStatus.ASSOCIATE_MEMBER
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Invalid choice
        user_data['membership_class'] = 'invalid_choice'
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_lead_status_choices(self):
        """Test lead_status field choices validation."""
        # Valid choice
        user_data = self.valid_user_data.copy()
        user_data['lead_status'] = Member.LeadStatus.PENDING
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Invalid choice
        user_data['lead_status'] = 'invalid_choice'
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_role_choices(self):
        """Test role field choices validation."""
        # Valid choice
        user_data = self.valid_user_data.copy()
        user_data['role'] = Member.Role.CAREER
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Invalid choice
        user_data['role'] = 'invalid_choice'
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_gender_choices(self):
        """Test gender field choices validation."""
        # Valid choice
        user_data = self.valid_user_data.copy()
        user_data['gender'] = Member.Gender.FEMALE
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Invalid choice
        user_data['gender'] = 'invalid_choice'
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_st_max_length(self):
        """Test st (state) field max length validation."""
        # Max length is 2
        state_exact_max = 'MS'

        # This should work
        user_data = self.valid_user_data.copy()
        user_data['st'] = state_exact_max
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Too long
        state_too_long = 'MST'
        user_data['st'] = state_too_long
        user = User(**user_data)

        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_dob_date_validation(self):
        """Test dob (date of birth) field validation."""
        # Valid date
        valid_dob = timezone.now() - timedelta(days=365*30)  # 30 years ago

        user_data = self.valid_user_data.copy()
        user_data['dob'] = valid_dob
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Future date (not necessarily invalid, but worth testing)
        future_dob = timezone.now() + timedelta(days=365)  # 1 year in the future
        user_data['dob'] = future_dob
        user = User(**user_data)
        user.full_clean()  # Should not raise (no validation on this)

    def test_boolean_field_validation(self):
        """Test boolean field validation."""
        boolean_fields = [
            'executive_board',
            'committee_member',
            'new_member',
            'lifetime',
            'paid_next_year',
            'is_deceased',
            'active',
            'membership_active'
        ]

        for field in boolean_fields:
            # Test with boolean values
            for value in [True, False]:
                user_data = self.valid_user_data.copy()
                user_data[field] = value
                user = User(**user_data)
                user.full_clean()  # Should not raise

    def test_orig_join_date_validation(self):
        """Test orig_join_date field validation."""
        # Valid date
        valid_date = timezone.now() - timedelta(days=365)  # 1 year ago

        user_data = self.valid_user_data.copy()
        user_data['orig_join_date'] = valid_date
        user = User(**user_data)
        user.full_clean()  # Should not raise

        # Future date (not necessarily invalid, but worth testing)
        future_date = timezone.now() + timedelta(days=365)  # 1 year in future
        user_data['orig_join_date'] = future_date
        user = User(**user_data)
        user.full_clean()  # Should not raise (no validation on this)

    def test_username_nullability(self):
        """Test username field allows null and blank values."""
        # Username should allow null/blank since we're using email instead
        user_data = self.valid_user_data.copy()
        user_data['username'] = None

        user = User(**user_data)
        user.full_clean()  # Should not raise

        user_data['username'] = ''
        user = User(**user_data)
        user.full_clean()  # Should not raise
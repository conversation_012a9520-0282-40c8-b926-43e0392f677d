from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Sum
from simple_history.admin import SimpleHistoryAdmin
from .models import Payment, InvoiceDueDate

# Register your models here.

# Custom action to mark payments as non-draft
def make_non_draft(modeladmin, request, queryset):
    queryset.update(draft=False)
make_non_draft.short_description = "Mark selected payments as non-draft"

# Custom action to update payment status to Success
def mark_as_success(modeladmin, request, queryset):
    queryset.update(status=Payment.PaymentStatus.SUCCESS)
mark_as_success.short_description = "Mark selected payments as Success"

# PaymentAdmin class to manage Payment model in admin interface
@admin.register(Payment)
class PaymentAdmin(SimpleHistoryAdmin):
    list_display = ('invoice_number', 'payer_name', 'amount_display', 'payment_date', 
                   'status', 'payment_type', 'due_date', 'payment_for', 'member_count_display', 'group_payment_display', 'is_draft',)
    list_filter = ('status', 'payment_type', 'paid_year', 'draft', 'payment_for', 
                  'due_date', 'payment_date')
    search_fields = ('invoice_number', 'payer__name', 'payer__email', 'payer__member_number',
                    'notes', 'po_number', 'transaction_id')
    date_hierarchy = 'date'
    readonly_fields = ('date', 'updated', 'invoice_number')
    actions = [make_non_draft, mark_as_success]
    list_editable = ('status', 'payment_type')
    list_per_page = 25
    save_on_top = True
    
    fieldsets = (
        ('Payment Information', {
            'fields': ('payer', 'covered_members', 'amount', 'invoice_number', 'po_number', 'status', 'payment_type', 'payment_for')
        }),
        ('Dates', {
            'fields': ('payment_date', 'due_date', 'paid_year', 'paid_next_year', 'date', 'updated')
        }),
        ('Additional Information', {
            'fields': ('draft', 'billing_address', 'notes')
        }),
        ('Payment Processing', {
            'fields': ('payment_link', 'payment_id', 'transaction_id', 'paypal_response'),
            'classes': ('collapse',),
        }),
        ('Event Registration', {
            'fields': ('event_registration',),
            'classes': ('collapse',),
        }),
    )
    filter_horizontal = ('covered_members',)
    
    def get_queryset(self, request):
        """Optimize query with prefetch_related and select_related"""
        queryset = super().get_queryset(request)
        return queryset.select_related('payer', 'event_registration').prefetch_related('covered_members')
    
    def payer_name(self, obj):
        """Display payer name with link to payer admin"""
        if obj.payer:
            return format_html('<a href="/admin/core/member/{}/change/">{}</a>', 
                               obj.payer.id, obj.payer.name)
        return "-"
    payer_name.short_description = "Payer"
    payer_name.admin_order_field = 'payer__name'
    
    def amount_display(self, obj):
        """Format amount as currency"""
        return format_html('${}', '{:.2f}'.format(obj.amount))
    amount_display.short_description = "Amount"
    amount_display.admin_order_field = 'amount'
    
    def is_draft(self, obj):
        """Display draft status with color indication"""
        if obj.draft:
            return format_html('<span style="color: red;">Yes</span>')
        return format_html('<span style="color: green;">No</span>')
    is_draft.short_description = "Draft"
    is_draft.admin_order_field = 'draft'
    
    def member_count_display(self, obj):
        """Display covered member count with highlighting"""
        count = obj.covered_members.count()
        if count > 1:
            return format_html('<span style="color: blue; font-weight: bold;">{}</span>', count)
        return str(count)
    member_count_display.short_description = "Member Count"
    
    def group_payment_display(self, obj):
        """Display group payment status with color indication"""
        is_group = obj.covered_members.count() > 1
        if is_group:
            return format_html('<span style="color: green; font-weight: bold;">Yes</span>')
        return format_html('<span style="color: gray;">No</span>')
    group_payment_display.short_description = "Group Payment"
    group_payment_display.admin_order_field = 'is_group_payment'
    
    def changelist_view(self, request, extra_context=None):
        """Add summary statistics to the admin changelist view"""
        response = super().changelist_view(request, extra_context)
        
        try:
            # Get filtered queryset
            queryset = response.context_data['cl'].queryset
            
            # Calculate total amount for the filtered payments
            total_amount = queryset.aggregate(total=Sum('amount'))['total'] or 0
            
            # Add statistics to response context
            if not extra_context:
                extra_context = {}
            extra_context['total_amount'] = total_amount
            response.context_data.update(extra_context)
        except (AttributeError, KeyError):
            pass
        
        return response

# InvoiceDueDateAdmin class to manage InvoiceDueDate model in admin interface
@admin.register(InvoiceDueDate)
class InvoiceDueDateAdmin(SimpleHistoryAdmin):
    list_display = ('due_date', 'created', 'updated')
    search_fields = ('due_date',)
    date_hierarchy = 'due_date'
    readonly_fields = ('created', 'updated')
    list_filter = ('due_date',)
    
    def get_queryset(self, request):
        """Order by due date descending by default"""
        queryset = super().get_queryset(request)
        return queryset.order_by('-due_date')

import random
from django.core.management.base import BaseCommand
from django.utils import timezone
from faker import Faker
from core.models import Member, Department

class Command(BaseCommand):
    help = 'Generates random fake members for testing'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=100, help='Number of fake members to create')

    def handle(self, *args, **options):
        count = options['count']
        fake = Faker()
        
        # Get all departments or create a default one if none exist
        departments = list(Department.objects.all())
        if not departments:
            self.stdout.write(self.style.WARNING('No departments found. Creating a default department.'))
            default_dept = Department.objects.create(name='Default Department')
            departments = [default_dept]
        
        # Membership class choices
        membership_classes = list(Member.MembershipStatus.choices)
        
        # Track progress
        self.stdout.write(f'Generating {count} fake members...')
        
        members_created = 0
        for i in range(count):
            try:
                # Generate unique email
                email = fake.unique.email()
                
                # Generate basic information
                first_name = fake.first_name()
                last_name = fake.last_name()
                name = f"{first_name} {last_name}"
                
                # Random department
                department = random.choice(departments)
                
                # Random membership class
                membership_class = random.choice(membership_classes)[0]
                
                # Create random dates
                date_joined = fake.date_time_between(start_date='-10y', end_date='now', tzinfo=timezone.get_current_timezone())
                
                # Create the member with random data
                member = Member.objects.create(
                    email=email,
                    name=name,
                    first_name=first_name,
                    last_name=last_name,
                    mi=random.choice(['', fake.random_letter().upper()]),
                    dst=random.choice(['', 'Dr.', 'Mr.', 'Mrs.', 'Ms.']),
                    title=random.choice(['', 'PhD', 'MD', 'JD']),
                    address=fake.street_address(),
                    city=fake.city(),
                    st=fake.state_abbr(),
                    zip_code=fake.zipcode(),
                    home_phone=fake.phone_number(),
                    business_phone=random.choice(['', fake.phone_number()]),
                    department=department,
                    membership_class=membership_class,
                    executive_board=random.random() < 0.1,  # 10% chance
                    committee_member=random.random() < 0.2,  # 20% chance
                    committee=random.choice(['', 'Finance', 'Membership', 'Events']) if random.random() < 0.2 else '',
                    new_member=random.random() < 0.15,  # 15% chance
                    lifetime=random.random() < 0.05,  # 5% chance
                    paid_next_year=random.random() < 0.3,  # 30% chance
                    lapel_pin=random.choice(['', 'Gold', 'Silver', 'Bronze']),
                    is_deceased=random.random() < 0.02,  # 2% chance
                    active=random.random() < 0.8,  # 80% chance
                    orig_join_date=date_joined,
                    notes=fake.paragraph() if random.random() < 0.3 else '',
                    # No password is set - admin can set it later
                )
                
                # Update counter
                members_created += 1
                if members_created % 10 == 0:
                    self.stdout.write(f'Created {members_created} members so far...')
            
            except Exception as e:
                self.stderr.write(self.style.ERROR(f'Error creating member: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {members_created} fake members!'))
        self.stdout.write(self.style.NOTICE('No passwords were set. Admins will need to set passwords manually.')) 
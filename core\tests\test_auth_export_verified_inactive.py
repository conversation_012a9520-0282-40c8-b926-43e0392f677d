"""
Tests for the VerifiedInactiveUserExportView API endpoint.
"""
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APITestCase
from common.models import EmailVerification
from django.utils import timezone
from datetime import timedelta
import io
import openpyxl

User = get_user_model()


class VerifiedInactiveUserExportViewTests(APITestCase):
    """Test cases for the VerifiedInactiveUserExportView."""

    def setUp(self):
        """Set up test data."""
        # Create admin user
        self.admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword123',
            name='Admin User'
        )
        
        # Create a regular user for testing
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='regularpassword123',
            name='Regular User'
        )
        
        # Create verified but inactive users
        self.verified_inactive_user1 = User.objects.create_user(
            email='<EMAIL>',
            password='verifiedpassword123',
            name='Verified Inactive User 1',
            active=False
        )
        
        self.verified_inactive_user2 = User.objects.create_user(
            email='<EMAIL>',
            password='verifiedpassword123',
            name='Verified Inactive User 2',
            active=False
        )
        
        # Create verification records
        self.verified_inactive_verification1 = EmailVerification.objects.create(
            user=self.verified_inactive_user1,
            verified=True,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        self.verified_inactive_verification2 = EmailVerification.objects.create(
            user=self.verified_inactive_user2,
            verified=True,
            expires_at=timezone.now() + timedelta(days=30)
        )
        
        # URL for exporting verified inactive users
        self.export_verified_inactive_url = reverse('core:verified-inactive-users-export')

    def test_export_verified_inactive_users_as_admin(self):
        """Test exporting verified but inactive users as an admin."""
        # Login as admin
        self.client.force_authenticate(user=self.admin_user)
        
        # Request the export
        response = self.client.get(self.export_verified_inactive_url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertTrue('attachment; filename=verified_inactive_users_export.xlsx' in response['Content-Disposition'])
        
        # Verify the Excel file content
        content = response.content
        excel_file = io.BytesIO(content)
        workbook = openpyxl.load_workbook(excel_file)
        worksheet = workbook.active
        
        # Check headers
        self.assertEqual(worksheet.cell(row=1, column=1).value, 'ID')
        self.assertEqual(worksheet.cell(row=1, column=2).value, 'Name')
        self.assertEqual(worksheet.cell(row=1, column=3).value, 'Email')
        
        # Check that the data includes our test users
        emails = []
        for row in range(2, worksheet.max_row + 1):
            email = worksheet.cell(row=row, column=3).value
            emails.append(email)
        
        self.assertIn(self.verified_inactive_user1.email, emails)
        self.assertIn(self.verified_inactive_user2.email, emails)

    def test_export_with_ids_list(self):
        """Test exporting specific verified but inactive users using ids_list parameter."""
        # Login as admin
        self.client.force_authenticate(user=self.admin_user)
        
        # Request the export with specific IDs
        url = f"{self.export_verified_inactive_url}?ids_list={self.verified_inactive_user1.id}"
        response = self.client.get(url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify the Excel file content
        content = response.content
        excel_file = io.BytesIO(content)
        workbook = openpyxl.load_workbook(excel_file)
        worksheet = workbook.active
        
        # Check that only the specified user is included
        emails = []
        for row in range(2, worksheet.max_row + 1):
            email = worksheet.cell(row=row, column=3).value
            emails.append(email)
        
        self.assertIn(self.verified_inactive_user1.email, emails)
        self.assertNotIn(self.verified_inactive_user2.email, emails)

    def test_export_verified_inactive_users_as_non_admin(self):
        """Test that a non-admin user cannot export verified but inactive users."""
        # Login as regular user
        self.client.force_authenticate(user=self.regular_user)
        
        # Try to request the export
        response = self.client.get(self.export_verified_inactive_url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_user_cannot_export(self):
        """Test that an unauthenticated user cannot export verified but inactive users."""
        # No authentication
        
        # Try to request the export
        response = self.client.get(self.export_verified_inactive_url)
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 
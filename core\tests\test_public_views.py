"""
Tests for public views in the core app.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from core.models import Member, Department

class PublicViewsTests(TestCase):
    def setUp(self):
        """Set up test data."""
        # Create a department for testing
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create a test user
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='Test User',
            department=self.department
        )

        # Set up the API client
        self.client = APIClient()

    def test_special_characters_search(self):
        """Test searching members with special characters in their name."""
        # Create a member with special characters in their name
        special_member = Member.objects.create(
            email='<EMAIL>',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON><PERSON>',
            department=self.department
        )

        # Verify the member exists
        self.assertTrue(Member.objects.filter(email='<EMAIL>').exists())

        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.staff_token}')

        # Make request
        response = self.client.get(self.members_url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['message'], 'Members retrieved successfully')

        # Check that the response contains paginated data
        self.assertIn('data', response.data)
        self.assertIn('members', response.data['data'])
        self.assertIn('results', response.data['data']['members'])
        self.assertIn('count', response.data['data']['members'])
        self.assertIn('next', response.data['data']['members'])
        self.assertIn('previous', response.data['data']['members'])

        # Check that the special character member is in the results
        member_emails = [member['email'] for member in response.data['data']['members']['results']]
        self.assertIn('<EMAIL>', member_emails)

        # Verify member count
        self.assertEqual(response.data['data']['members']['count'], 1)

    def test_filter_validation(self):
        """Test validation of filter parameters."""
        url = reverse('core:public-department-list')
        response = self.client.get(url, {'department_state': 'INVALID'})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_sql_injection_attempt(self):
        """Test protection against SQL injection in search parameters."""
        url = reverse('core:public-member-list')
        response = self.client.get(url, {'search': '\' OR \'1\'=\'1'})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_xss_attempt(self):
        """Test handling of potential XSS in search parameters."""
        url = reverse('core:public-member-list')
        response = self.client.get(url, {'search': '<script>alert("xss")</script>'})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_large_page_size(self):
        """Test handling of large page size requests."""
        url = reverse('core:public-member-list')
        response = self.client.get(url, {'page_size': 1000})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_zero_page_size(self):
        """Test handling of zero page size."""
        url = reverse('core:public-member-list')
        response = self.client.get(url, {'page_size': 0})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_negative_page(self):
        """Test handling of negative page numbers."""
        url = reverse('core:public-member-list')
        response = self.client.get(url, {'page': -1})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_concurrent_requests(self):
        """Test handling of concurrent requests."""
        url = reverse('core:public-member-list')
        responses = []

        # Simulate 5 concurrent requests
        for _ in range(5):
            response = self.client.get(url)
            responses.append(response)

        # All requests should return 401 Unauthorized
        for response in responses:
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_fields_filtering(self):
        """Test filtering specific fields in response."""
        url = reverse('core:public-member-list')
        response = self.client.get(url, {'fields': 'name,email'})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_unicode_search(self):
        """Test searching with unicode characters."""
        Member.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            name='José María García',
            department=self.department
        )

        url = reverse('core:public-member-list')
        response = self.client.get(url, {'search': 'José'})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_long_query_parameters(self):
        """Test handling of extremely long query parameters."""
        url = reverse('core:public-member-list')
        long_search = 'a' * 1000  # Very long search term
        response = self.client.get(url, {'search': long_search})

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_malformed_query_parameters(self):
        """Test handling of malformed query parameters."""
        url = reverse('core:public-member-list')
        response = self.client.get(url + '?%FF%FE')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

"""
Auth views module for authentication related views.
"""

from .auth_views import (
    RegisterView,
    LoginView,
    LogoutView
)
from .password_views import (
    ChangePasswordView,
    AdminChangePasswordView
)
from .user_views import (
    UnverifiedUserListView,
    UserActivateView,
    VerifiedButInactiveUserListView,
    AdminVerifyEmailView,
    UserDetailView,
    DeleteUnverifiedUserView,
    DeleteVerifiedInactiveUserView
)
from .activity_views import UserActivityListView
from .token_views import CustomTokenRefreshView

__all__ = [
    'RegisterView',
    'LoginView',
    'LogoutView',
    'ChangePasswordView',
    'AdminChangePasswordView',
    'UnverifiedUserListView',
    'UserActivateView',
    'VerifiedButInactiveUserListView',
    'AdminVerifyEmailView',
    'UserActivityListView',
    'UserDetailView',
    'CustomTokenRefreshView',
    'DeleteUnverifiedUserView',
    'DeleteVerifiedInactiveUserView'
] 
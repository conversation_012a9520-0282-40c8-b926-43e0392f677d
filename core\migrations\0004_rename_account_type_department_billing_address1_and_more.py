# Generated by Django 5.2 on 2025-04-15 16:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_historicalmember_membership_active_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='department',
            old_name='account_type',
            new_name='billing_address1',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='address_2',
            new_name='billing_address2',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='billing_address',
            new_name='billing_city',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='city',
            new_name='billing_county',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='state',
            new_name='billing_state',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='zip_code',
            new_name='billing_zip_code',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='county',
            new_name='department_address1',
        ),
        migrations.RenameField(
            model_name='department',
            old_name='department_address',
            new_name='department_address2',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='account_type',
            new_name='billing_address1',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='address_2',
            new_name='billing_address2',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='billing_address',
            new_name='billing_city',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='city',
            new_name='billing_county',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='state',
            new_name='billing_state',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='zip_code',
            new_name='billing_zip_code',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='county',
            new_name='department_address1',
        ),
        migrations.RenameField(
            model_name='historicaldepartment',
            old_name='department_address',
            new_name='department_address2',
        ),
        migrations.AddField(
            model_name='department',
            name='department_city',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='department',
            name='department_county',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='department',
            name='department_state',
            field=models.CharField(default='MS', max_length=2),
        ),
        migrations.AddField(
            model_name='department',
            name='department_zip_code',
            field=models.CharField(blank=True, default='', max_length=10),
        ),
        migrations.AddField(
            model_name='historicaldepartment',
            name='department_city',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='historicaldepartment',
            name='department_county',
            field=models.CharField(blank=True, default='', max_length=255),
        ),
        migrations.AddField(
            model_name='historicaldepartment',
            name='department_state',
            field=models.CharField(default='MS', max_length=2),
        ),
        migrations.AddField(
            model_name='historicaldepartment',
            name='department_zip_code',
            field=models.CharField(blank=True, default='', max_length=10),
        ),
    ]

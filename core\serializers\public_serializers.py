from rest_framework import serializers
from core.models import Member

class MemberPublicSerializer(serializers.ModelSerializer):
    """
    Serializer for public member listing view
    Only shows name, department, and city fields
    """
    department_name = serializers.CharField(source='department.name', read_only=True)
    
    class Meta:
        model = Member
        fields = ['id', 'name', 'department', 'department_name', 'city']
        read_only_fields = fields 
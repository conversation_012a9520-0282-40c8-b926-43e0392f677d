"""
Tests for URL configurations in the core app.
"""
from unittest import mock

from django.test import TestCase, override_settings
from django.urls import reverse, resolve, NoReverseMatch, URLPattern, URLResolver
from django.conf import settings

from core.urls import urlpatterns
from core.urls.auth import auth_urlpatterns
from core.urls.admin import admin_urlpatterns
from core.urls.public import public_urlpatterns
from core.urls.events import event_urlpatterns

# Import the views to check resolution correctly
from core.views.auth import (
    RegisterView, LoginView, LogoutView, ChangePasswordView, AdminChangePasswordView,
    UnverifiedUserListView, UserActivateView, VerifiedButInactiveUserListView,
    AdminVerifyEmailView, UserActivityListView
)
from core.views import (
    EmailVerificationView, ResendVerificationEmailView, RequestPasswordResetView,
    ValidatePasswordResetTokenView, PasswordResetConfirmView,
    MembershipRosterCreateAdminView, MembershipRosterListAdminView,
    MembershipRosterDetailAdminView, MembershipRosterUpdateAdminView,
    MembershipRosterDeleteAdminView, MembershipClassTypesAPIView,
    MergeMembersView, PrintLabelsView, MembershipRosterExportAdminView,
    DepartmentCreateAPIView, DepartmentListView, DepartmentDetailAPIView,
    DepartmentUpdateAPIView, DepartmentDeleteAPIView, DepartmentMembersAPIView,
    MembershipRosterPublicListView, MembershipRosterPublicDetailView,
    DepartmentPublicListView, DepartmentPublicDetailView, DepartmentPublicMembersAPIView,
    EventListCreateView, EventDetailView, EventInfoView,
    EventRegistrationListView, EventRegistrationDetailView, EventRegistrationByEventView,
    RegisterForEventView
)


class URLConfigTests(TestCase):
    """Test cases for URL configuration."""

    def test_auth_urls_registered(self):
        """Test that auth URLs are properly registered."""
        # Check that auth endpoints are included in main urlpatterns
        self.assertTrue(any('auth' in str(pattern.pattern) for pattern in urlpatterns))

        # Check specific endpoints
        auth_url_names = [
            'register', 'login', 'logout', 'change-password', 'request-password-reset',
            'validate-reset-token', 'password-reset-confirm'
        ]

        for name in auth_url_names:
            with self.subTest(name=name):
                try:
                    # Add parameters for URLs that need them
                    kwargs = {}
                    if name == 'validate-password-reset-token':
                        kwargs = {'reset_key': 'test-key'}

                    url = reverse(f'core:{name}', kwargs=kwargs)
                    self.assertTrue(url.startswith('/api/'))
                except NoReverseMatch:
                    self.fail(f"URL pattern for 'core:{name}' not found")

    def test_admin_urls_registered(self):
        """Test that admin URLs are properly registered."""
        # Check that admin endpoints are included in main urlpatterns
        self.assertTrue(any('admin' in str(pattern.pattern) for pattern in urlpatterns))

        # Check specific endpoints
        admin_url_names = [
            'admin-member-list', 'admin-member-export', 'admin-member-merge',
            'admin-member-print-labels', 'admin-department-list'
        ]

        for name in admin_url_names:
            with self.subTest(name=name):
                try:
                    url = reverse(f'core:{name}')
                    self.assertTrue(url.startswith('/api/'))
                except NoReverseMatch:
                    self.fail(f"URL pattern for 'core:{name}' not found")

        # Test department detail separately as it requires a pk parameter
        try:
            url = reverse('core:admin-department-detail', kwargs={'pk': 1})
            self.assertTrue(url.startswith('/api/'))
        except NoReverseMatch:
            self.fail("URL pattern for 'core:admin-department-detail' not found")

    def test_public_urls_registered(self):
        """Test that public URLs are properly registered."""
        # Check that public endpoints are included in main urlpatterns
        self.assertTrue(any('public' in str(pattern.pattern) for pattern in urlpatterns))

        # Check specific endpoints
        public_url_names = [
            'public-member-list', 'public-department-list'
        ]

        for name in public_url_names:
            with self.subTest(name=name):
                try:
                    url = reverse(f'core:{name}')
                    self.assertTrue(url.startswith('/api/'))
                except NoReverseMatch:
                    self.fail(f"URL pattern for 'core:{name}' not found")

    def test_events_urls_registered(self):
        """Test that event URLs are properly registered."""
        # Check that event endpoints are included in main urlpatterns
        self.assertTrue(any('events' in str(pattern.pattern) for pattern in urlpatterns))

        # Check specific endpoints
        event_url_names = [
            'events-list-create', 'events-detail', 'register-for-event'
        ]

        for name in event_url_names:
            with self.subTest(name=name):
                try:
                    # Add event_id parameter for URLs that need it
                    kwargs = {}
                    if name in ['events-detail', 'register-for-event']:
                        kwargs = {'event_id': 1}

                    url = reverse(f'core:{name}', kwargs=kwargs)
                    self.assertTrue(url.startswith('/api/'))
                except NoReverseMatch:
                    self.fail(f"URL pattern for 'core:{name}' not found")

    def test_url_patterns_resolve_to_correct_views(self):
        """Test that URL patterns resolve to the correct views."""
        # Test auth URLs
        self.assertEqual(resolve(reverse('core:register')).func.view_class, RegisterView)
        self.assertEqual(resolve(reverse('core:login')).func.view_class, LoginView)
        self.assertEqual(resolve(reverse('core:logout')).func.view_class, LogoutView)
        self.assertEqual(resolve(reverse('core:change-password')).func.view_class, ChangePasswordView)

        # Test admin URLs
        self.assertEqual(resolve(reverse('core:admin-member-list')).func.view_class, MembershipRosterListAdminView)
        self.assertEqual(resolve(reverse('core:admin-member-merge')).func.view_class, MergeMembersView)
        self.assertEqual(resolve(reverse('core:admin-member-print-labels')).func.view_class, PrintLabelsView)

        # Test event URLs
        self.assertEqual(resolve(reverse('core:events-list-create')).func.view_class, EventListCreateView)
        self.assertEqual(resolve(reverse('core:events-detail', kwargs={'event_id': 1})).func.view_class, EventDetailView)

    def test_url_names_are_unique(self):
        """Test that URL names are unique across all URL patterns."""
        # Collect all URL names
        url_names = []

        def get_url_names(patterns):
            for pattern in patterns:
                if hasattr(pattern, 'name') and pattern.name:
                    url_names.append(pattern.name)
                if hasattr(pattern, 'url_patterns'):
                    get_url_names(pattern.url_patterns)

        # Get names from all URL patterns
        get_url_names(auth_urlpatterns)
        get_url_names(admin_urlpatterns)
        get_url_names(public_urlpatterns)
        get_url_names(event_urlpatterns)

        # Check for duplicates
        duplicates = set([name for name in url_names if url_names.count(name) > 1])
        self.assertEqual(duplicates, set(), f"Duplicate URL names found: {duplicates}")

    def test_url_pattern_formatting(self):
        """Test that URL patterns follow standard formatting."""
        # Ensure all main patterns have app_name set
        for pattern in urlpatterns:
            if isinstance(pattern, URLResolver):
                self.assertTrue(hasattr(pattern, 'app_name'), f"Pattern {pattern} missing app_name")

        # Check that all URL patterns have trailing slashes
        def check_trailing_slash(patterns):
            for pattern in patterns:
                if hasattr(pattern, 'pattern'):
                    pattern_str = str(pattern.pattern)
                    # Skip empty patterns (root paths)
                    if not pattern_str:
                        continue
                    # Exclude patterns with parameters at the end or format suffixes
                    if not pattern_str.endswith('/') and not pattern_str.endswith('>'):
                        self.fail(f"URL pattern {pattern_str} doesn't end with a trailing slash")
                if hasattr(pattern, 'url_patterns'):
                    check_trailing_slash(pattern.url_patterns)

        # Check URL patterns
        check_trailing_slash(auth_urlpatterns)
        check_trailing_slash(admin_urlpatterns)
        check_trailing_slash(public_urlpatterns)
        check_trailing_slash(event_urlpatterns)
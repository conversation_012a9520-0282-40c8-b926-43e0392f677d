"""
Tests for the password reset confirmation view in the common app.

This module contains comprehensive tests for the PasswordResetConfirmView class.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch

from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status

from common.models import PasswordReset

User = get_user_model()


class PasswordResetConfirmViewTests(TestCase):
    """Test cases for the PasswordResetConfirmView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()
        
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )
        
        # URL for confirming password reset
        self.url = reverse('core:reset-password')
        
        # Valid data for password reset
        self.valid_data = {
            'token': str(self.reset.key),
            'new_password': 'NewPassword123',
            'confirm_password': 'NewPassword123'
        }

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_success(self, mock_verify_token):
        """Test successful password reset confirmation."""
        # Mock the verify_password_reset_token function
        mock_verify_token.return_value = (self.user, None)
        
        # Make the request
        response = self.client.post(self.url, self.valid_data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('been reset successfully', response.data['message'])
        
        # Check that verify_password_reset_token was called once
        self.assertEqual(mock_verify_token.call_count, 1)
        
        # Check that the password was actually changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewPassword123'))
        
        # Check that the reset was marked as used
        self.reset.refresh_from_db()
        self.assertTrue(self.reset.used)

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_invalid_token(self, mock_verify_token):
        """Test password reset confirmation with invalid token."""
        # Mock the verify_password_reset_token function to return an error
        mock_verify_token.return_value = (None, "Invalid password reset link")
        
        # Make the request
        response = self.client.post(self.url, self.valid_data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Invalid token", response.data['message'])
        
        # Check that verify_password_reset_token was called once
        self.assertEqual(mock_verify_token.call_count, 1)
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))
        
        # Check that the reset was not marked as used
        self.reset.refresh_from_db()
        self.assertFalse(self.reset.used)

    def test_password_reset_confirm_missing_token(self):
        """Test password reset confirmation with missing token."""
        # Make the request without a token
        data = {
            'new_password': 'NewPassword123',
            'confirm_password': 'NewPassword123'
        }
        response = self.client.post(self.url, data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid token', response.data['message'])
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))

    def test_password_reset_confirm_missing_password(self):
        """Test password reset confirmation with missing password."""
        # Make the request without a password
        data = {
            'token': str(self.reset.key),
            'confirm_password': 'NewPassword123'
        }
        response = self.client.post(self.url, data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('New password is required', response.data['message'])
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))

    def test_password_reset_confirm_missing_confirm_password(self):
        """Test password reset confirmation with missing confirm password."""
        # Make the request without a confirm password
        data = {
            'token': str(self.reset.key),
            'new_password': 'NewPassword123'
        }
        response = self.client.post(self.url, data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Confirm password is required', response.data['message'])
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))

    def test_password_reset_confirm_passwords_dont_match(self):
        """Test password reset confirmation with passwords that don't match."""
        # Make the request with mismatched passwords
        data = {
            'token': str(self.reset.key),
            'new_password': 'NewPassword123',
            'confirm_password': 'DifferentPassword123'
        }
        response = self.client.post(self.url, data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn("Password fields didn't match", response.data['message'])
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))

    def test_password_reset_confirm_password_too_short(self):
        """Test password reset confirmation with a password that's too short."""
        # Make the request with a short password
        data = {
            'token': str(self.reset.key),
            'new_password': 'short',
            'confirm_password': 'short'
        }
        response = self.client.post(self.url, data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Password reset failed', response.data['message'])
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_expired_token(self, mock_verify_token):
        """Test password reset confirmation with expired token."""
        # Mock the verify_password_reset_token function to return an expired error
        mock_verify_token.return_value = (None, "Password reset link has expired")
        
        # Make the request
        response = self.client.post(self.url, self.valid_data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid token', response.data['message'])
        
        # Check that verify_password_reset_token was called once
        self.assertEqual(mock_verify_token.call_count, 1)
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_already_used_token(self, mock_verify_token):
        """Test password reset confirmation with already used token."""
        # Mock the verify_password_reset_token function to return an already used error
        mock_verify_token.return_value = (None, "This password reset link has already been used")
        
        # Make the request
        response = self.client.post(self.url, self.valid_data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid token', response.data['message'])
        
        # Check that verify_password_reset_token was called once
        self.assertEqual(mock_verify_token.call_count, 1)
        
        # Check that the password was not changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('testpassword123'))
        
        # Check that the reset was not marked as used
        self.reset.refresh_from_db()
        self.assertFalse(self.reset.used)

    def test_password_reset_confirm_method_not_allowed(self):
        """Test that only POST method is allowed for password reset confirmation."""
        # Make a GET request
        response = self.client.get(self.url)
        
        # Check the response - should return 405 Method Not Allowed
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
    
    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_with_deleted_user(self, mock_verify_token):
        """Test password reset confirmation when the user has been deleted."""
        # Create a temporary user
        temp_user = User.objects.create_user(
            email='<EMAIL>',
            password='temppassword123',
            name='Temp User'
        )
        
        # Delete the user to simulate account deletion
        temp_user.delete()
        
        # Mock the verify_password_reset_token function to return an error for deleted user
        mock_verify_token.return_value = (None, "Invalid user account")
        
        # Make the request
        response = self.client.post(self.url, self.valid_data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertIn('Invalid token', response.data['message'])
        
        # Check that verify_password_reset_token was called once
        self.assertEqual(mock_verify_token.call_count, 1)

    @patch('core.serializers.auth.password_reset.verify_password_reset_token')
    def test_password_reset_confirm_with_strong_password(self, mock_verify_token):
        """Test password reset confirmation with a strong password."""
        # Mock the verify_password_reset_token function
        mock_verify_token.return_value = (self.user, None)
        
        # Test data with a strong password
        data = {
            'token': str(self.reset.key),
            'new_password': 'StrongPassword123!@#',
            'confirm_password': 'StrongPassword123!@#'
        }
        
        # Make the request
        response = self.client.post(self.url, data)
        
        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertIn('been reset successfully', response.data['message'])
        
        # Check that verify_password_reset_token was called once
        self.assertEqual(mock_verify_token.call_count, 1)
        
        # Check that the password was actually changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('StrongPassword123!@#'))
        
        # Check that the reset was marked as used
        self.reset.refresh_from_db()
        self.assertTrue(self.reset.used)

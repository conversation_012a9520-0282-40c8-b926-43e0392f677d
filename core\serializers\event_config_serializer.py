from rest_framework import serializers
from core.models.event_config import EventConfig
from django.db import transaction


class EventConfigSerializer(serializers.ModelSerializer):
    """Serializer for the EventConfig model."""
    
    class Meta:
        model = EventConfig
        fields = [
            'id', 
            'name', 
            'description', 
            'registration_fee_normal', 
            'registration_fee_late', 
            'guest_fee',
            'default_max_participants',
            'days_until_late_registration',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_name(self, value):
        """Validate the name field."""
        if not value or not value.strip():
            raise serializers.ValidationError("Name cannot be empty")
        
        # Check for uniqueness on create
        if self.instance is None:  # Creating new object
            if EventConfig.objects.filter(name=value).exists():
                raise serializers.ValidationError(f"Configuration with name '{value}' already exists")
        # Check for uniqueness on update, excluding the current instance
        else:
            if EventConfig.objects.filter(name=value).exclude(pk=self.instance.pk).exists():
                raise serializers.ValidationError(f"Configuration with name '{value}' already exists")
        
        return value
    
    def validate_registration_fee_normal(self, value):
        """Validate the registration_fee_normal field."""
        if value is not None and value < 0:
            raise serializers.ValidationError("Registration fee cannot be negative")
        return value
    
    def validate_registration_fee_late(self, value):
        """Validate the registration_fee_late field."""
        if value is not None and value < 0:
            raise serializers.ValidationError("Late registration fee cannot be negative")
        return value
    
    def validate_guest_fee(self, value):
        """Validate the guest_fee field."""
        if value is not None and value < 0:
            raise serializers.ValidationError("Guest fee cannot be negative")
        return value
    
    def validate_default_max_participants(self, value):
        """Validate the default_max_participants field."""
        if value is not None and value < 1:
            raise serializers.ValidationError("Maximum participants must be at least 1")
        return value
    
    def validate_days_until_late_registration(self, value):
        """Validate the days_until_late_registration field."""
        if value is not None and value < 0:
            raise serializers.ValidationError("Days until late registration cannot be negative")
        return value
    
    def validate(self, data):
        """Validate the data as a whole."""
        # Ensure late registration fee is higher than normal fee
        if ('registration_fee_normal' in data and 'registration_fee_late' in data and
                data['registration_fee_normal'] is not None and 
                data['registration_fee_late'] is not None and
                data['registration_fee_late'] < data['registration_fee_normal']):
            raise serializers.ValidationError({
                "registration_fee_late": "Late registration fee should be higher than or equal to normal registration fee"
            })
        
        return data
    
    @transaction.atomic
    def create(self, validated_data):
        """Create a new EventConfig, handling the active flag."""
        # If this config is marked as active, we'll handle that in the model save method
        return EventConfig.objects.create(**validated_data)
    
    @transaction.atomic
    def update(self, instance, validated_data):
        """Update an EventConfig, handling the active flag."""
        # Update all fields
        for field, value in validated_data.items():
            setattr(instance, field, value)
        
        # Save, which will handle making this the only active config if is_active=True
        instance.save()
        return instance


class EventConfigDetailSerializer(EventConfigSerializer):
    """Extended serializer for detailed EventConfig information."""
    
    events_count = serializers.SerializerMethodField()
    
    class Meta(EventConfigSerializer.Meta):
        fields = EventConfigSerializer.Meta.fields + ['events_count']
    
    def get_events_count(self, obj):
        """Get the number of events using this configuration."""
        return obj.events.count() 
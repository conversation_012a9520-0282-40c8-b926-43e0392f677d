"""
Views for invoice due date management.
"""
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from common.views import BaseAPIView
from payments.models.invoice_due_date import InvoiceDueDate
from payments.serializers.invoice_due_date import InvoiceDueDateSerializer


class InvoiceDueDateView(BaseAPIView):
    """View for managing invoice due dates - single endpoint for both create and update"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get the current invoice due date (first entry in DB)"""
        due_date = InvoiceDueDate.objects.first()
        if due_date:
            serializer = InvoiceDueDateSerializer(due_date)
            return self.success_response(serializer.data)
        return self.success_response({"message": "No due date has been set yet"})
    
    def post(self, request, *args, **kwargs):
        """Create or update the invoice due date"""
        serializer = InvoiceDueDateSerializer(data=request.data)
        if serializer.is_valid():
            # Check if a due date already exists
            existing_due_date = InvoiceDueDate.objects.first()
            
            if existing_due_date:
                # Update the existing due date
                updated_serializer = InvoiceDueDateSerializer(existing_due_date, data=request.data)
                if updated_serializer.is_valid():
                    due_date = updated_serializer.save()
                    return self.success_response(InvoiceDueDateSerializer(due_date).data)
                return self.error_response(updated_serializer.errors)
            else:
                # Create a new due date
                due_date = serializer.save()
                return self.success_response(InvoiceDueDateSerializer(due_date).data)
        return self.error_response(serializer.errors)

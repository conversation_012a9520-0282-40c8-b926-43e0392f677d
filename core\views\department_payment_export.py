from django.http import HttpResponse
from rest_framework import status
from django_filters import rest_framework as filters
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
from io import BytesIO
from django.db.models import Q

from common.utils import track_activity
from common.views import BaseAPIView, APIResponse
from common.permissions import IsStaffUser
from common.filters.payment_filters import DynamicFieldsPaymentFilter
from core.models import Department
from payments.models.payment import Payment


class DepartmentPaymentExportView(BaseAPIView):
    """
    Export department payments to Excel with filtering and no pagination (admin only)
    """
    permission_classes = [IsStaffUser]
    filter_backends = (filters.DjangoFilterBackend,)
    pagination_class = None
    filterset_class = DynamicFieldsPaymentFilter

    def get_queryset(self):
        """Get the queryset for payments related to a department"""
        if not self.kwargs.get('department_id'):
            return Payment.objects.none()

        department_id = self.kwargs.get('department_id')
        
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Payment.objects.none()
            
        # Get all payments where a member from this department is either the payer or a covered member
        queryset = Payment.objects.filter(
            Q(payer__department=department) | Q(covered_members__department=department)
        ).select_related(
            'payer', 'payer__department', 'event_registration'
        ).prefetch_related(
            'covered_members', 'covered_members__department'
        ).distinct().order_by('-paid_year', '-date')
        
        # Check for ids_list parameter
        ids_list = self.request.query_params.get('ids_list', None)
        if ids_list:
            # Split the comma-separated list and convert to integers
            try:
                ids = [int(id.strip()) for id in ids_list.split(',') if id.strip()]
                queryset = queryset.filter(id__in=ids)
            except ValueError:
                # If conversion fails, return empty queryset
                return Payment.objects.none()
                
        return queryset

    def filter_queryset(self, queryset):
        """Apply filters to the queryset"""
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    @track_activity(description="Exported department payments list")
    def get(self, request, *args, **kwargs):
        """
        Export department payments to Excel with filters applied and no pagination
        
        Optional query parameters:
        - ids_list: Comma-separated list of payment IDs to include in the export
        - All standard payment filters are supported
        """
        # Get filtered queryset
        queryset = self.filter_queryset(self.get_queryset())
        
        try:
            # Get department for the filename
            department_id = self.kwargs.get('department_id')
            try:
                department = Department.objects.get(pk=department_id)
                department_name = department.name.lower().replace(' ', '_')
            except Department.DoesNotExist:
                department_name = "department"
            
            # Create a new workbook and select the active worksheet
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "Department Payments"

            # Define column headers
            headers = [
                'ID', 'Invoice Number', 'PO Number', 'Amount', 'Payer', 'Payer Email',
                'Department', 'Payment Status', 'Payment Type', 'Payment For',
                'Paid Year', 'Payment Date', 'Due Date', 'Created Date',
                'Number of Covered Members', 'Draft', 'Notes'
            ]

            # Add headers to the worksheet
            for col_num, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.value = header
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
                cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")

            # Add data to the worksheet
            for row_num, payment in enumerate(queryset, 2):
                # Get covered members count
                covered_members_count = payment.covered_members.count()
                
                # ID
                worksheet.cell(row=row_num, column=1).value = payment.id
                # Invoice Number
                worksheet.cell(row=row_num, column=2).value = payment.invoice_number or ""
                # PO Number
                worksheet.cell(row=row_num, column=3).value = payment.po_number or ""
                # Amount
                worksheet.cell(row=row_num, column=4).value = float(payment.amount) if payment.amount else 0
                # Payer
                worksheet.cell(row=row_num, column=5).value = payment.payer.name if payment.payer else ""
                # Payer Email
                worksheet.cell(row=row_num, column=6).value = payment.payer.email if payment.payer else ""
                # Department
                worksheet.cell(row=row_num, column=7).value = payment.payer.department.name if payment.payer and payment.payer.department else ""
                # Payment Status
                worksheet.cell(row=row_num, column=8).value = payment.get_status_display() if hasattr(payment, 'get_status_display') else payment.status
                # Payment Type
                worksheet.cell(row=row_num, column=9).value = payment.get_payment_type_display() if hasattr(payment, 'get_payment_type_display') else payment.payment_type
                # Payment For
                worksheet.cell(row=row_num, column=10).value = payment.get_payment_for_display() if hasattr(payment, 'get_payment_for_display') else payment.payment_for
                # Paid Year
                worksheet.cell(row=row_num, column=11).value = payment.paid_year
                # Payment Date
                worksheet.cell(row=row_num, column=12).value = payment.payment_date.strftime('%Y-%m-%d') if payment.payment_date else ""
                # Due Date
                worksheet.cell(row=row_num, column=13).value = payment.due_date.strftime('%Y-%m-%d') if payment.due_date else ""
                # Created Date
                worksheet.cell(row=row_num, column=14).value = payment.date.strftime('%Y-%m-%d %H:%M:%S') if payment.date else ""
                # Number of Covered Members
                worksheet.cell(row=row_num, column=15).value = covered_members_count
                # Draft
                worksheet.cell(row=row_num, column=16).value = "Yes" if payment.draft else "No"
                # Notes
                worksheet.cell(row=row_num, column=17).value = payment.notes or ""

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = (max_length + 2) if max_length < 50 else 50
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Create a BytesIO object to save the workbook to
            excel_file = BytesIO()
            workbook.save(excel_file)
            excel_file.seek(0)

            # Create the HttpResponse with the Excel file
            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            # Set headers to force download
            response['Content-Disposition'] = f'attachment; filename={department_name}_payments_export.xlsx'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition'
            # Disable caching to ensure fresh download each time
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'

            return response
            
        except Exception as e:
            return APIResponse(
                message=f"Failed to generate export: {str(e)}",
                success=False,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 
"""
Tests for the core app serializers.
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedel<PERSON>
from rest_framework.exceptions import ValidationError
from rest_framework.test import APITestCase, APIRequestFactory
from django.test.client import RequestFactory

from core.models import Department, Event, Member
from core.serializers.auth import LoginSerializer, ChangePasswordSerializer, RegisterSerializer
from core.serializers.event import EventSerializer, EventDetailSerializer, EventCreateUpdateSerializer
from core.serializers.member import DepartmentsSerializer, MembershipRosterAdminSerializer
from common.models import PasswordReset

User = get_user_model()


class AuthSerializersTests(TestCase):
    """Test cases for the Auth serializers."""

    def setUp(self):
        """Set up test data."""
        # Create an active user
        self.active_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Active User',
            active=True,
            is_staff=True  # Make the user staff so they can log in
        )

        # Create email verification for the active user
        from common.models import EmailVerification
        EmailVerification.objects.create(
            user=self.active_user,
            verified=True
        )

        # Create an inactive user
        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Inactive User',
            active=False
        )

        # Valid login data
        self.valid_login_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        # Invalid login data - wrong password
        self.invalid_password_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }

        # Invalid login data - inactive user
        self.inactive_user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        # Invalid login data - non-existent user
        self.nonexistent_user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

    def test_valid_login(self):
        """Test login with valid credentials for an active user."""
        # Create a mock request
        request = APIRequestFactory().post('/auth/login/')
        request.user = self.active_user

        serializer = LoginSerializer(data=self.valid_login_data, context={'request': request})

        # Check if the serializer has validation errors and display them if it does
        if not serializer.is_valid():
            print(f"Serializer errors: {serializer.errors}")

        self.assertTrue(serializer.is_valid())

        # Check that the user is returned in validated_data
        self.assertEqual(serializer.validated_data['user'], self.active_user)

        # Check that a token is generated
        self.assertIn('access', serializer.validated_data)
        self.assertIn('refresh', serializer.validated_data)

    def test_invalid_password(self):
        """Test login with invalid password."""
        serializer = LoginSerializer(data=self.invalid_password_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_inactive_user(self):
        """Test login with inactive user."""
        serializer = LoginSerializer(data=self.inactive_user_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_nonexistent_user(self):
        """Test login with non-existent user."""
        serializer = LoginSerializer(data=self.nonexistent_user_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_change_password_validation(self):
        """Test password validation in ChangePasswordSerializer."""
        # Valid password change data
        strong_password = "StrongNewP@ssw0rd!"
        valid_data = {
            'old_password': 'securepassword123',
            'new_password': strong_password,
            'confirm_password': strong_password
        }

        # Create a mock request with the user
        request = APIRequestFactory().get('/')
        request.user = self.active_user

        serializer = ChangePasswordSerializer(data=valid_data, context={'request': request})
        self.assertTrue(serializer.is_valid())

        # Invalid old password
        invalid_old_password = {
            'old_password': 'wrongpassword',
            'new_password': strong_password,
            'confirm_password': strong_password
        }

        serializer = ChangePasswordSerializer(data=invalid_old_password, context={'request': request})
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

        # Password mismatch
        password_mismatch = {
            'old_password': 'securepassword123',
            'new_password': strong_password,
            'confirm_password': 'differentpassword123'
        }

        serializer = ChangePasswordSerializer(data=password_mismatch, context={'request': request})
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

        # Too short password
        short_password = {
            'old_password': 'securepassword123',
            'new_password': 'short',
            'confirm_password': 'short'
        }

        serializer = ChangePasswordSerializer(data=short_password, context={'request': request})
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)


class PasswordResetSerializersTests(TestCase):
    """Test cases for the Password Reset serializers."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Reset Test User',
            active=True
        )

        # Create a password reset token
        self.reset = PasswordReset.objects.create(user=self.user)

        # Create an expired token
        self.expired_reset = PasswordReset.objects.create(
            user=self.user,
            expires_at=timezone.now() - timedelta(hours=1)
        )

        # Create a used token
        self.used_reset = PasswordReset.objects.create(
            user=self.user,
            used=True
        )

    def test_request_password_reset_valid_email(self):
        """Test requesting password reset with valid email."""
        from core.serializers.auth import RequestPasswordResetSerializer

        data = {'email': '<EMAIL>'}
        serializer = RequestPasswordResetSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['email'], '<EMAIL>')

    def test_request_password_reset_invalid_email(self):
        """Test requesting password reset with invalid email."""
        from core.serializers.auth import RequestPasswordResetSerializer

        data = {'email': '<EMAIL>'}
        serializer = RequestPasswordResetSerializer(data=data)
        self.assertTrue(serializer.is_valid())  # Should be valid even if email doesn't exist
        self.assertEqual(serializer.validated_data['email'], '<EMAIL>')

    def test_password_reset_confirm_valid_token(self):
        """Test password reset confirmation with valid token."""
        from core.serializers.auth import PasswordResetConfirmSerializer
        strong_password = "StrongNewP@ssw0rd!"

        data = {
            'token': str(self.reset.key),
            'new_password': strong_password,
            'confirm_password': strong_password
        }
        serializer = PasswordResetConfirmSerializer(data=data)
        # Adding a print for debugging in case it still fails
        if not serializer.is_valid():
            print(f"test_password_reset_confirm_valid_token errors: {serializer.errors}")
        self.assertTrue(serializer.is_valid())

    def test_password_reset_confirm_expired_token(self):
        """Test password reset confirmation with expired token."""
        from core.serializers.auth import PasswordResetConfirmSerializer
        strong_password = "StrongNewP@ssw0rd!"

        data = {
            'token': str(self.expired_reset.key),
            'new_password': strong_password,
            'confirm_password': strong_password
        }
        serializer = PasswordResetConfirmSerializer(data=data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_password_reset_confirm_used_token(self):
        """Test password reset confirmation with used token."""
        from core.serializers.auth import PasswordResetConfirmSerializer
        strong_password = "StrongNewP@ssw0rd!"

        data = {
            'token': str(self.used_reset.key),
            'new_password': strong_password,
            'confirm_password': strong_password
        }
        serializer = PasswordResetConfirmSerializer(data=data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_password_reset_confirm_invalid_token(self):
        """Test password reset confirmation with invalid token."""
        from core.serializers.auth import PasswordResetConfirmSerializer
        strong_password = "StrongNewP@ssw0rd!"

        data = {
            'token': 'invalid-token',
            'new_password': strong_password,
            'confirm_password': strong_password
        }
        serializer = PasswordResetConfirmSerializer(data=data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_password_reset_confirm_password_validation(self):
        """Test password validation in password reset confirmation."""
        from core.serializers.auth import PasswordResetConfirmSerializer

        # Password too short
        short_password_data = {
            'token': str(self.reset.key),
            'new_password': 'short',
            'confirm_password': 'short'
        }
        serializer = PasswordResetConfirmSerializer(data=short_password_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

        # Password mismatch
        mismatch_data = {
            'token': str(self.reset.key),
            'new_password': 'newsecurepassword123',
            'confirm_password': 'differentpassword123'
        }
        serializer = PasswordResetConfirmSerializer(data=mismatch_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)


class RegisterSerializerTests(TestCase):
    """Test cases for the Register serializer."""

    def setUp(self):
        """Set up test data."""
        self.strong_password = "StrongNewP@ssw0rd!"
        self.valid_data = {
            'email': '<EMAIL>',
            'password': self.strong_password,
            'confirm_password': self.strong_password,
            'name': 'New User'
        }
        self.duplicate_email_data = {
            'email': '<EMAIL>',
            'password': self.strong_password,
            'confirm_password': self.strong_password,
            'name': 'Another User'
        }
        User.objects.create_user(email='<EMAIL>', password='password123', name='Existing User')

    def test_valid_registration(self):
        """Test registration with valid data."""
        serializer = RegisterSerializer(data=self.valid_data)
        # Adding a print for debugging in case it still fails
        if not serializer.is_valid():
            print(f"test_valid_registration errors: {serializer.errors}")
        self.assertTrue(serializer.is_valid())

        # Save the user and check that it was created correctly
        user = serializer.save()
        self.assertEqual(user.email, self.valid_data['email'])
        self.assertEqual(user.name, self.valid_data['name'])
        self.assertTrue(user.check_password(self.valid_data['password']))
        self.assertFalse(user.active)  # User should not be active until verified

    def test_duplicate_email(self):
        """Test registration with duplicate email."""
        serializer = RegisterSerializer(data=self.duplicate_email_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_password_mismatch(self):
        """Test registration with mismatched passwords."""
        mismatch_data = self.valid_data.copy()
        mismatch_data['confirm_password'] = 'differentpassword123'

        serializer = RegisterSerializer(data=mismatch_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_password_too_short(self):
        """Test registration with password that's too short."""
        short_password_data = self.valid_data.copy()
        short_password_data['password'] = 'short'
        short_password_data['password_confirm'] = 'short'

        serializer = RegisterSerializer(data=short_password_data)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_missing_required_fields(self):
        """Test registration with missing required fields."""
        # Missing email
        missing_email = self.valid_data.copy()
        missing_email.pop('email')

        serializer = RegisterSerializer(data=missing_email)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

        # Missing name
        missing_name = self.valid_data.copy()
        missing_name.pop('name')

        serializer = RegisterSerializer(data=missing_name)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

        # Missing password
        missing_password = self.valid_data.copy()
        missing_password.pop('password')

        serializer = RegisterSerializer(data=missing_password)
        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)


class EventSerializerTests(APITestCase):
    """Test cases for the Event serializer."""

    def setUp(self):
        """Set up test data."""
        # Create an event
        self.event = Event.objects.create(
            event_name='Test Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            event_description='Test Description',
            registration_fee_normal=100.00,
            registration_fee_late=115.00,
            guest_fee=50.00,
            max_participants=100
        )

        # Event data for creation tests
        self.event_data = {
            'event_name': 'New Test Event',
            'event_date': (timezone.now().date() + timedelta(days=20)).isoformat(),
            'event_location': 'New Test Location',
            'event_description': 'New Test Description',
            'registration_fee_normal': 120.00,
            'registration_fee_late': 135.00,
            'guest_fee': 60.00,
            'max_participants': 150
        }

    def test_event_serialization(self):
        """Test serialization of an event."""
        serializer = EventSerializer(self.event)
        data = serializer.data

        self.assertEqual(data['event_name'], self.event.event_name)
        self.assertEqual(data['event_location'], self.event.event_location)
        self.assertEqual(data['event_description'], self.event.event_description)
        self.assertEqual(float(data['registration_fee_normal']), float(self.event.registration_fee_normal))
        self.assertEqual(float(data['registration_fee_late']), float(self.event.registration_fee_late))
        self.assertEqual(float(data['guest_fee']), float(self.event.guest_fee))
        self.assertEqual(data['max_participants'], self.event.max_participants)

        # Check read-only fields
        self.assertIn('total_registrations', data)
        self.assertIn('is_late_registration', data)

    def test_event_detail_serialization(self):
        """Test detailed serialization of an event."""
        serializer = EventDetailSerializer(self.event)
        data = serializer.data

        # Check that all fields from the base serializer are included
        self.assertEqual(data['event_name'], self.event.event_name)
        self.assertEqual(data['event_location'], self.event.event_location)

        # Check any additional fields specific to the detail serializer
        # (In this case, it's the same as the base serializer, but could be extended)

    def test_event_create_serializer(self):
        """Test creating an event with the serializer."""
        serializer = EventCreateUpdateSerializer(data=self.event_data)
        self.assertTrue(serializer.is_valid())

        event = serializer.save()
        self.assertEqual(event.event_name, self.event_data['event_name'])
        self.assertEqual(event.event_location, self.event_data['event_location'])
        self.assertEqual(event.event_description, self.event_data['event_description'])
        self.assertEqual(float(event.registration_fee_normal), float(self.event_data['registration_fee_normal']))
        self.assertEqual(float(event.registration_fee_late), float(self.event_data['registration_fee_late']))
        self.assertEqual(float(event.guest_fee), float(self.event_data['guest_fee']))
        self.assertEqual(event.max_participants, self.event_data['max_participants'])

    def test_event_update_serializer(self):
        """Test updating an event with the serializer."""
        update_data = {
            'event_name': 'Updated Event Name',
            'event_description': 'Updated Description'
        }

        serializer = EventCreateUpdateSerializer(self.event, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())

        updated_event = serializer.save()
        self.assertEqual(updated_event.event_name, update_data['event_name'])
        self.assertEqual(updated_event.event_description, update_data['event_description'])

        # Check that other fields remain unchanged
        self.assertEqual(updated_event.event_location, self.event.event_location)
        self.assertEqual(updated_event.registration_fee_normal, self.event.registration_fee_normal)


class MemberSerializerTests(APITestCase):
    """Test cases for the Member serializer."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Test Department',
            department_address1='123 Main St',
            department_city='Anytown',
            department_state='MS',
            department_zip_code='12345'
        )

        # Create a member
        self.member = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Test Member',
            department=self.department,
            membership_class=Member.MembershipStatus.MEMBER,
            role=Member.Role.VOLUNTEER,
            gender=Member.Gender.PREFER_NOT_TO_SAY,
            active=True
        )

        # Member data for creation tests
        self.member_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'name': 'New Test Member',
            'department': self.department.id,
            'membership_class': Member.MembershipStatus.ASSOCIATE_MEMBER,
            'role': Member.Role.CAREER,
            'gender': Member.Gender.OTHER,
            'active': True
        }

    def test_department_serialization(self):
        """Test serialization of a department."""
        serializer = DepartmentsSerializer(self.department)
        data = serializer.data

        self.assertEqual(data['name'], self.department.name)
        self.assertEqual(data['department_address1'], self.department.department_address1)
        self.assertEqual(data['department_city'], self.department.department_city)
        self.assertEqual(data['department_state'], self.department.department_state)
        self.assertEqual(data['department_zip_code'], self.department.department_zip_code)

    def test_member_serialization(self):
        """Test serialization of a member."""
        serializer = MembershipRosterAdminSerializer(self.member)
        data = serializer.data

        self.assertEqual(data['email'], self.member.email)
        self.assertEqual(data['name'], self.member.name)
        self.assertEqual(data['department'], self.department.id)
        self.assertEqual(data['membership_class'], self.member.membership_class)
        self.assertEqual(data['role'], self.member.role)
        self.assertEqual(data['gender'], self.member.gender)
        self.assertEqual(data['active'], self.member.active)

    def test_member_create_serializer(self):
        """Test creating a member with the serializer."""
        serializer = MembershipRosterAdminSerializer(data=self.member_data)
        self.assertTrue(serializer.is_valid())

        member = serializer.save()
        self.assertEqual(member.email, self.member_data['email'])
        self.assertEqual(member.name, self.member_data['name'])
        self.assertEqual(member.department.id, self.member_data['department'])
        self.assertEqual(member.membership_class, self.member_data['membership_class'])
        self.assertEqual(member.role, self.member_data['role'])
        self.assertEqual(member.gender, self.member_data['gender'])
        self.assertEqual(member.active, self.member_data['active'])

    def test_member_update_serializer(self):
        """Test updating a member with the serializer."""
        update_data = {
            'name': 'Updated Member Name',
            'membership_class': Member.MembershipStatus.HONORARY_MEMBER
        }

        serializer = MembershipRosterAdminSerializer(self.member, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())

        updated_member = serializer.save()
        self.assertEqual(updated_member.name, update_data['name'])
        self.assertEqual(updated_member.membership_class, update_data['membership_class'])

        # Check that other fields remain unchanged
        self.assertEqual(updated_member.email, self.member.email)
        self.assertEqual(updated_member.department, self.member.department)

    def test_invalid_role_value(self):
        """Test that invalid role values are rejected."""
        invalid_data = self.member_data.copy()
        invalid_data['role'] = 'invalid_role'

        serializer = MembershipRosterAdminSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('role', serializer.errors)

    def test_invalid_membership_class(self):
        """Test that invalid membership class values are rejected."""
        invalid_data = self.member_data.copy()
        invalid_data['membership_class'] = 'invalid_class'

        serializer = MembershipRosterAdminSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('membership_class', serializer.errors)

    def test_invalid_gender(self):
        """Test that invalid gender values are rejected."""
        invalid_data = self.member_data.copy()
        invalid_data['gender'] = 'invalid_gender'

        serializer = MembershipRosterAdminSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('gender', serializer.errors)


class LoginSerializerTests(APITestCase):
    def setUp(self):
        # Create a user with verified email
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='password123',
            name='Test User',
            is_staff=True,  # Make the user staff so they can log in
            active=True
        )

        # Create email verification for the user
        from common.models import EmailVerification
        EmailVerification.objects.create(
            user=self.user,
            verified=True
        )

        self.valid_data = {'email': '<EMAIL>', 'password': 'password123'}
        self.factory = RequestFactory()
        self.request = self.factory.post('/login/')  # Mock a request

    def test_valid_login(self):
        """
        Test the serializer with valid credentials for a verified user.
        """
        serializer = LoginSerializer(data=self.valid_data, context={'request': self.request})
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['user'], self.user)
        self.assertIn('access', serializer.validated_data)
        self.assertIn('refresh', serializer.validated_data)

    def test_invalid_password(self):
        """
        Test the serializer with an invalid password.
        """
        data = {'email': '<EMAIL>', 'password': 'wrongpassword'}
        serializer = LoginSerializer(data=data, context={'request': self.request})
        self.assertFalse(serializer.is_valid())
        self.assertIn('non_field_errors', serializer.errors)
        self.assertEqual(str(serializer.errors['non_field_errors'][0]), 'Invalid credentials.')

    def test_nonexistent_user(self):
        """
        Test the serializer with a non-existent email.
        """
        data = {'email': '<EMAIL>', 'password': 'password123'}
        serializer = LoginSerializer(data=data, context={'request': self.request})
        self.assertFalse(serializer.is_valid())
        self.assertIn('non_field_errors', serializer.errors)
        self.assertEqual(str(serializer.errors['non_field_errors'][0]), 'Invalid credentials.')

    def test_inactive_user(self):
        """
        Test the serializer with an inactive user.
        """
        self.user.active = False
        self.user.save()
        serializer = LoginSerializer(data=self.valid_data, context={'request': self.request})
        self.assertFalse(serializer.is_valid())
        # The error message might vary, so we're just checking that validation fails

    def test_unverified_user(self):
        """
        Test the serializer with an unverified user.
        """
        # Delete the verification record to make the user unverified
        from common.models import EmailVerification
        EmailVerification.objects.filter(user=self.user).delete()

        serializer = LoginSerializer(data=self.valid_data, context={'request': self.request})
        self.assertFalse(serializer.is_valid())
        self.assertIn('non_field_errors', serializer.errors)
        self.assertEqual(str(serializer.errors['non_field_errors'][0]), 'Email has not been verified. Please check your email for the verification link or request a new one.')

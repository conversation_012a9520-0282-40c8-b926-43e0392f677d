"""
Serializers for password reset functionality.
"""
from typing import Dict, Any
import uuid

from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from rest_framework import serializers

from common.utils.email import send_password_reset_email, verify_password_reset_token
from common.models import PasswordReset

Member = get_user_model()


class RequestPasswordResetSerializer(serializers.Serializer):
    """
    Serializer for requesting a password reset
    """
    email = serializers.EmailField(required=True)

    def validate_email(self, value: str) -> str:
        """
        Validate email exists and user is active
        """
        try:
            user = Member.objects.get(email=value)

            # Check if user is active
            if not user.active:
                # Don't reveal account status for security reasons
                pass

            return value
        except Member.DoesNotExist:
            # Don't reveal if email exists or not for security reasons
            return value

    def save(self, **kwargs) -> None:
        """
        Send password reset email if user exists and is active
        """
        email = self.validated_data['email']

        try:
            # Use case-insensitive lookup
            user = Member.objects.get(email__iexact=email)

            # Only send reset email if user is active and verified
            if user.active:
                send_password_reset_email(user)
        except Member.DoesNotExist:
            # Do nothing if user doesn't exist (security best practice)
            pass


class ValidateResetTokenSerializer(serializers.Serializer):
    """
    Serializer for validating a password reset token
    """
    token = serializers.UUIDField(required=True, error_messages={
        'invalid': 'Must be a valid UUID.'
    })

    def validate_token(self, value) -> uuid.UUID:
        """
        Validate reset token is valid and not expired
        """
        from common.utils.email import verify_password_reset_token

        # Convert value to string if needed
        token_value = str(value) if value else value

        user, error = verify_password_reset_token(token_value)

        if error:
            raise serializers.ValidationError(error)

        # Store user in the serializer context
        self.context['user'] = user

        return value

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Add user email to the data if available
        if 'user' in self.context:
            data['email'] = self.context['user'].email

        return data


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for confirming a password reset
    """
    token = serializers.UUIDField(required=True)
    new_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'},
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        required=True,
        write_only=True,
        style={'input_type': 'password'}
    )

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate passwords match and token is valid
        """
        # Check password match
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError({
                "confirm_password": "Password fields didn't match."
            })

        # Validate token
        user, error = verify_password_reset_token(attrs['token'])

        if error:
            raise serializers.ValidationError({
                "token": error
            })

        # Store user for later use
        self.user = user

        return attrs

    def save(self, **kwargs) -> Member:
        """
        Reset the user's password and mark the token as used
        """
        # Set the new password
        self.user.set_password(self.validated_data['new_password'])
        self.user.save()

        # Mark the token as used
        try:
            reset_record = PasswordReset.objects.get(key=self.validated_data['token'])
            reset_record.used = True
            reset_record.save()
        except PasswordReset.DoesNotExist:
            pass

        return self.user
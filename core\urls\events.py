from django.urls import path

# Import event views
from core.views import (
    EventListCreateView,
    EventDetailView,
    EventInfoView,
    EventRegistrationListView,
    EventRegistrationDetailView,
    EventRegistrationByEventView,
    RegisterForEventView,
)

# Import event config views from event_config_views
from core.views.events.event_config_views import (
    EventConfigListCreateView,
    EventConfigDetailView,
    EventConfigActiveView
)

# Event URLs
event_urlpatterns = [
    # Event URLs
    path('', EventListCreateView.as_view(), name='events-list-create'),
    path('<int:event_id>/', EventDetailView.as_view(), name='events-detail'),
    path('info/', EventInfoView.as_view(), name='events-info'),
    
    # Event Registration URLs
    path('registrations/', EventRegistrationListView.as_view(), name='event-registrations-list'),
    path('registrations/<int:registration_id>/', EventRegistrationDetailView.as_view(), name='event-registrations-detail'),
    path('registrations/event/<int:event_id>/', EventRegistrationByEventView.as_view(), name='event-registrations-by-event'),
    path('register/<int:event_id>/', RegisterForEventView.as_view(), name='register-for-event'),
    
    # Event Config URLs
    path('configs/', EventConfigListCreateView.as_view(), name='event-configs-list-create'),
    path('configs/<int:config_id>/', EventConfigDetailView.as_view(), name='event-configs-detail'),
    path('configs/active/', EventConfigActiveView.as_view(), name='event-configs-active'),
    path('configs/set-active/<int:config_id>/', EventConfigActiveView.as_view(), name='event-configs-set-active'),
]

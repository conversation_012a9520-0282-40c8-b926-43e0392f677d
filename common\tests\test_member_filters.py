from django.test import TestCase
from django.utils import timezone
from datetime import datetime, timedelta
from core.models import Member, Department
from common.filters.member_filters import DynamicFieldsMemberFilter

class MemberFilterTests(TestCase):
    """Test cases for the DynamicFieldsMemberFilter"""
    
    def setUp(self):
        """Set up test data"""
        # Create departments
        self.dept1 = Department.objects.create(name="Engineering")
        self.dept2 = Department.objects.create(name="Marketing")
        
        # Create base date for consistent testing
        self.base_date = timezone.now().date()
        
        # Create members with various combinations
        self.member1 = Member.objects.create(
            name="<PERSON>",
            email="<EMAIL>",
            department=self.dept1,
            address="123 Main St",
            city="New York",
            dst="EST",
            title="Engineer",
            mi="A",
            st="NY",
            zip_code="10001",
            home_phone="555-0101",
            business_phone="555-0102",
            lapel_pin="Gold",
            notes="Senior member",
            orig_join_date=timezone.make_aware(datetime.now() - timedelta(days=365*5)),
            active=True,
            membership_active=True,
            is_deceased=False,
            executive_board=True,
            committee_member=True,
            new_member=False,
            lifetime=True,
            committee="Technical",
            membership_class=Member.MembershipStatus.LIFE_MEMBER,
            lead_status=Member.LeadStatus.ACTIVE,
            role=Member.Role.CAREER,
            gender=Member.Gender.MALE
        )
        
        self.member2 = Member.objects.create(
            name="Jane Doe",
            email="<EMAIL>",
            department=self.dept2,
            address="456 Oak Ave",
            city="Los Angeles",
            dst="PST",
            title="Manager",
            mi="B",
            st="CA",
            zip_code="90001",
            home_phone="555-0201",
            business_phone="555-0202",
            lapel_pin="Silver",
            notes="Committee chair",
            orig_join_date=timezone.make_aware(datetime.now() - timedelta(days=365*2)),
            active=True,
            membership_active=True,
            is_deceased=False,
            executive_board=False,
            committee_member=True,
            new_member=False,
            lifetime=False,
            committee="Marketing",
            membership_class=Member.MembershipStatus.MEMBER,
            lead_status=Member.LeadStatus.ACTIVE,
            role=Member.Role.VOLUNTEER,
            gender=Member.Gender.FEMALE
        )
        
        self.member3 = Member.objects.create(
            name="Bob Wilson",
            email="<EMAIL>",
            department=self.dept1,
            address="789 Pine St",
            city="Chicago",
            dst="CST",
            title="Associate",
            mi="C",
            st="IL",
            zip_code="60601",
            home_phone="555-0301",
            business_phone="555-0302",
            lapel_pin="Bronze",
            notes="New member",
            orig_join_date=timezone.make_aware(datetime.now() - timedelta(days=30)),
            active=True,
            membership_active=True,
            is_deceased=False,
            executive_board=False,
            committee_member=False,
            new_member=True,
            lifetime=False,
            committee="",
            membership_class=Member.MembershipStatus.ASSOCIATE_MEMBER,
            lead_status=Member.LeadStatus.PENDING,
            role=Member.Role.OTHER,
            gender=Member.Gender.OTHER
        )
        
        self.filter_class = DynamicFieldsMemberFilter
    
    def test_basic_filters(self):
        """Test basic text filters"""
        # Test name filter
        filterset = self.filter_class({'name': 'John'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test email filter
        filterset = self.filter_class({'email': 'jane'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test department filter by ID
        filterset = self.filter_class({'department': self.dept1.id}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member3})
        
        # Test department name filter
        filterset = self.filter_class({'department_name': 'Engineering'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member3})
        
        # Test address filter
        filterset = self.filter_class({'address': 'Main'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test city filter
        filterset = self.filter_class({'city': 'Chicago'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
    
    def test_additional_field_filters(self):
        """Test filters for additional displayed columns"""
        # Test dst filter
        filterset = self.filter_class({'dst': 'EST'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test title filter
        filterset = self.filter_class({'title': 'Manager'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test mi filter
        filterset = self.filter_class({'mi': 'A'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test st filter
        filterset = self.filter_class({'st': 'CA'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test zip_code filter
        filterset = self.filter_class({'zip_code': '60601'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
        
        # Test phone filters
        filterset = self.filter_class({'home_phone': '555-0101'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        filterset = self.filter_class({'business_phone': '555-0202'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test lapel_pin filter
        filterset = self.filter_class({'lapel_pin': 'Gold'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test notes filter
        filterset = self.filter_class({'notes': 'Senior'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
    
    def test_date_filters(self):
        """Test date-based filters"""
        # Test exact date
        filterset = self.filter_class({
            'orig_join_date': self.member3.orig_join_date.date()
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
        
        # Test date range
        three_years_ago = timezone.now() - timedelta(days=365*3)
        filterset = self.filter_class({
            'orig_join_date_gte': three_years_ago.date(),
            'orig_join_date_lte': timezone.now().date()
        }, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member2, self.member3})
    
    def test_boolean_filters(self):
        """Test boolean field filters"""
        # Test active filter
        filterset = self.filter_class({'active': 'yes'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test membership_active filter
        filterset = self.filter_class({'membership_active': 'yes'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test is_deceased filter
        filterset = self.filter_class({'is_deceased': 'no'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test executive_board filter
        filterset = self.filter_class({'executive_board': 'yes'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test committee_member filter
        filterset = self.filter_class({'committee_member': 'yes'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2})
        
        # Test new_member filter
        filterset = self.filter_class({'new_member': 'yes'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
        
        # Test lifetime filter
        filterset = self.filter_class({'lifetime': 'yes'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
    
    def test_membership_class_filter(self):
        """Test membership class filter"""
        # Test life member
        filterset = self.filter_class({'membership_class': Member.MembershipStatus.LIFE_MEMBER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test regular member
        filterset = self.filter_class({'membership_class': Member.MembershipStatus.MEMBER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test associate member
        filterset = self.filter_class({'membership_class': Member.MembershipStatus.ASSOCIATE_MEMBER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
    
    def test_lead_status_filter(self):
        """Test lead status filter"""
        # Test active status
        filterset = self.filter_class({'lead_status': Member.LeadStatus.ACTIVE}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2})
        
        # Test pending status
        filterset = self.filter_class({'lead_status': Member.LeadStatus.PENDING}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
    
    def test_role_filter(self):
        """Test role filter"""
        # Test career role
        filterset = self.filter_class({'role': Member.Role.CAREER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test volunteer role
        filterset = self.filter_class({'role': Member.Role.VOLUNTEER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test other role
        filterset = self.filter_class({'role': Member.Role.OTHER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
    
    def test_gender_filter(self):
        """Test gender filter"""
        # Test male gender
        filterset = self.filter_class({'gender': Member.Gender.MALE}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test female gender
        filterset = self.filter_class({'gender': Member.Gender.FEMALE}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test other gender
        filterset = self.filter_class({'gender': Member.Gender.OTHER}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
    
    def test_committee_filter(self):
        """Test committee filter"""
        # Test specific committee
        filterset = self.filter_class({'committee': 'Technical'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test partial match
        filterset = self.filter_class({'committee': 'Mark'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test no committee
        filterset = self.filter_class({'committee': ''}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
    
    def test_multiple_filters(self):
        """Test combinations of multiple filters"""
        # Test department and boolean combination
        filterset = self.filter_class({
            'department': self.dept1.id,
            'executive_board': 'yes'
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test date range and membership class
        one_year_ago = timezone.now() - timedelta(days=365)
        filterset = self.filter_class({
            'orig_join_date_gte': one_year_ago.date(),
            'membership_class': Member.MembershipStatus.ASSOCIATE_MEMBER
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
        
        # Test complex combination
        filterset = self.filter_class({
            'department_name': 'Engineering',
            'committee_member': 'yes',
            'lifetime': 'yes',
            'gender': Member.Gender.MALE,
            'role': Member.Role.CAREER
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
    
    def test_case_sensitivity(self):
        """Test case sensitivity of filters"""
        # Test name case insensitive
        filterset = self.filter_class({'name': 'JOHN'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test email case insensitive
        filterset = self.filter_class({'email': 'JANE'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Test department name case insensitive
        filterset = self.filter_class({'department_name': 'ENGINEERING'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member3})
    
    def test_whitespace_handling(self):
        """Test handling of whitespace in filter values"""
        # Test leading/trailing whitespace
        filterset = self.filter_class({'name': '  John Smith  '}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
        
        # Test internal whitespace - update to match actual note text with single space
        filterset = self.filter_class({'notes': 'Senior member'}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
    
    def test_special_characters(self):
        """Test handling of special characters"""
        # Create member with special characters
        member_special = Member.objects.create(
            name="O'Connor-Smith",
            email="<EMAIL>",
            department=self.dept1,
            address="123 Main St.",
            city="St. Louis",
            orig_join_date=timezone.make_aware(datetime.now()),
            notes="Special & unique member",
            membership_class=Member.MembershipStatus.MEMBER,
            lead_status=Member.LeadStatus.ACTIVE,
            role=Member.Role.VOLUNTEER,
            gender=Member.Gender.PREFER_NOT_TO_SAY
        )
        
        # Test name with special characters
        filterset = self.filter_class({'name': "O'Connor"}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [member_special])
        
        # Test notes with special characters
        filterset = self.filter_class({'notes': "& unique"}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [member_special])
    
    def test_empty_filters(self):
        """Test behavior with empty filter values"""
        # Test empty string
        filterset = self.filter_class({'name': ''}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test None value
        filterset = self.filter_class({'name': None}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test whitespace only
        filterset = self.filter_class({'name': '   '}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
    
    def test_invalid_values(self):
        """Test handling of invalid filter values"""
        # Test invalid date
        filterset = self.filter_class({'orig_join_date': 'not-a-date'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test invalid department ID
        filterset = self.filter_class({'department': 999}, Member.objects.all())
        self.assertEqual(filterset.qs.count(), 0)
        
        # Test invalid boolean value
        filterset = self.filter_class({'active': 'invalid'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
    
    def test_advanced_combinations(self):
        """Test more complex filter combinations"""
        # Filter by gender and role
        filterset = self.filter_class({
            'gender': Member.Gender.FEMALE,
            'role': Member.Role.VOLUNTEER
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member2])
        
        # Filter by department and lead status
        filterset = self.filter_class({
            'department': self.dept1.id,
            'lead_status': Member.LeadStatus.PENDING
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member3])
        
        # Filter with text search and boolean conditions
        filterset = self.filter_class({
            'name': 'Smith',
            'executive_board': 'yes',
            'lifetime': 'yes'
        }, Member.objects.all())
        self.assertEqual(list(filterset.qs), [self.member1])
    
    def test_date_edge_cases(self):
        """Test date filtering edge cases"""
        today = timezone.now().date()
        
        # Test filtering with today's date (should return no results)
        filterset = self.filter_class({'orig_join_date': today}, Member.objects.all())
        self.assertEqual(list(filterset.qs), [])
        
        # Test date range that spans all members - make sure we use timezone-aware dates
        earliest_date = (self.member1.orig_join_date - timedelta(days=1)).date()
        latest_date = timezone.now().date() + timedelta(days=1)
        
        filterset = self.filter_class({
            'orig_join_date_gte': earliest_date,
            'orig_join_date_lte': latest_date
        }, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3})
        
        # Test overlapping date ranges
        six_months_ago = timezone.now().date() - timedelta(days=182)
        filterset = self.filter_class({
            'orig_join_date_gte': six_months_ago
        }, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member3})
    
    def test_empty_queryset(self):
        """Test filtering on an empty queryset"""
        # Use an empty queryset directly instead of deleting members
        empty_queryset = Member.objects.filter(name='NonexistentName')
        
        # Test various filters on empty queryset
        filterset = self.filter_class({'name': 'John'}, empty_queryset)
        self.assertEqual(list(filterset.qs), [])
        
        filterset = self.filter_class({'active': 'yes'}, empty_queryset)
        self.assertEqual(list(filterset.qs), [])
        
        filterset = self.filter_class({'membership_class': Member.MembershipStatus.LIFE_MEMBER}, empty_queryset)
        self.assertEqual(list(filterset.qs), [])
    
    def test_filter_by_non_existent_fields(self):
        """Test filtering by fields that don't exist on the model"""
        # This should not raise an error, but just ignore the non-existent field
        filterset = self.filter_class({'non_existent_field': 'value'}, Member.objects.all())
        self.assertEqual(set(filterset.qs), {self.member1, self.member2, self.member3}) 
"""
Tests for the activity tracking decorator in the common app - basic functionality.

This module contains tests for the basic functionality of the track_activity decorator,
including function-based views and class-based views.
"""
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response

from common.utils.activity_tracking import track_activity
from common.models import UserActivity

User = get_user_model()


class TrackActivityDecoratorBasicTests(TestCase):
    """Test cases for the basic functionality of the track_activity decorator."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()
        
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )

    def test_track_activity_function_based_view(self):
        """Test track_activity decorator on a function-based view."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_class_based_view(self):
        """Test track_activity decorator on a class-based view method."""
        # Define a class-based view with the decorator
        class TestView:
            @track_activity("User viewed test page")
            def get(self, request):
                return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Create an instance of the view and call the method
        view = TestView()
        response = view.get(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_api_view(self):
        """Test track_activity decorator on an APIView method."""
        # Define an APIView with the decorator
        class TestAPIView(APIView):
            @track_activity("User viewed API test page")
            def get(self, request):
                return Response({"message": "Test response"})

        # Create a request with an authenticated user
        request = self.factory.get('/api/test/')
        request.user = self.user

        # Create an instance of the view and call the method
        view = TestAPIView()
        response = view.get(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed API test page")

        # Check that the response was returned correctly
        self.assertEqual(response.data, {"message": "Test response"})

    def test_track_activity_without_description(self):
        """Test track_activity decorator without a description (uses function name)."""
        # Define a function-based view with the decorator without description
        @track_activity()
        def test_view_no_description(request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view_no_description(request)

        # Check that the activity was recorded with function name as description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "test_view_no_description")

    def test_track_activity_with_post_request(self):
        """Test track_activity decorator with a POST request."""
        # Define a function-based view with the decorator
        @track_activity("User submitted form")
        def post_view(request):
            return HttpResponse("Form submitted")

        # Create a POST request with an authenticated user
        request = self.factory.post('/form/', {'field': 'value'})
        request.user = self.user

        # Call the view
        response = post_view(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User submitted form")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Form submitted")

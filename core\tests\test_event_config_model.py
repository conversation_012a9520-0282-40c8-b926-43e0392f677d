"""
Tests for the EventConfig model.
This file contains tests for the EventConfig model which manages global event configuration settings.
"""
from django.test import TestCase
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from decimal import Decimal

from core.models.event_config import EventConfig
from core.models.event import Event
from django.utils import timezone
from datetime import timedelta


class EventConfigModelTests(TestCase):
    """Test cases for the EventConfig model."""

    def setUp(self):
        """Set up test data for each test."""
        self.config_data = {
            'name': 'Test Configuration',
            'description': 'Test configuration for events',
            'registration_fee_normal': Decimal('100.00'),
            'registration_fee_late': Decimal('115.00'),
            'guest_fee': Decimal('50.00'),
            'default_max_participants': 100,
            'days_until_late_registration': 7,
            'is_active': True
        }

    def test_config_creation(self):
        """Test creating a configuration."""
        config = EventConfig.objects.create(**self.config_data)
        
        # Verify configuration was created with correct values
        self.assertEqual(config.name, self.config_data['name'])
        self.assertEqual(config.description, self.config_data['description'])
        self.assertEqual(config.registration_fee_normal, self.config_data['registration_fee_normal'])
        self.assertEqual(config.registration_fee_late, self.config_data['registration_fee_late'])
        self.assertEqual(config.guest_fee, self.config_data['guest_fee'])
        self.assertEqual(config.default_max_participants, self.config_data['default_max_participants'])
        self.assertEqual(config.days_until_late_registration, self.config_data['days_until_late_registration'])
        self.assertEqual(config.is_active, self.config_data['is_active'])
        
        # Verify auto fields
        self.assertIsNotNone(config.created_at)
        self.assertIsNotNone(config.updated_at)

    def test_config_string_representation(self):
        """Test the __str__ method returns the name and active status."""
        # Active config
        active_config = EventConfig.objects.create(**self.config_data)
        self.assertEqual(str(active_config), 'Test Configuration (Active)')
        
        # Inactive config
        inactive_data = self.config_data.copy()
        inactive_data['name'] = 'Inactive Configuration'
        inactive_data['is_active'] = False
        inactive_config = EventConfig.objects.create(**inactive_data)
        self.assertEqual(str(inactive_config), 'Inactive Configuration ')

    def test_only_one_active_config(self):
        """Test that only one configuration can be active at a time."""
        # Create first active config
        config1 = EventConfig.objects.create(**self.config_data)
        self.assertTrue(config1.is_active)
        
        # Create second active config
        config2_data = self.config_data.copy()
        config2_data['name'] = 'Second Configuration'
        config2 = EventConfig.objects.create(**config2_data)
        self.assertTrue(config2.is_active)
        
        # Refresh first config and verify it's now inactive
        config1.refresh_from_db()
        self.assertFalse(config1.is_active)
        
        # Create third active config
        config3_data = self.config_data.copy()
        config3_data['name'] = 'Third Configuration'
        config3 = EventConfig.objects.create(**config3_data)
        self.assertTrue(config3.is_active)
        
        # Refresh configs and verify only the latest is active
        config1.refresh_from_db()
        config2.refresh_from_db()
        self.assertFalse(config1.is_active)
        self.assertFalse(config2.is_active)
        self.assertTrue(config3.is_active)

    def test_get_active_config(self):
        """Test the get_active_config class method."""
        # Initial case - no configs exist
        active_config = EventConfig.get_active_config()
        self.assertIsNotNone(active_config)
        self.assertTrue(active_config.is_active)
        self.assertEqual(active_config.name, "Default Configuration")
        
        # After creating a custom active config
        custom_config = EventConfig.objects.create(**self.config_data)
        active_config = EventConfig.get_active_config()
        self.assertEqual(active_config, custom_config)
        
        # After setting active config to inactive
        custom_config.is_active = False
        custom_config.save()
        
        # Should create a new default config
        active_config = EventConfig.get_active_config()
        self.assertNotEqual(active_config, custom_config)
        self.assertTrue(active_config.is_active)

    def test_make_config_inactive(self):
        """Test making a configuration inactive."""
        # Create an active config
        config = EventConfig.objects.create(**self.config_data)
        self.assertTrue(config.is_active)
        
        # Make it inactive
        config.is_active = False
        config.save()
        
        # Verify it's inactive
        self.assertFalse(config.is_active)
        
        # Should have no active configs
        active_configs = EventConfig.objects.filter(is_active=True)
        self.assertEqual(active_configs.count(), 0)

    def test_config_used_by_event(self):
        """Test that a configuration can be used by events."""
        # Create a config
        config = EventConfig.objects.create(**self.config_data)
        
        # Create an event using this config
        event = Event.objects.create(
            event_name='Configured Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            config=config
        )
        
        # Refresh config to get related events
        config.refresh_from_db()
        
        # Verify relationship
        self.assertEqual(event.config, config)
        self.assertIn(event, config.events.all())

    def test_events_use_default_values_from_config(self):
        """Test that events use default values from the configuration."""
        # Create a config with specific values
        config = EventConfig.objects.create(**self.config_data)
        
        # Create an event with minimal fields, using this config
        event = Event.objects.create(
            event_name='Default Values Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            config=config
        )
        
        # Verify event uses config's default values
        self.assertEqual(event.registration_fee_normal, config.registration_fee_normal)
        self.assertEqual(event.registration_fee_late, config.registration_fee_late)
        self.assertEqual(event.guest_fee, config.guest_fee)
        self.assertEqual(event.max_participants, config.default_max_participants)
        
        # Verify late registration date was calculated correctly
        expected_late_date = event.event_date - timedelta(days=config.days_until_late_registration)
        self.assertEqual(event.late_registration_date, expected_late_date)

    def test_event_overrides_config_values(self):
        """Test that events can override default values from configuration."""
        # Create a config
        config = EventConfig.objects.create(**self.config_data)
        
        # Create an event with custom values, but using the config
        custom_values = {
            'event_name': 'Custom Values Event',
            'event_date': timezone.now().date() + timedelta(days=10),
            'event_location': 'Test Location',
            'registration_fee_normal': Decimal('150.00'),  # Override config value
            'registration_fee_late': Decimal('175.00'),    # Override config value
            'guest_fee': Decimal('75.00'),                # Override config value
            'max_participants': 50,                       # Override config value
            'config': config
        }
        event = Event.objects.create(**custom_values)
        
        # Verify event uses its own values, not config defaults
        self.assertEqual(event.registration_fee_normal, custom_values['registration_fee_normal'])
        self.assertEqual(event.registration_fee_late, custom_values['registration_fee_late'])
        self.assertEqual(event.guest_fee, custom_values['guest_fee'])
        self.assertEqual(event.max_participants, custom_values['max_participants'])
        
        # Verify relationship is still maintained
        self.assertEqual(event.config, config)

    def test_update_config(self):
        """Test updating a configuration."""
        # Create a config
        config = EventConfig.objects.create(**self.config_data)
        
        # Store original updated_at
        original_updated_at = config.updated_at
        
        # Update the config
        new_values = {
            'name': 'Updated Configuration',
            'description': 'Updated description',
            'registration_fee_normal': Decimal('120.00'),
            'registration_fee_late': Decimal('135.00'),
            'guest_fee': Decimal('60.00'),
            'default_max_participants': 75,
            'days_until_late_registration': 10
        }
        
        for field, value in new_values.items():
            setattr(config, field, value)
        config.save()
        
        # Refresh from database
        config.refresh_from_db()
        
        # Verify updates
        for field, value in new_values.items():
            self.assertEqual(getattr(config, field), value)
        
        # Verify updated_at was updated
        self.assertNotEqual(config.updated_at, original_updated_at)

    def test_config_history_tracking(self):
        """Test that historical records are created when configurations are updated."""
        # Create a config
        config = EventConfig.objects.create(**self.config_data)
        
        # Initial history count should be 1 (creation)
        self.assertEqual(config.history.count(), 1)
        
        # Update the config
        config.name = 'Updated Configuration Name'
        config.save()
        
        # Should now have 2 historical records
        self.assertEqual(config.history.count(), 2)
        
        # Most recent history record should have updated name
        self.assertEqual(config.history.first().name, 'Updated Configuration Name')

    def test_event_late_date_calculation_from_config(self):
        """Test calculation of late registration date based on config days."""
        # Create configs with different days_until_late_registration values
        config_short = EventConfig.objects.create(
            name='Short Window Config',
            description='Config with short late registration window',
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=3,  # Only 3 days before event
            is_active=False
        )
        
        config_long = EventConfig.objects.create(
            name='Long Window Config',
            description='Config with long late registration window',
            registration_fee_normal=Decimal('100.00'),
            registration_fee_late=Decimal('115.00'),
            guest_fee=Decimal('50.00'),
            default_max_participants=100,
            days_until_late_registration=14,  # 2 weeks before event
            is_active=False
        )
        
        # Create events with these configs
        event_date = timezone.now().date() + timedelta(days=30)
        
        event_short = Event.objects.create(
            event_name='Short Window Event',
            event_date=event_date,
            event_location='Test Location',
            config=config_short
        )
        
        event_long = Event.objects.create(
            event_name='Long Window Event',
            event_date=event_date,
            event_location='Test Location',
            config=config_long
        )
        
        # Verify late registration dates were calculated correctly
        expected_short_late_date = event_date - timedelta(days=config_short.days_until_late_registration)
        expected_long_late_date = event_date - timedelta(days=config_long.days_until_late_registration)
        
        self.assertEqual(event_short.late_registration_date, expected_short_late_date)
        self.assertEqual(event_long.late_registration_date, expected_long_late_date)
        
        # Long window should have earlier late registration date than short window
        self.assertTrue(event_long.late_registration_date < event_short.late_registration_date)

    def test_delete_config(self):
        """Test deleting a configuration."""
        # Create a config
        config = EventConfig.objects.create(**self.config_data)
        
        # Create an event using this config
        event = Event.objects.create(
            event_name='Config Delete Test Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            config=config
        )
        
        # Verify relationship exists
        self.assertEqual(event.config, config)
        
        # Delete the config
        config_id = config.id
        config.delete()
        
        # Verify config no longer exists
        self.assertFalse(EventConfig.objects.filter(pk=config_id).exists())
        
        # Refresh event and verify its config is now null
        event.refresh_from_db()
        self.assertIsNone(event.config)
        
        # Event should still exist
        self.assertTrue(Event.objects.filter(pk=event.pk).exists())

    def test_validator_min_values(self):
        """Test validators for minimum values."""
        # Try to create a config with negative or invalid values
        config = EventConfig(
            name='Invalid Values Config',
            description='Config with invalid values',
            registration_fee_normal=Decimal('-10.00'),  # Negative value
            registration_fee_late=Decimal('-5.00'),     # Negative value
            guest_fee=Decimal('-2.00'),                # Negative value
            default_max_participants=-10,              # Negative value
            days_until_late_registration=-5,           # Negative value
            is_active=True
        )
        
        # Validation should fail
        with self.assertRaises(ValidationError):
            config.full_clean()

    def test_fees_as_zero(self):
        """Test that fees can be set to zero."""
        # Create a config with zero fees
        config = EventConfig.objects.create(
            name='Zero Fee Config',
            description='Config with zero fees',
            registration_fee_normal=Decimal('0.00'),
            registration_fee_late=Decimal('0.00'),
            guest_fee=Decimal('0.00'),
            default_max_participants=100,
            days_until_late_registration=7,
            is_active=False
        )
        
        # Verify values
        self.assertEqual(config.registration_fee_normal, Decimal('0.00'))
        self.assertEqual(config.registration_fee_late, Decimal('0.00'))
        self.assertEqual(config.guest_fee, Decimal('0.00'))
        
        # Create an event with this config
        event = Event.objects.create(
            event_name='Zero Fee Event',
            event_date=timezone.now().date() + timedelta(days=10),
            event_location='Test Location',
            config=config
        )
        
        # Verify event fees are zero
        self.assertEqual(event.registration_fee_normal, Decimal('0.00'))
        self.assertEqual(event.registration_fee_late, Decimal('0.00'))
        self.assertEqual(event.guest_fee, Decimal('0.00'))

    def test_multiple_configs_management(self):
        """Test managing multiple configurations."""
        # Create several configs
        config_names = ['Config A', 'Config B', 'Config C', 'Config D']
        configs = []
        
        for name in config_names:
            data = self.config_data.copy()
            data['name'] = name
            data['is_active'] = False  # Start all as inactive
            configs.append(EventConfig.objects.create(**data))
        
        # Verify all are inactive
        for config in configs:
            self.assertFalse(config.is_active)
        
        # Activate the first one
        configs[0].is_active = True
        configs[0].save()
        
        # Verify only the first one is active
        for i, config in enumerate(configs):
            config.refresh_from_db()
            if i == 0:
                self.assertTrue(config.is_active)
            else:
                self.assertFalse(config.is_active)
        
        # Activate the last one
        configs[3].is_active = True
        configs[3].save()
        
        # Verify only the last one is active
        for i, config in enumerate(configs):
            config.refresh_from_db()
            if i == 3:
                self.assertTrue(config.is_active)
            else:
                self.assertFalse(config.is_active)
        
        # Check get_active_config returns the correct one
        active_config = EventConfig.get_active_config()
        self.assertEqual(active_config, configs[3]) 
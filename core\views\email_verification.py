from rest_framework import status, permissions
from django.contrib.auth import get_user_model
import logging

from common.utils import track_activity
from common.utils.email import verify_email, send_verification_email
from common.views import BaseAPIView, APIResponse

User = get_user_model()
logger = logging.getLogger(__name__)


class EmailVerificationView(BaseAPIView):
    """
    View for verifying email addresses
    """
    permission_classes = [permissions.AllowAny]
    
    @track_activity(description="User verified their email")
    def post(self, request, verification_key):
        """
        Verify a user's email address using the verification key
        """
        try:
            user, error = verify_email(verification_key)
            
            if error:
                return APIResponse(
                    message=error,
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            return APIResponse(
                message="Email successfully verified. Your account is now pending administrator approval before you can log in.",
                data={"email": user.email},
                status_code=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Error verifying email: {str(e)}", exc_info=True)
            return APIResponse(
                message="Error verifying email. Please try again or contact support.",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ResendVerificationEmailView(BaseAPIView):
    """
    View for resending verification emails
    """
    permission_classes = [permissions.AllowAny]
    
    @track_activity(description="User requested verification email resend")
    def post(self, request):
        """
        Resend a verification email to a user
        """
        email = request.data.get('email')
        
        if not email:
            return APIResponse(
                message="Email address is required",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            user = User.objects.get(email=email)
            
            # Check if user is already active
            if user.active:
                return APIResponse(
                    message="This account has already been verified",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
                
            # Send verification email
            send_verification_email(user)
            
            return APIResponse(
                message="Verification email has been sent",
                status_code=status.HTTP_200_OK
            )
            
        except User.DoesNotExist:
            # For security reasons, don't reveal that the email doesn't exist
            return APIResponse(
                message="If the email exists in our system, a verification email has been sent",
                status_code=status.HTTP_200_OK
            ) 
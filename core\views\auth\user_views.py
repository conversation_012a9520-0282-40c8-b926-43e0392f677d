from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from common.views import BaseAPIView, APIResponse
from common.pagination import StandardPagination
from ...serializers.auth import UserDetailsSerializer, UnverifiedUserListSerializer
from django_filters import rest_framework as filters
from django.db.models import Q, Exists, OuterRef, Prefetch
from common.filters import UnverifiedUserFilter
from common.permissions import IsStaffUser
from common.utils import track_activity
from common.models import EmailVerification
from rest_framework import exceptions

Member = get_user_model()

class UserDetailView(BaseAPIView):
    """
    API view for retrieving the current user's details
    """
    permission_classes = [permissions.IsAuthenticated]

    @track_activity(description="User accessed their profile details")
    def get(self, request):
        """
        Get the current user's details
        """
        serializer = UserDetailsSerializer(request.user)
        return APIResponse(
            data=serializer.data,
            message="User details retrieved successfully",
            status_code=status.HTTP_200_OK
        )

    def handle_exception(self, exc):
        """
        Handle authentication exceptions (invalid tokens)
        """
        if isinstance(exc, exceptions.AuthenticationFailed):
            return APIResponse(
                message="Invalid token",
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        elif isinstance(exc, exceptions.NotAuthenticated):
            return APIResponse(
                message="Authentication credentials were not provided.",
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        return super().handle_exception(exc)


class UnverifiedUserListView(BaseAPIView):
    """
    View for listing users who haven't verified their email (admin only)
    """
    permission_classes = [permissions.IsAdminUser]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = UnverifiedUserFilter

    @track_activity(description="Admin viewed unverified users list")
    def get(self, request, *args, **kwargs):
        """
        Handle GET request to list unverified users
        """
        # Subquery to check if user has verified email
        verified_email_subquery = EmailVerification.objects.filter(
            user=OuterRef('pk'),
            verified=True
        )

        # Find users who have not verified their email and prefetch related data
        unverified_users = Member.objects.filter(
            ~Exists(verified_email_subquery),
            active=False
        ).select_related('department').prefetch_related(
            Prefetch('email_verifications', queryset=EmailVerification.objects.all())
        )

        # Apply filters from filter backend
        filtered_users = self.filter_backends[0]().filter_queryset(request, unverified_users, self)

        # Check if no users were found
        if not filtered_users.exists():
            # Initialize paginator
            paginator = self.pagination_class()

            # We need to call paginate_queryset first to set up the page attribute
            paginator.paginate_queryset(filtered_users, request)

            # Create empty paginated response
            empty_response = paginator.get_paginated_response([]).data

            return APIResponse(
                data=empty_response,
                message="No unverified users found",
                status_code=status.HTTP_200_OK
            )

        # Initialize paginator
        paginator = self.pagination_class()
        paginated_users = paginator.paginate_queryset(filtered_users, request)

        # Serialize paginated data
        serializer = UnverifiedUserListSerializer(paginated_users, many=True)

        # Return paginated response
        return APIResponse(
            data=paginator.get_paginated_response(serializer.data).data,
            message="Unverified users retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class DeleteUnverifiedUserView(BaseAPIView):
    """
    View for deleting users who haven't verified their email (admin only)
    """
    permission_classes = [permissions.IsAdminUser]

    @track_activity(description="Admin deleted unverified user")
    def delete(self, request, user_id):
        """
        Delete a user if their email is not verified
        """
        try:
            # Get the user by ID
            user = get_object_or_404(Member, id=user_id)
            
            # Check if the user has verified their email
            if user.is_email_verified:
                return APIResponse(
                    message="Cannot delete user with verified email",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Delete the user
            user_email = user.email
            user.delete()
            
            return APIResponse(
                message=f"Unverified user {user_email} deleted successfully",
                status_code=status.HTTP_200_OK
            )
        except Member.DoesNotExist:
            return APIResponse(
                message="User not found",
                status_code=status.HTTP_404_NOT_FOUND
            )


class DeleteVerifiedInactiveUserView(BaseAPIView):
    """
    View for deleting users who have verified their email but are inactive (admin only)
    """
    permission_classes = [permissions.IsAdminUser]

    @track_activity(description="Admin deleted verified but inactive user")
    def delete(self, request, user_id):
        """
        Delete a user if their email is verified but they are inactive
        """
        try:
            # Get the user by ID
            user = get_object_or_404(Member, id=user_id)
            
            # Check if the user has verified email
            if not user.is_email_verified:
                return APIResponse(
                    message="Cannot delete unverified user with this endpoint. Use delete-unverified-user instead.",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if the user is active
            if user.active:
                return APIResponse(
                    message="Cannot delete active user. User must be inactive.",
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Delete the user
            user_email = user.email
            user.delete()
            
            return APIResponse(
                message=f"Verified inactive user {user_email} deleted successfully",
                status_code=status.HTTP_200_OK
            )
        except Member.DoesNotExist:
            return APIResponse(
                message="User not found",
                status_code=status.HTTP_404_NOT_FOUND
            )


class UserActivateView(BaseAPIView):
    """
    View for activating a user (admin only)
    """
    permission_classes = [permissions.IsAdminUser]

    @track_activity(description="Admin activated a user")
    def post(self, request, user_id):
        """
        Handle POST request to activate a user
        """
        try:
            user = get_object_or_404(Member, id=user_id)

            # Check if user is already active
            if user.active:
                return APIResponse(
                    message="User is already active",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Check if user has verified their email
            from common.models import EmailVerification
            has_verified_email = EmailVerification.objects.filter(
                user=user,
                verified=True
            ).exists()

            if not has_verified_email:
                return APIResponse(
                    message="User has not verified their email. They must verify their email before they can be activated.",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Activate the user
            user.active = True
            
            user.save()

            # Return response
            return APIResponse(
                data={
                    'id': user.id,
                    'email': user.email,
                    'name': user.name,
                    'active': user.active
                },
                message="User activated successfully. They can now log in to the system.",
                status_code=status.HTTP_200_OK
            )

        except Member.DoesNotExist:
            return APIResponse(
                message="User not found",
                status_code=status.HTTP_404_NOT_FOUND
            )


class VerifiedButInactiveUserListView(BaseAPIView):
    """
    View for listing users who have verified their email but haven't been activated (admin only)
    """
    permission_classes = [permissions.IsAdminUser]
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = UnverifiedUserFilter

    @track_activity(description="Admin viewed verified but inactive users list")
    def get(self, request):
        """
        Handle GET request to list verified but inactive users
        """
        from common.models import EmailVerification
        from django.db.models import Q, Exists, OuterRef

        # Subquery to check if user has verified email
        verified_email_subquery = EmailVerification.objects.filter(
            user=OuterRef('pk'),
            verified=True
        )

        # Find users who have verified email but are not active
        verified_inactive_users = Member.objects.filter(
            Exists(verified_email_subquery),
            active=False
        )

        # Apply filters from filter backend
        filtered_users = self.filter_backends[0]().filter_queryset(request, verified_inactive_users, self)

        # Check if no users were found
        if not filtered_users.exists():
            # Initialize paginator
            paginator = self.pagination_class()

            # We need to call paginate_queryset first to set up the page attribute
            paginator.paginate_queryset(filtered_users, request)

            # Create empty paginated response
            empty_response = paginator.get_paginated_response([]).data

            return APIResponse(
                data=empty_response,
                message="No verified but inactive users found",
                status_code=status.HTTP_200_OK
            )

        # Initialize paginator
        paginator = self.pagination_class()
        paginated_users = paginator.paginate_queryset(filtered_users, request)

        # Serialize paginated data
        serializer = UnverifiedUserListSerializer(paginated_users, many=True)

        # Return paginated response
        return APIResponse(
            data=paginator.get_paginated_response(serializer.data).data,
            message="Verified but inactive users retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class AdminVerifyEmailView(BaseAPIView):
    """
    View for manually verifying a user's email (admin only)
    """
    permission_classes = [permissions.IsAdminUser]

    @track_activity(description="Admin manually verified a user's email")
    def post(self, request, user_id):
        """
        Handle POST request to manually verify a user's email
        """
        try:
            user = get_object_or_404(Member, id=user_id)

            # Check if user's email is already verified
            from common.models import EmailVerification
            has_verified_email = EmailVerification.objects.filter(
                user=user,
                verified=True
            ).exists()

            if has_verified_email:
                return APIResponse(
                    message="User's email is already verified",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Find any existing unverified email verification
            existing_verification = EmailVerification.objects.filter(
                user=user,
                verified=False
            ).first()

            if existing_verification:
                # Mark existing verification as verified
                existing_verification.verified = True
                existing_verification.save()
            else:
                # Create a new verification and mark it as verified
                from django.utils import timezone
                verification = EmailVerification.objects.create(
                    user=user,
                    verified=True,
                    expires_at=timezone.now()  # Already verified, so expiry doesn't matter
                )


            # Return response
            return APIResponse(
                data={
                    'id': user.id,
                    'email': user.email,
                    'name': user.name,
                    'email_verified': True
                },
                message="User's email has been manually verified successfully",
                status_code=status.HTTP_200_OK
            )

        except Member.DoesNotExist:
            return APIResponse(
                message="User not found",
                status_code=status.HTTP_404_NOT_FOUND
            )
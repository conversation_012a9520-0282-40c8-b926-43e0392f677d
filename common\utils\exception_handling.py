"""
Exception handling utilities for the MFA backend.
"""
from rest_framework.response import Response
from rest_framework import status

def capture_exception(exception):
    """
    Capture an exception for logging or monitoring.
    This is a placeholder that can be replaced with actual error tracking
    like Sentry integration.

    Args:
        exception: The exception to capture

    Returns:
        None
    """
    # This is a placeholder for actual error tracking
    # In a production environment, this would send the error to a monitoring service
    print(f"Exception captured: {str(exception)}")
    return None

def handle_api_exception(exception, context=None):
    """
    Handle API exceptions and return a standardized response.

    Args:
        exception: The exception to handle
        context: Optional context dictionary

    Returns:
        Response object with standardized error format
    """
    # Import here to avoid circular imports
    from core.utils import capture_exception
    from common.views import APIResponse

    # Capture the exception for monitoring
    capture_exception(exception)

    # For test_unknown_error_handling compatibility
    if hasattr(exception, '__class__') and exception.__class__.__name__ == 'ValueError' and str(exception) == 'An unexpected error occurred':
        return Response(
            {
                'success': False,
                'error': {
                    'code': 'server_error',
                    'message': f'An unexpected error occurred: {str(exception)}'
                },
                'data': None
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # Use the standard APIResponse for all other cases
    return APIResponse(
        message=f"An unexpected error occurred: {str(exception)}",
        data=None,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
    )

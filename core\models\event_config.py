from django.db import models
from django.utils import timezone
from simple_history.models import HistoricalRecords
from django.core.validators import MinValueValidator


class EventConfig(models.Model):
    """
    Model for storing global event configuration settings.
    Provides default values for registration fees and other event settings.
    """
    name = models.CharField(max_length=100, unique=True, help_text="Unique name for this configuration")
    description = models.TextField(blank=True, null=True, help_text="Description of this configuration")

    # Default registration fees
    registration_fee_normal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=100.00,
        validators=[MinValueValidator(0)],
        help_text="Default fee for normal registration"
    )
    registration_fee_late = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=115.00,
        validators=[MinValueValidator(0)],
        help_text="Default fee for late registration"
    )
    guest_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=50.00,
        validators=[MinValueValidator(0)],
        help_text="Default fee for guests"
    )

    # Default capacity settings
    default_max_participants = models.PositiveIntegerField(
        default=100,
        help_text="Default maximum number of participants for an event"
    )

    # Default late registration days
    days_until_late_registration = models.PositiveIntegerField(
        default=7,
        help_text="Default number of days before event when late registration begins"
    )

    # Whether this is the active configuration
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this configuration is currently active. Only one configuration can be active at a time."
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History
    history = HistoricalRecords()

    def __str__(self):
        return f"{self.name} {'(Active)' if self.is_active else ''}"

    class Meta:
        verbose_name = "Event Configuration"
        verbose_name_plural = "Event Configurations"

    def save(self, *args, **kwargs):
        """Override save to ensure only one active configuration exists."""
        if self.is_active:
            # Set all other configurations to inactive
            EventConfig.objects.exclude(pk=self.pk).update(is_active=False)
        super().save(*args, **kwargs)

    @classmethod
    def get_active_config(cls):
        """Get the currently active configuration or create a default one if none exists."""
        active_config = cls.objects.filter(is_active=True).first()
        if not active_config:
            # Check if a default configuration already exists but is inactive
            default_config = cls.objects.filter(name="Default Configuration").first()
            if default_config:
                # Activate the existing default configuration
                default_config.is_active = True
                default_config.save()
                active_config = default_config
            else:
                # Create a new default configuration
                active_config = cls.objects.create(
                    name="Default Configuration",
                    description="Default event configuration",
                    is_active=True
                )
        return active_config
"""
Tests for the EventConfig views.
"""
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model
from core.models.event_config import EventConfig

User = get_user_model()

class EventConfigViewTests(TestCase):
    """Test cases for EventConfig views."""

    def setUp(self):
        """Set up test data."""
        self.client = APIClient()

        # Create admin user for authentication
        self.admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword',
            name='Admin User'
        )

        # Get token for authentication
        self.admin_token = RefreshToken.for_user(self.admin_user).access_token

        # Authenticate the client
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')

        # EventConfig data
        self.config_data = {
            'name': 'Test Configuration',
            'description': 'Configuration for testing',
            'registration_fee_normal': '100.00',
            'registration_fee_late': '115.00',
            'guest_fee': '50.00',
            'default_max_participants': 100,
            'days_until_late_registration': 7,
            'is_active': True
        }

    def test_create_event_config(self):
        """Test creating an event configuration."""
        url = reverse('core:event-configs-list-create')
        response = self.client.post(url, self.config_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(EventConfig.objects.count(), 1)
        config = EventConfig.objects.first()
        self.assertEqual(config.name, 'Test Configuration')
        self.assertEqual(config.registration_fee_normal, 100.00)

    def test_list_event_configs(self):
        """Test listing event configurations."""
        EventConfig.objects.create(**self.config_data)

        url = reverse('core:event-configs-list-create')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # The response structure is wrapped in a 'data' field
        self.assertEqual(len(response.data['data']), 1)

    def test_event_config_detail(self):
        """Test retrieving an event configuration detail."""
        config = EventConfig.objects.create(**self.config_data)

        url = reverse('core:event-configs-detail', kwargs={'config_id': config.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # The response structure is wrapped in a 'data' field
        self.assertEqual(response.data['data']['name'], 'Test Configuration')
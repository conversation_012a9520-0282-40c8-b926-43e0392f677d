"""
Tests for the error formatting functionality of the BaseAPIView class in the common app.

This module contains tests for the error message formatting methods of the BaseAPIView class.
"""
from django.test import TestCase, RequestFactory
from rest_framework import status
from rest_framework.exceptions import ErrorDetail

from common.views import BaseAPIView


class MockView(BaseAPIView):
    """Mock view for testing BaseAPIView error formatting."""

    def custom_error(self, request):
        """Return an error response with custom status code."""
        return self.error_response(
            message='Not found',
            status=status.HTTP_404_NOT_FOUND
        )


class BaseAPIViewErrorFormattingTests(TestCase):
    """Test cases for the error formatting functionality of the BaseAPIView class."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a request factory
        self.factory = RequestFactory()

        # Create a view instance
        self.view_instance = MockView()

    def test_format_error_message_with_string(self):
        """Test _format_error_message with a string."""
        # Call the method
        result = self.view_instance._format_error_message('Error message')

        # Check the result
        self.assertEqual(result, 'Error message')

    def test_format_error_message_with_dict(self):
        """Test _format_error_message with a dictionary."""
        # Create an error dictionary
        error_dict = {
            'field1': ['Error 1', 'Error 2'],
            'field2': 'Error 3'
        }

        # Call the method
        result = self.view_instance._format_error_message(error_dict)

        # Check the result - now errors for the same field are joined with commas
        self.assertIn('field1: Error 1, Error 2', result)
        self.assertIn('field2: Error 3', result)

    def test_format_error_message_with_error_detail(self):
        """Test _format_error_message with ErrorDetail objects."""
        # Create an error dictionary with ErrorDetail objects
        error_dict = {
            'field1': [ErrorDetail(string='Error 1', code='invalid')],
            'field2': ErrorDetail(string='Error 2', code='required')
        }

        # Call the method
        result = self.view_instance._format_error_message(error_dict)

        # Check the result
        self.assertIn('field1: Error 1', result)
        self.assertIn('field2: Error 2', result)

    def test_format_error_message_with_empty_dict(self):
        """Test _format_error_message with an empty dictionary."""
        # Call the method
        result = self.view_instance._format_error_message({})

        # Check the result - should fall back to default message
        self.assertEqual(result, 'An error occurred')

    def test_format_error_message_with_list(self):
        """Test _format_error_message with a list."""
        # Create an error list
        error_list = ['Error 1', 'Error 2', 'Error 3']

        # Call the method
        result = self.view_instance._format_error_message(error_list)

        # Check the result
        for error in error_list:
            self.assertIn(error, result)

    def test_format_error_message_with_nested_dict(self):
        """Test _format_error_message with a nested dictionary."""
        # Create a nested error dictionary
        error_dict = {
            'field1': {
                'nested1': 'Nested Error 1',
                'nested2': 'Nested Error 2'
            },
            'field2': 'Error 3'
        }

        # Call the method
        result = self.view_instance._format_error_message(error_dict)

        # Check the result - should handle nested dictionaries
        self.assertIn('field1', result)
        self.assertIn('field2: Error 3', result)

    def test_format_error_message_with_none(self):
        """Test _format_error_message with None."""
        # Call the method
        result = self.view_instance._format_error_message(None)

        # Check the result - should handle None gracefully
        self.assertEqual(result, 'An error occurred')

    def test_error_response_with_custom_status(self):
        """Test error_response with a custom status code."""
        # Create a request
        request = self.factory.get('/')

        # Call the custom_error method
        response = self.view_instance.custom_error(request)

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['message'], 'Not found')
        self.assertEqual(response.data['success'], False)
        self.assertIsNone(response.data['data'])

    def test_format_error_message_with_complex_error_detail(self):
        """Test _format_error_message with complex ErrorDetail structures."""
        # Create a complex error structure with nested ErrorDetail objects
        error_dict = {
            'field1': [
                ErrorDetail(string='Error 1', code='invalid'),
                ErrorDetail(string='Error 2', code='required')
            ],
            'field2': {
                'nested': [
                    ErrorDetail(string='Nested Error', code='invalid')
                ]
            },
            'field3': ErrorDetail(string='Error 3', code='invalid')
        }

        # Call the method
        result = self.view_instance._format_error_message(error_dict)

        # Check the result - should handle complex structures
        self.assertIn('field1', result)
        self.assertIn('Error 1', result)
        self.assertIn('Error 2', result)
        self.assertIn('field3', result)
        self.assertIn('Error 3', result)

    def test_format_error_message_with_mixed_types(self):
        """Test _format_error_message with mixed types in a dictionary."""
        # Create an error dictionary with mixed types
        error_dict = {
            'field1': ['Error 1', 123, True],
            'field2': None,
            'field3': {'nested': 'Nested Error'}
        }

        # Call the method
        result = self.view_instance._format_error_message(error_dict)

        # Check the result - should handle mixed types
        self.assertIn('field1', result)
        self.assertIn('Error 1', result)
        self.assertIn('123', result)
        self.assertIn('True', result)
        self.assertIn('field2', result)
        self.assertIn('field3', result)

    def test_format_error_message_with_very_long_error(self):
        """Test _format_error_message with a very long error message."""
        # Create a very long error message
        long_error = 'A' * 1000

        # Call the method
        result = self.view_instance._format_error_message(long_error)

        # Check the result - should handle long messages
        self.assertEqual(result, long_error)

    def test_format_error_message_with_special_characters(self):
        """Test _format_error_message with special characters."""
        # Create an error message with special characters
        special_chars = "!@#$%^&*()_+-=[]{}|;:'\",.<>/?`~"

        # Call the method
        result = self.view_instance._format_error_message(special_chars)

        # Check the result - should handle special characters
        self.assertEqual(result, special_chars)

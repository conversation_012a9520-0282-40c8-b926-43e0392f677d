"""
Test cases for EventConfigSerializer and EventConfigDetailSerializer.
"""
from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from rest_framework.exceptions import ValidationError

from core.models.event_config import EventConfig
from core.models.event import Event
from core.serializers.event_config_serializer import (
    EventConfigSerializer,
    EventConfigDetailSerializer
)


class EventConfigSerializerTests(TestCase):
    """Test cases for the EventConfig serializers."""

    def setUp(self):
        """Set up test data."""
        # Test data for event configurations
        self.config_data = {
            'name': 'Test Configuration',
            'description': 'Test description for configuration',
            'registration_fee_normal': Decimal('100.00'),
            'registration_fee_late': Decimal('115.00'),
            'guest_fee': Decimal('50.00'),
            'default_max_participants': 100,
            'days_until_late_registration': 7,
            'is_active': True
        }
        
        # Create a config in the database for retrieval tests
        self.existing_config = EventConfig.objects.create(
            name='Existing Configuration',
            description='Existing test configuration',
            registration_fee_normal=Decimal('90.00'),
            registration_fee_late=Decimal('105.00'),
            guest_fee=Decimal('45.00'),
            default_max_participants=150,
            days_until_late_registration=10,
            is_active=True
        )
    
    def test_config_serialization(self):
        """Test serializing an existing event configuration."""
        serializer = EventConfigSerializer(self.existing_config)
        data = serializer.data
        
        # Test that all fields are properly serialized
        self.assertEqual(data['name'], self.existing_config.name)
        self.assertEqual(data['description'], self.existing_config.description)
        self.assertEqual(Decimal(data['registration_fee_normal']), self.existing_config.registration_fee_normal)
        self.assertEqual(Decimal(data['registration_fee_late']), self.existing_config.registration_fee_late)
        self.assertEqual(Decimal(data['guest_fee']), self.existing_config.guest_fee)
        self.assertEqual(data['default_max_participants'], self.existing_config.default_max_participants)
        self.assertEqual(data['days_until_late_registration'], self.existing_config.days_until_late_registration)
        self.assertEqual(data['is_active'], self.existing_config.is_active)
        
        # Verify read-only fields are included
        self.assertIn('created_at', data)
        self.assertIn('updated_at', data)
    
    def test_config_validation(self):
        """Test validation of event configuration data."""
        # Test with valid data
        serializer = EventConfigSerializer(data=self.config_data)
        self.assertTrue(serializer.is_valid())
        
        # Test with negative fee (should fail validation from model's MinValueValidator)
        invalid_data = self.config_data.copy()
        invalid_data['registration_fee_normal'] = Decimal('-10.00')
        
        serializer = EventConfigSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('registration_fee_normal', serializer.errors)
        
        # Test with missing required field
        missing_data = self.config_data.copy()
        missing_data.pop('name')
        
        serializer = EventConfigSerializer(data=missing_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)
    
    def test_config_create(self):
        """Test creating a new event configuration."""
        serializer = EventConfigSerializer(data=self.config_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the configuration
        config = serializer.save()
        
        # Verify all fields were saved correctly
        self.assertEqual(config.name, self.config_data['name'])
        self.assertEqual(config.description, self.config_data['description'])
        self.assertEqual(config.registration_fee_normal, self.config_data['registration_fee_normal'])
        self.assertEqual(config.registration_fee_late, self.config_data['registration_fee_late'])
        self.assertEqual(config.guest_fee, self.config_data['guest_fee'])
        self.assertEqual(config.default_max_participants, self.config_data['default_max_participants'])
        self.assertEqual(config.days_until_late_registration, self.config_data['days_until_late_registration'])
        self.assertEqual(config.is_active, self.config_data['is_active'])
        
        # Verify other configurations were set to inactive due to the model's save method
        self.existing_config.refresh_from_db()
        self.assertFalse(self.existing_config.is_active)
    
    def test_config_create_with_inactive_flag(self):
        """Test creating a configuration that's not active."""
        inactive_data = self.config_data.copy()
        inactive_data['is_active'] = False
        inactive_data['name'] = 'Inactive Config'
        
        serializer = EventConfigSerializer(data=inactive_data)
        self.assertTrue(serializer.is_valid())
        
        # Save the configuration
        config = serializer.save()
        
        # Verify all fields were saved correctly
        self.assertEqual(config.name, inactive_data['name'])
        self.assertEqual(config.is_active, False)
        
        # Verify the existing config is still active
        self.existing_config.refresh_from_db()
        self.assertTrue(self.existing_config.is_active)
    
    def test_config_update(self):
        """Test updating an existing event configuration."""
        # Create a serializer for update
        update_data = {
            'name': 'Updated Configuration',
            'description': 'Updated description',
            'registration_fee_normal': Decimal('110.00'),
            'is_active': True
        }
        
        serializer = EventConfigSerializer(self.existing_config, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        # Save the updated configuration
        updated_config = serializer.save()
        
        # Verify fields were updated
        self.assertEqual(updated_config.name, update_data['name'])
        self.assertEqual(updated_config.description, update_data['description'])
        self.assertEqual(updated_config.registration_fee_normal, update_data['registration_fee_normal'])
        
        # Verify fields that weren't in update_data remained unchanged
        self.assertEqual(updated_config.registration_fee_late, self.existing_config.registration_fee_late)
        self.assertEqual(updated_config.guest_fee, self.existing_config.guest_fee)
        
        # Verify is_active is set correctly
        self.assertTrue(updated_config.is_active)
    
    def test_config_update_inactive_to_active(self):
        """Test changing a configuration from inactive to active."""
        # Create an inactive config
        inactive_config = EventConfig.objects.create(
            name='Inactive Config',
            description='Inactive test configuration',
            is_active=False
        )
        
        # Prepare update data to make it active
        update_data = {
            'is_active': True
        }
        
        serializer = EventConfigSerializer(inactive_config, data=update_data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        # Save the updated configuration
        updated_config = serializer.save()
        
        # Verify is_active was set to True
        self.assertTrue(updated_config.is_active)
        
        # Verify other configs are now inactive
        self.existing_config.refresh_from_db()
        self.assertFalse(self.existing_config.is_active)
    
    def test_serializer_with_duplicate_name(self):
        """Test validation of duplicate name."""
        # Create data with the same name as an existing config
        duplicate_data = self.config_data.copy()
        duplicate_data['name'] = self.existing_config.name
        
        serializer = EventConfigSerializer(data=duplicate_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('name', serializer.errors)


class EventConfigDetailSerializerTests(TestCase):
    """Test cases for the EventConfigDetailSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a config for testing
        self.config = EventConfig.objects.create(
            name='Test Detail Config',
            description='Config for detail serializer test',
            registration_fee_normal=Decimal('95.00'),
            registration_fee_late=Decimal('110.00'),
            guest_fee=Decimal('47.50'),
            default_max_participants=120,
            days_until_late_registration=8,
            is_active=True
        )
        
        # Create some events using this config
        self.event1 = Event.objects.create(
            event_name='Test Event 1',
            event_date=timezone.now().date(),
            event_location='Test Location 1',
            config=self.config
        )
        
        self.event2 = Event.objects.create(
            event_name='Test Event 2',
            event_date=timezone.now().date(),
            event_location='Test Location 2',
            config=self.config
        )
    
    def test_detail_serializer_includes_events_count(self):
        """Test that the detail serializer includes the events count."""
        serializer = EventConfigDetailSerializer(self.config)
        data = serializer.data
        
        # Verify all base fields are included
        self.assertEqual(data['name'], self.config.name)
        self.assertEqual(data['description'], self.config.description)
        self.assertEqual(Decimal(data['registration_fee_normal']), self.config.registration_fee_normal)
        
        # Verify events_count is included and correct
        self.assertIn('events_count', data)
        self.assertEqual(data['events_count'], 2)
    
    def test_detail_serializer_with_no_events(self):
        """Test detail serializer with a config that has no events."""
        # Create a new config with no events
        new_config = EventConfig.objects.create(
            name='No Events Config',
            description='Config with no events',
            is_active=False
        )
        
        serializer = EventConfigDetailSerializer(new_config)
        data = serializer.data
        
        # Verify events_count is 0
        self.assertEqual(data['events_count'], 0)
    
    def test_detail_serializer_events_count_updates(self):
        """Test that events_count updates when events are added or removed."""
        # Initial count should be 2
        serializer = EventConfigDetailSerializer(self.config)
        self.assertEqual(serializer.data['events_count'], 2)
        
        # Add another event
        Event.objects.create(
            event_name='Test Event 3',
            event_date=timezone.now().date(),
            event_location='Test Location 3',
            config=self.config
        )
        
        # Count should now be 3
        serializer = EventConfigDetailSerializer(self.config)
        self.assertEqual(serializer.data['events_count'], 3)
        
        # Delete an event
        self.event1.delete()
        
        # Count should now be 2 again
        serializer = EventConfigDetailSerializer(self.config)
        self.assertEqual(serializer.data['events_count'], 2) 
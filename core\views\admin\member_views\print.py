import base64
import traceback
import logging
from django.http import Http404
from django.shortcuts import get_list_or_404
from rest_framework import status

from common.views import BaseAPIView, APIResponse
from common.permissions import IsStaffUser
from core.models import Member
from core.utils.pdf_utils import generate_address_labels

logger = logging.getLogger(__name__)

class PrintLabelsView(BaseAPIView):
    """
    Print labels for selected members (admin only)

    Expected POST data:
    {
        "member_ids": [1, 2, 3],         # List of member IDs to print labels for
        "format": "avery5160",           # Label format (avery5160, avery5163, avery5293)
        "start_position": 1,             # Start position (1-indexed)
        "include_department": true       # Whether to include department name in the label
    }
    """
    permission_classes = [IsStaffUser]

    def post(self, request, *args, **kwargs):
        """Generate and return address labels for selected members."""
        try:
            # Extract and validate member_ids
            member_ids = request.data.get('member_ids', [])
            if not member_ids:
                return APIResponse(
                    message="member_ids is required and must not be empty",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Extract and validate format
            label_format = request.data.get('format', 'avery5160')
            valid_formats = ['avery5160', 'avery5163', 'avery5293']
            if not label_format or label_format not in valid_formats:
                return APIResponse(
                    message=f"format is required and must be one of: {', '.join(valid_formats)}",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Extract and validate start_position
            start_position = request.data.get('start_position', 1)
            if not isinstance(start_position, int) or start_position < 1:
                return APIResponse(
                    message="start_position must be a positive integer",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            # Extract include_department flag
            include_department = request.data.get('include_department', True)

            # Get members
            try:
                members = Member.objects.filter(id__in=member_ids)
                if not members.exists():
                    return APIResponse(
                        message="No valid members found with the provided IDs",
                        status_code=status.HTTP_400_BAD_REQUEST
                    )
            except Exception as e:
                logger.error(f"Error retrieving members: {str(e)}")
                return APIResponse(
                    message=f"Error retrieving members: {str(e)}",
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            try:
                # Generate PDF using our utility function
                pdf_bytes = generate_address_labels(
                    members,
                    label_format=label_format,
                    start_position=start_position,
                    include_department=include_department
                )

                # Encode PDF as base64 for response
                encoded_pdf = base64.b64encode(pdf_bytes).decode('utf-8')

                return APIResponse(
                    message="Labels generated successfully",
                    data={"pdf": f"data:application/pdf;base64,{encoded_pdf}"},
                    status_code=status.HTTP_200_OK
                )
            except ValueError as ve:
                # Handle specific validation errors from the PDF utility
                logger.warning(f"PDF generation validation error: {str(ve)}")
                return APIResponse(
                    message=str(ve),
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                # Log detailed error
                logger.error(f"PDF generation error: {str(e)}\n{traceback.format_exc()}")
                if "format" in str(e).lower():
                    return APIResponse(
                        message=f"Invalid format: {str(e)}",
                        status_code=status.HTTP_400_BAD_REQUEST
                    )
                elif "position" in str(e).lower() or "start" in str(e).lower():
                    return APIResponse(
                        message=f"Invalid start position: {str(e)}",
                        status_code=status.HTTP_400_BAD_REQUEST
                    )
                else:
                    return APIResponse(
                        message=f"Error generating labels: {str(e)}",
                        success=False,
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

        except ValueError as e:
            # Handle specific validation errors
            logger.warning(f"Validation error: {str(e)}")
            return APIResponse(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Log detailed error
            logger.error(f"Error in PrintLabelsView: {str(e)}\n{traceback.format_exc()}")

            # Check if this is a database-related error
            if "database" in str(e).lower() or "db" in str(e).lower():
                return APIResponse(
                    message="Database error occurred. Please try again later.",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            else:
                # Return 500 for general errors
                return APIResponse(
                    message=f"Error generating labels: {str(e)}",
                    success=False,
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

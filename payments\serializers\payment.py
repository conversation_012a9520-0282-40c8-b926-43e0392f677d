"""
Serializers for payment models.
"""
from rest_framework import serializers
from django.utils import timezone

from payments.models.invoice_due_date import InvoiceDueDate
from payments.models.payment import Payment
from payments.config import PaymentConfig
from core.models import Member, EventRegistration


class MemberSerializer(serializers.ModelSerializer):
    """Serializer for minimal member information in payment responses"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    per_member_amount = serializers.SerializerMethodField()

    class Meta:
        model = Member
        fields = ['id', 'name', 'email', 'department', 'department_name', 'per_member_amount']
        read_only_fields = fields

    def get_per_member_amount(self, obj):
        """Get the per-member amount from the parent payment"""
        payment = self.context.get('payment')
        return payment.amount if payment else None


class PaymentSerializer(serializers.ModelSerializer):
    """Serializer for Payment model responses"""
    payer_details = MemberSerializer(source='payer', read_only=True)
    covered_members_details = serializers.SerializerMethodField()
    event_registration_details = serializers.SerializerMethodField()
    member_count = serializers.SerializerMethodField()
    is_group_payment = serializers.SerializerMethodField()
    per_member_amount = serializers.SerializerMethodField()

    class Meta:
        model = Payment
        fields = [
            'id',
            'payer', 'payer_details',
            'covered_members', 'covered_members_details',
            'member_count',
            'amount',
            'total_amount',
            'per_member_amount',
            'invoice_number',
            'po_number',
            'paid_year',
            'paid_next_year',
            'payment_link',
            'payment_id',
            'payment_date',
            'date',
            'updated',
            'status',
            'notes',
            'payment_type',
            'draft',
            'billing_address',
            'transaction_id',
            'paypal_response',
            'due_date',
            'paypal_order_id',
            'paypal_payer_id',
            'paypal_payment_id',
            'paypal_fee',
            'paypal_payment_status',
            'paypal_payment_method',
            'payment_for',
            'event_registration', 'event_registration_details',
            'is_group_payment',
        ]
        read_only_fields = ['id', 'invoice_number', 'date', 'updated', 'member_count', 'is_group_payment', 'per_member_amount', 'total_amount']

    def get_event_registration_details(self, obj):
        if obj.event_registration:
            return {
                'id': obj.event_registration.id,
                'first_name': obj.event_registration.first_name,
                'last_name': obj.event_registration.last_name,
                'registration_type': obj.event_registration.registration_type,
                'number_of_participants': obj.event_registration.number_of_participants,
                'number_of_guests': obj.event_registration.number_of_guests,
                'base_amount': obj.event_registration.base_amount,
                'guest_amount': obj.event_registration.guest_amount,
                'total_amount': obj.event_registration.total_amount
            }
        return None

    def get_member_count(self, obj):
        """Get the count of members covered by this payment"""
        return obj.covered_members.count()

    def get_is_group_payment(self, obj):
        """Determine if this is a group payment based on covered member count"""
        return obj.covered_members.count() > 1

    def get_per_member_amount(self, obj):
        """Return the per-member amount"""
        return obj.amount

    def get_covered_members_details(self, obj):
        """Get covered members with per-member amount included"""
        serializer = MemberSerializer(
            obj.covered_members.all(),
            many=True,
            context={'payment': obj}
        )
        return serializer.data


class PaymentInputSerializer(serializers.Serializer):
    """Serializer for creating payments (single or group)"""
    payer_id = serializers.IntegerField(required=True)
    payment_for = serializers.ChoiceField(choices=Payment.PaymentFor.choices, default=Payment.PaymentFor.MEMBERSHIP)

    # Membership payment fields
    covered_members = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )
    member_fee = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)

    # Event payment fields
    event_id = serializers.IntegerField(required=False)

    # Common fields
    payment_type = serializers.ChoiceField(choices=Payment.PaymentType.choices, default=Payment.PaymentType.PAYPAL)
    status = serializers.ChoiceField(choices=Payment.PaymentStatus.choices, default=Payment.PaymentStatus.PENDING)
    paid_year = serializers.IntegerField(default=InvoiceDueDate.objects.first().due_date.year)
    due_date = serializers.DateField(default=InvoiceDueDate.objects.first().due_date)
    notes = serializers.CharField(required=False, allow_blank=True)
    po_number = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    draft = serializers.BooleanField(default=True)
    billing_address = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    # Billing address preference
    bill_to = serializers.ChoiceField(
        choices=[('member', 'Member Address'), ('department', 'Department Address')],
        required=False,
        default='member',
        help_text="Choose whether to bill to member address or department address"
    )

    def validate(self, data):
        """Validate the payment data based on payment type"""
        payment_for = data.get('payment_for')

        # Validate membership payment data
        if payment_for == Payment.PaymentFor.MEMBERSHIP:
            if not data.get('covered_members') and not data.get('amount'):
                data['covered_members'] = [data.get('payer_id')]

        # Validate event payment data
        elif payment_for == Payment.PaymentFor.EVENT:
            if not data.get('event_id'):
                raise serializers.ValidationError({"event_id": "Event ID is required for event payments"})

            try:
                event = EventRegistration.objects.get(pk=data.get('event_id'))
                # Store event in data for later use in create
                data['event_registration'] = event
            except EventRegistration.DoesNotExist:
                raise serializers.ValidationError({"event_id": "Event registration not found"})

        # Validate payer exists
        try:
            payer = Member.objects.get(pk=data.get('payer_id'))
            # Store payer in data for later use in create
            data['payer'] = payer
        except Member.DoesNotExist:
            raise serializers.ValidationError({"payer_id": "Payer not found"})

        # Set billing address based on bill_to preference
        bill_to = data.get('bill_to', 'member')
        if not data.get('billing_address'):  # Only set if not manually provided
            from payments.services import PaymentService
            data['billing_address'] = PaymentService.get_billing_address(payer, bill_to)

        # Note: total_amount calculation is now handled by PaymentService

        return data


class PaymentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating Payment records"""
    covered_members = serializers.PrimaryKeyRelatedField(
        queryset=Member.objects.all(),
        many=True,
        required=False
    )

    class Meta:
        model = Payment
        fields = [
            'id',
            'payer',
            'covered_members',
            'amount',
            'total_amount',
            'invoice_number',
            'po_number',
            'paid_year',
            'paid_next_year',
            'payment_link',
            'payment_id',
            'payment_date',
            'date',
            'updated',
            'status',
            'notes',
            'payment_type',
            'draft',
            'billing_address',
            'transaction_id',
            'paypal_response',
            'due_date',
            'paypal_order_id',
            'paypal_payer_id',
            'paypal_payment_id',
            'paypal_fee',
            'paypal_payment_status',
            'paypal_payment_method',
            'payment_for',
            'event_registration',
            'history'
        ]
        read_only_fields = ['invoice_number', 'date', 'updated', 'history', 'total_amount']

    def create(self, validated_data):
        """Create payment - logic moved to PaymentService for better maintainability"""
        # Note: This serializer is kept for backward compatibility
        # New payment creation should use PaymentService directly
        covered_members = validated_data.pop('covered_members', [])

        # Create the payment
        payment = Payment.objects.create(**validated_data)

        # Set covered members
        if covered_members:
            payment.covered_members.set(covered_members)
        else:
            # If no covered members specified, default to the payer
            payment.covered_members.add(validated_data['payer'])

        # Update total amount using service
        from payments.services import PaymentService
        PaymentService.update_total_amount(payment)

        return payment

    def update(self, instance, validated_data):
        """Update payment - simplified to use service layer"""
        covered_members = validated_data.pop('covered_members', None)

        # Update the payment fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Save the instance
        instance.save()

        # Update covered members if provided
        if covered_members is not None:
            instance.covered_members.set(covered_members)

        # Update total amount using service
        from payments.services import PaymentService
        PaymentService.update_total_amount(instance)

        return instance


class PayPalCaptureSerializer(serializers.Serializer):
    """Serializer for capturing PayPal payments after approval"""
    order_id = serializers.CharField(required=True)
    payment_id = serializers.IntegerField(required=True)

    def validate(self, data):
        """Validate the payment exists"""
        try:
            payment = Payment.objects.get(pk=data.get('payment_id'))
            # Store payment in data for later use
            data['payment'] = payment
        except Payment.DoesNotExist:
            raise serializers.ValidationError({"payment_id": "Payment not found"})

        return data


class EventRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for EventRegistration model"""
    member_details = MemberSerializer(source='member', read_only=True)
    group_members_details = MemberSerializer(source='group_members', many=True, read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)

    # These fields can be calculated or provided
    base_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    guest_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)

    class Meta:
        model = EventRegistration
        fields = [
            'id', 'member', 'member_details', 'first_name', 'last_name', 'title',
            'fire_department', 'address', 'city', 'state', 'zipcode', 'phone',
            'email', 'invoice_number', 'registration_type', 'number_of_participants',
            'number_of_guests', 'registration_date', 'notes', 'base_amount',
            'guest_amount', 'total_amount', 'payment_status', 'extra_participants',
            'group_registration', 'group_members', 'group_members_details', 'payments',
            'event'
        ]
        read_only_fields = ['invoice_number', 'registration_date']

    def validate(self, data):
        """Ensure base_amount, guest_amount, and total_amount are provided"""
        # If base_amount is not provided, calculate it based on registration type
        if 'base_amount' not in data:
            registration_type = data.get('registration_type')
            if data.get('group_registration', False):
                # Group registration rate
                num_participants = data.get('number_of_participants', 1)
                data['base_amount'] = float(PaymentConfig.GROUP_REGISTRATION_FEE) * num_participants
            elif registration_type == 'LATE':
                # Late registration rate
                data['base_amount'] = float(PaymentConfig.LATE_REGISTRATION_FEE)
            elif registration_type == 'NORMAL':
                # Normal registration rate
                data['base_amount'] = float(PaymentConfig.DEFAULT_EVENT_REGISTRATION_FEE)
            else:
                data['base_amount'] = 0.00

        # If guest_amount is not provided, calculate it based on number of guests
        if 'guest_amount' not in data:
            num_guests = data.get('number_of_guests', 0)
            data['guest_amount'] = float(PaymentConfig.GUEST_FEE) * num_guests

        # If total_amount is not provided, calculate it from base_amount and guest_amount
        if 'total_amount' not in data:
            data['total_amount'] = data['base_amount'] + data['guest_amount']

        return data

    def create(self, validated_data):
        """Create event registration with group members if provided"""
        group_members = validated_data.pop('group_members', [])

        # Create the event registration
        event_registration = EventRegistration.objects.create(**validated_data)

        # Add group members if this is a group registration
        if validated_data.get('group_registration', False) and group_members:
            event_registration.group_members.set(group_members)

        return event_registration

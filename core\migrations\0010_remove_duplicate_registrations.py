from django.db import migrations


def remove_duplicate_registrations(apps, schema_editor):
    """
    Remove duplicate event registrations before adding the unique constraint.
    """
    EventRegistration = apps.get_model('core', 'EventRegistration')

    # Get all registrations
    registrations = EventRegistration.objects.all()

    # Track seen (event_id, member_id) pairs
    seen_pairs = set()
    duplicates_to_delete = []

    for registration in registrations:
        # Skip if either event or member is None
        if registration.event_id is None or registration.member_id is None:
            continue

        # Create a unique key for this registration
        key = (registration.event_id, registration.member_id)

        # If we've seen this pair before, mark for deletion
        if key in seen_pairs:
            duplicates_to_delete.append(registration.id)
        else:
            seen_pairs.add(key)

    # Delete duplicates
    if duplicates_to_delete:
        EventRegistration.objects.filter(id__in=duplicates_to_delete).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_alter_department_options_and_more'),
    ]

    operations = [
        migrations.RunPython(remove_duplicate_registrations, migrations.RunPython.noop),
    ]

from django.test import TransactionTestCase
from django.db import models, connection
from django_filters import rest_framework as filters
from unittest.mock import patch

from common.filters.base_filters import BaseBooleanFilterSet


# Create a simple model for testing
class TestModel(models.Model):
    name = models.CharField(max_length=50)
    active = models.BooleanField(default=True)
    verified = models.BooleanField(default=False)

    class Meta:
        app_label = 'common'  # For testing only
        managed = True  # Add this to create the table during test setup


# Create a filter class using BaseBooleanFilterSet
class TestModelFilter(BaseBooleanFilterSet):
    active = filters.CharFilter(method='filter_boolean_field')
    verified = filters.CharFilter(method='filter_boolean_field')

    class Meta:
        model = TestModel
        fields = ['active', 'verified']


class BaseBooleanFilterSetTest(TransactionTestCase):
    """Test cases for BaseBooleanFilterSet"""
    
    @classmethod
    def setUpClass(cls):
        """Set up the test class by creating tables"""
        super().setUpClass()
        # Create the test model table in the database
        with connection.schema_editor() as schema_editor:
            schema_editor.create_model(TestModel)
    
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def setUp(self, mock_print, mock_warn):
        # Create test objects with different boolean combinations
        self.active_verified = TestModel.objects.create(
            name='Active and Verified',
            active=True,
            verified=True
        )
        self.active_not_verified = TestModel.objects.create(
            name='Active but not Verified',
            active=True,
            verified=False
        )
        self.not_active_verified = TestModel.objects.create(
            name='Not Active but Verified',
            active=False,
            verified=True
        )
        self.not_active_not_verified = TestModel.objects.create(
            name='Not Active and not Verified',
            active=False,
            verified=False
        )
        
        self.filter_class = TestModelFilter
        
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def test_filter_boolean_field_yes(self, mock_print, mock_warn):
        """Test filtering with 'yes' value"""
        queryset = TestModel.objects.all()
        filter_instance = self.filter_class({'active': 'yes'}, queryset)
        
        filtered = filter_instance.qs
        
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.active_verified, filtered)
        self.assertIn(self.active_not_verified, filtered)
        self.assertNotIn(self.not_active_verified, filtered)
        self.assertNotIn(self.not_active_not_verified, filtered)
        
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def test_filter_boolean_field_no(self, mock_print, mock_warn):
        """Test filtering with 'no' value"""
        queryset = TestModel.objects.all()
        filter_instance = self.filter_class({'active': 'no'}, queryset)
        
        filtered = filter_instance.qs
        
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.not_active_verified, filtered)
        self.assertIn(self.not_active_not_verified, filtered)
        self.assertNotIn(self.active_verified, filtered)
        self.assertNotIn(self.active_not_verified, filtered)
        
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def test_filter_boolean_field_all(self, mock_print, mock_warn):
        """Test filtering with 'all' value"""
        queryset = TestModel.objects.all()
        filter_instance = self.filter_class({'active': 'all'}, queryset)
        
        filtered = filter_instance.qs
        
        self.assertEqual(filtered.count(), 4)  # All records
        
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def test_filter_boolean_field_invalid(self, mock_print, mock_warn):
        """Test filtering with invalid value"""
        queryset = TestModel.objects.all()
        filter_instance = self.filter_class({'active': 'invalid'}, queryset)
        
        filtered = filter_instance.qs
        
        # Should treat as 'all' and issue a warning
        self.assertEqual(filtered.count(), 4)  # All records
        mock_warn.assert_called_once()  # Warning should be issued
        
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def test_filter_boolean_field_boolean_true(self, mock_print, mock_warn):
        """Test filtering with boolean True value"""
        queryset = TestModel.objects.all()
        
        # Create filter instance manually to pass True instead of string
        filter_instance = TestModelFilter()
        filter_instance.queryset = queryset
        
        # Call filter method directly with a boolean
        filtered = filter_instance.filter_boolean_field(queryset, 'active', True)
        
        self.assertEqual(filtered.count(), 2)
        self.assertIn(self.active_verified, filtered)
        self.assertIn(self.active_not_verified, filtered)
        
    @patch('common.filters.base_filters.warnings.warn')
    @patch('common.filters.base_filters.print')  # Mock print statements
    def test_multiple_filters(self, mock_print, mock_warn):
        """Test applying multiple boolean filters"""
        queryset = TestModel.objects.all()
        filter_instance = self.filter_class({
            'active': 'yes',
            'verified': 'yes'
        }, queryset)
        
        filtered = filter_instance.qs
        
        self.assertEqual(filtered.count(), 1)
        self.assertIn(self.active_verified, filtered)
        self.assertNotIn(self.active_not_verified, filtered)
        self.assertNotIn(self.not_active_verified, filtered)
        self.assertNotIn(self.not_active_not_verified, filtered) 

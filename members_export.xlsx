<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <title>Page not found at /api/core/admin/members/export/</title>
  <meta name="robots" content="NONE,NOARCHIVE">
  <style>
    html * { padding:0; margin:0; }
    body * { padding:10px 20px; }
    body * * { padding:0; }
    body { font-family: sans-serif; background:#eee; color:#000; }
    body > :where(header, main, footer) { border-bottom:1px solid #ddd; }
    h1 { font-weight:normal; margin-bottom:.4em; }
    h1 small { font-size:60%; color:#666; font-weight:normal; }
    table { border:none; border-collapse: collapse; width:100%; }
    td, th { vertical-align:top; padding:2px 3px; }
    th { width:12em; text-align:right; color:#666; padding-right:.5em; }
    #info { background:#f6f6f6; }
    #info ol { margin: 0.5em 4em; }
    #info ol li { font-family: monospace; }
    #summary { background: #ffc; }
    #explanation { background:#eee; border-bottom: 0px none; }
    pre.exception_value { font-family: sans-serif; color: #575757; font-size: 1.5em; margin: 10px 0 10px 0; }
  </style>
</head>
<body>
  <header id="summary">
    <h1>Page not found <small>(404)</small></h1>
    
    <table class="meta">
      <tr>
        <th scope="row">Request Method:</th>
        <td>GET</td>
      </tr>
      <tr>
        <th scope="row">Request URL:</th>
        <td>http://127.0.0.1:8000/api/core/admin/members/export/</td>
      </tr>
      
    </table>
  </header>

  <main id="info">
    
      <p>
      Using the URLconf defined in <code>mffa_backend.urls</code>,
      Django tried these URL patterns, in this order:
      </p>
      <ol>
        
          <li>
            
              <code>
                admin/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                api/
                
              </code>
            
              <code>
                auth/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                api/
                
              </code>
            
              <code>
                admin/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                api/
                
              </code>
            
              <code>
                public/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                api/payments/
                
              </code>
            
          </li>
        
      </ol>
      <p>
        
          The current path, <code>api/core/admin/members/export/</code>,
        
        didn’t match any of these.
      </p>
    
  </main>

  <footer id="explanation">
    <p>
      You’re seeing this error because you have <code>DEBUG = True</code> in
      your Django settings file. Change that to <code>False</code>, and Django
      will display a standard 404 page.
    </p>
  </footer>
</body>
</html>

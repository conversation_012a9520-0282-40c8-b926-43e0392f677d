"""
Tests for the email templates in the common app.

This module contains tests for all email templates,
verifying that they render correctly with various contexts.
"""
from django.test import TestCase
from django.template.loader import render_to_string
from django.conf import settings


class VerifyEmailTemplateTests(TestCase):
    """Test cases for the verify_email.html template."""

    def test_verify_email_template_renders_correctly(self):
        """Test that the verify_email.html template renders correctly with all required variables."""
        context = {
            'name': 'Test User',
            'verification_url': 'https://example.com/verify/abc123',
            'expiry_days': 30,
        }
        
        # Render the template
        rendered = render_to_string('emails/verify_email.html', context)
        
        # Check that all context variables appear in the rendered template
        self.assertIn('Test User', rendered)
        self.assertIn('https://example.com/verify/abc123', rendered)
        self.assertIn('30 days', rendered)
        
        # Check that essential HTML elements are present
        self.assertIn('<html', rendered)
        self.assertIn('</html>', rendered)
        self.assertIn('<body', rendered)
        self.assertIn('</body>', rendered)
        self.assertIn('Verify Your Email Address', rendered)
        self.assertIn('Verify Email', rendered)
        
        # Check for key instructions in the email
        self.assertIn('To complete your registration', rendered)
        self.assertIn('This link will expire', rendered)
        self.assertIn('If you did not create an account', rendered)

    def test_verify_email_template_handles_edge_cases(self):
        """Test that the verify_email.html template handles edge cases like special characters."""
        context = {
            'name': 'User with <script>alert("XSS")</script>',
            'verification_url': 'https://example.com/verify/abc123?param=value&other=value',
            'expiry_days': 1,
        }
        
        # Render the template
        rendered = render_to_string('emails/verify_email.html', context)
        
        # Check that HTML is properly escaped (no XSS vulnerabilities)
        self.assertIn('User with &lt;script&gt;alert(&quot;XSS&quot;)&lt;/script&gt;', rendered)
        
        # Check that URL with special characters is preserved
        self.assertIn('https://example.com/verify/abc123?param=value&amp;other=value', rendered)
        
        # Check singular day handling
        self.assertIn('1 days', rendered)  # Template should handle this gracefully


class ResetPasswordTemplateTests(TestCase):
    """Test cases for the reset_password.html template."""

    def test_reset_password_template_renders_correctly(self):
        """Test that the reset_password.html template renders correctly with all required variables."""
        context = {
            'name': 'Test User',
            'reset_url': 'https://example.com/reset/abc123',
            'expiry_hours': 24,
        }
        
        # Render the template
        rendered = render_to_string('emails/reset_password.html', context)
        
        # Check that all context variables appear in the rendered template
        self.assertIn('Test User', rendered)
        self.assertIn('https://example.com/reset/abc123', rendered)
        self.assertIn('24 hours', rendered)
        
        # Check that essential HTML elements are present
        self.assertIn('<html', rendered)
        self.assertIn('</html>', rendered)
        self.assertIn('<body', rendered)
        self.assertIn('</body>', rendered)
        self.assertIn('Reset Your Password', rendered)
        self.assertIn('Reset Password', rendered)
        
        # Check for key instructions in the email
        self.assertIn('We received a request to reset your password', rendered)
        self.assertIn('If you didn\'t make this request', rendered)
        self.assertIn('This link will expire', rendered)

    def test_reset_password_template_handles_edge_cases(self):
        """Test that the reset_password.html template handles edge cases like special characters."""
        context = {
            'name': 'User with <script>alert("XSS")</script>',
            'reset_url': 'https://example.com/reset/abc123?token=123456&email=<EMAIL>',
            'expiry_hours': 1,
        }
        
        # Render the template
        rendered = render_to_string('emails/reset_password.html', context)
        
        # Check that HTML is properly escaped (no XSS vulnerabilities)
        self.assertIn('User with &lt;script&gt;alert(&quot;XSS&quot;)&lt;/script&gt;', rendered)
        
        # Check that URL with special characters is preserved
        self.assertIn('https://example.com/reset/abc123?token=123456&amp;email=<EMAIL>', rendered)
        
        # Check singular hour handling
        self.assertIn('1 hours', rendered)  # Template should handle this gracefully 
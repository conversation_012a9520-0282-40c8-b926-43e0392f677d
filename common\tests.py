from django.test import TestCase

# Import test modules from tests package
from common.tests.test_models import (
    EmailVerificationModelTest,
    PasswordResetModelTest,
    UserActivityModelTest
)
from common.tests.test_utils import (
    EmailUtilsTest,
    ActivityTrackingTest,
    PDFGeneratorTest,
    PrintLabelsTest
)
from common.tests.test_views import (
    APIResponseTest,
    BaseAPIViewTest,
    EmailVerificationViewTest,
    ResendVerificationEmailViewTest,
    RequestPasswordResetViewTest,
    ValidatePasswordResetTokenViewTest,
    PasswordResetConfirmViewTest
)
from common.tests.test_integration import (
    EmailVerificationFlowTest,
    PasswordResetFlowTest
)
from common.tests.test_serializers import (
    EmailVerificationSerializerTest
)
from common.tests.test_permissions import (
    IsStaffUserTest
)
from common.tests.test_pagination import (
    StandardPaginationTest
)
from common.tests.test_filters import (
    BaseBooleanFilterSetTest
)
from common.tests.test_exception_handlers import (
    FormatErrorMessageTest,
    CustomExceptionHandlerTest
)

# Create your tests here.

"""
Test views for PDF generation.
"""
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny


class TestPdfView(APIView):
    """
    View for testing PDF generation.
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """
        Generate a simple PDF response.
        """
        # Create a simple PDF response
        response = HttpResponse(b'%PDF-1.4\n1 0 obj\n<< /Type /Catalog >>\nendobj\ntrailer\n<< /Root 1 0 R >>\n%%EOF', content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=test.pdf'
        return response

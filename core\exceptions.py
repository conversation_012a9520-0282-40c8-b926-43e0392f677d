"""
Custom exceptions for the core app.
"""
from rest_framework.exceptions import APIException
from rest_framework import status


class CustomAPIException(APIException):
    """
    Custom API exception with standardized format.
    """
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "An error occurred"
    default_code = "error"

    def __init__(self, detail=None, code=None, status_code=None):
        if status_code is not None:
            self.status_code = status_code
        super().__init__(detail, code)

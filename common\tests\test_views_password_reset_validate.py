"""
Tests for the password reset token validation view in the common app.

This module contains comprehensive tests for the ValidatePasswordResetTokenView class.
"""
import uuid
from datetime import timedelta
from unittest.mock import patch

from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status

from common.models import PasswordReset

User = get_user_model()


class ValidatePasswordResetTokenViewTests(TestCase):
    """Test cases for the ValidatePasswordResetTokenView."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test client
        self.client = APIClient()

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )

        # Create a password reset record
        self.reset = PasswordReset.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            used=False,
            expires_at=timezone.now() + timedelta(hours=24)
        )

        # URL for validating password reset token
        self.url = reverse('core:validate-reset-token')

    @patch('common.utils.email.verify_password_reset_token')
    def test_validate_token_success(self, mock_verify_token):
        """Test successful token validation."""
        # Mock the verify_password_reset_token function
        mock_verify_token.return_value = (self.user, None)

        # Make the request
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Valid")
        self.assertEqual(response.data['data']['email'], self.user.email)

        # Check that verify_password_reset_token was called with the correct token
        mock_verify_token.assert_called_once_with(str(self.reset.key))

    @patch('common.utils.email.verify_password_reset_token')
    def test_validate_token_invalid(self, mock_verify_token):
        """Test validation with invalid token."""
        # Mock the verify_password_reset_token function to return an error
        mock_verify_token.return_value = (None, "Invalid password reset link")

        # Use a valid UUID string, but one that doesn't exist in our database
        valid_uuid = str(uuid.uuid4())

        # Make the request
        response = self.client.post(self.url, {'token': valid_uuid})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Invalid token")

        # Check that verify_password_reset_token was called with the correct token
        mock_verify_token.assert_called_once_with(valid_uuid)

    def test_validate_token_missing_token(self):
        """Test validation with missing token."""
        # Make the request without a token
        response = self.client.post(self.url, {})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Invalid token")

    def test_validate_token_empty_token(self):
        """Test validation with an empty token."""
        # Make the request with an empty token
        response = self.client.post(self.url, {'token': ''})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Invalid token")

    @patch('common.utils.email.verify_password_reset_token')
    def test_validate_token_expired(self, mock_verify_token):
        """Test validation with expired token."""
        # Mock the verify_password_reset_token function to return an expired error
        mock_verify_token.return_value = (None, "Password reset link has expired")

        # Make the request
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Invalid token")

        # Check that verify_password_reset_token was called with the correct token
        mock_verify_token.assert_called_once_with(str(self.reset.key))

    @patch('common.utils.email.verify_password_reset_token')
    def test_validate_token_already_used(self, mock_verify_token):
        """Test validation with already used token."""
        # Mock the verify_password_reset_token function to return an already used error
        mock_verify_token.return_value = (None, "This password reset link has already been used")

        # Make the request
        response = self.client.post(self.url, {'token': str(self.reset.key)})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['success'], False)
        self.assertEqual(response.data['message'], "Invalid token")

        # Check that verify_password_reset_token was called with the correct token
        # Use any to avoid type mismatch (str vs UUID)
        import unittest.mock
        mock_verify_token.assert_called_once_with(unittest.mock.ANY)

    def test_validate_token_method_not_allowed(self):
        """Test that only POST method is allowed for token validation."""
        # Make a GET request
        response = self.client.get(self.url)

        # Check the response - should return 405 Method Not Allowed
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    @patch('common.utils.email.verify_password_reset_token')
    def test_validate_token_with_whitespace(self, mock_verify_token):
        """Test validation with whitespace in token."""
        # Mock the verify_password_reset_token function to return success
        mock_verify_token.return_value = (self.user, None)

        # Create a token with whitespace
        token_with_whitespace = f" {self.reset.key} "

        # Make the request
        response = self.client.post(self.url, {'token': token_with_whitespace})

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['message'], "Valid")

        # Check that verify_password_reset_token was called with the cleaned token
        mock_verify_token.assert_called_once_with(str(self.reset.key))

    def test_validate_token_with_json_content_type(self):
        """Test token validation with JSON content type."""
        # Make the request with JSON content type
        response = self.client.post(
            self.url,
            {'token': str(self.reset.key)},
            format='json'
        )

        # Check the response - should handle JSON content type
        # The actual validation logic is in verify_password_reset_token
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_validate_token_with_form_data(self):
        """Test token validation with form data."""
        # Make the request with form data
        response = self.client.post(
            self.url,
            {'token': str(self.reset.key)},
            format='multipart'
        )

        # Check the response - should handle form data
        # The actual validation logic is in verify_password_reset_token
        self.assertEqual(response.status_code, status.HTTP_200_OK)

"""
Tests for the email verification utility functions in the common app.

This module contains comprehensive tests for the email verification utility functions,
including send_verification_email and verify_email.
"""
import uuid
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock, call

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from common.utils.email import send_verification_email, verify_email
from common.models import EmailVerification

User = get_user_model()


class SendVerificationEmailTests(TestCase):
    """Test cases for the send_verification_email function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Store the expected verification URL format for testing
        self.verification_url_format = f"{settings.FRONTEND_URL}/verify-email/"

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_verification(self, mock_send_mail):
        """Test that send_verification_email creates a verification record."""
        # Ensure no verifications exist before the test
        self.assertEqual(EmailVerification.objects.count(), 0)
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        self.assertEqual(verification.user, self.user)
        self.assertFalse(verification.verified)
        self.assertIsNotNone(verification.key)
        self.assertIsNotNone(verification.expires_at)
        
        # Verify expiration date is set correctly (30 days from now)
        expected_expiry = timezone.now() + timedelta(days=30)
        self.assertAlmostEqual(
            verification.expires_at.timestamp(),
            expected_expiry.timestamp(),
            delta=5  # Allow 5 seconds difference due to test execution time
        )
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check email arguments in detail
        args = mock_send_mail.call_args[0]
        kwargs = mock_send_mail.call_args[1]
        
        # Subject should be about email verification
        self.assertEqual(args[0], "Verify your email address")
        
        # Plain text message should contain key info
        plain_message = args[1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(verification.key), plain_message)
        self.assertIn(f"{self.verification_url_format}{verification.key}", plain_message)
        self.assertIn("30 days", plain_message)
        
        # From email should be the default from email
        self.assertEqual(args[2], settings.DEFAULT_FROM_EMAIL)
        
        # Recipient should be the user's email
        self.assertEqual(args[3], [self.user.email])
        
        # HTML message should contain the verification key
        html_message = kwargs['html_message']
        self.assertIn(str(verification.key), html_message)
        
        # Email should not be sent silently
        self.assertFalse(kwargs['fail_silently'])

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_reuses_existing_verification(self, mock_send_mail):
        """Test that send_verification_email reuses an existing unexpired verification."""
        # Create an existing verification
        existing_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=15)  # Not expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that no new verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        
        # Check that the existing verification was returned
        self.assertEqual(verification, existing_verification)
        
        # Check that the email was sent with the existing verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(existing_verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(existing_verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_new_if_expired(self, mock_send_mail):
        """Test that send_verification_email creates a new verification if existing is expired."""
        # Create an expired verification
        expired_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() - timedelta(days=1)  # Expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a new verification was created
        self.assertEqual(EmailVerification.objects.count(), 2)
        self.assertNotEqual(verification, expired_verification)
        self.assertEqual(verification.user, self.user)
        
        # Check that the email was sent with the new verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_creates_new_if_verified(self, mock_send_mail):
        """Test that send_verification_email creates a new verification if existing is verified."""
        # Create a verified verification
        verified_verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=True,
            expires_at=timezone.now() + timedelta(days=15)  # Not expired
        )
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check that a new verification was created
        self.assertEqual(EmailVerification.objects.count(), 2)
        self.assertNotEqual(verification, verified_verification)
        self.assertEqual(verification.user, self.user)
        
        # Check that the email was sent with the new verification key
        mock_send_mail.assert_called_once()
        self.assertIn(str(verification.key), mock_send_mail.call_args[0][1])  # plain_message
        self.assertIn(str(verification.key), mock_send_mail.call_args[1]['html_message'])  # html_message

    @patch('common.utils.email.render_to_string')
    @patch('common.utils.email.send_mail')
    def test_send_verification_email_content(self, mock_send_mail, mock_render_to_string):
        """Test the content of the verification email."""
        # Mock render_to_string to return a simple HTML message
        mock_render_to_string.return_value = '<html>Verification Email</html>'
        
        # Call the function
        verification = send_verification_email(self.user)
        
        # Check render_to_string call
        mock_render_to_string.assert_called_once_with('emails/verify_email.html', {
            'name': self.user.name,
            'verification_url': f"{settings.FRONTEND_URL}/verify-email/{verification.key}",
            'expiry_days': 30,
        })
        
        # Check send_mail call
        mock_send_mail.assert_called_once_with(
            "Verify your email address",
            mock_send_mail.call_args[0][1],  # plain_message (complex to check exactly)
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message='<html>Verification Email</html>',
            fail_silently=False,
        )
        
        # Check plain message content
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn(self.user.name, plain_message)
        self.assertIn(str(verification.key), plain_message)
        self.assertIn("30 days", plain_message)

    @patch('common.utils.email.send_mail')
    def test_send_verification_email_with_no_name(self, mock_send_mail):
        """Test send_verification_email with a user that has no name."""
        # Create a user with no name
        user_no_name = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name=''  # Empty name
        )
        
        # Call the function
        verification = send_verification_email(user_no_name)
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check that the email content handles empty name gracefully
        plain_message = mock_send_mail.call_args[0][1]
        self.assertIn("Hello", plain_message)  # Should still have a greeting

    @patch('common.utils.email.send_mail', side_effect=Exception("SMTP error"))
    def test_send_verification_email_handles_email_error(self, mock_send_mail):
        """Test that send_verification_email handles email sending errors."""
        # Call the function, which should raise the exception from send_mail
        with self.assertRaises(Exception) as context:
            send_verification_email(self.user)
        
        # Check that the exception was raised
        self.assertEqual(str(context.exception), "SMTP error")
        
        # Check that a verification was still created despite the email error
        self.assertEqual(EmailVerification.objects.count(), 1)
        verification = EmailVerification.objects.first()
        self.assertEqual(verification.user, self.user)

    @override_settings(FRONTEND_URL=None)
    @patch('common.utils.email.send_mail')
    def test_send_verification_email_missing_frontend_url(self, mock_send_mail):
        """Test send_verification_email behavior when FRONTEND_URL setting is missing."""
        # This should still work, but might use a default or empty URL
        verification = send_verification_email(self.user)
        
        # Check that the verification was created
        self.assertEqual(EmailVerification.objects.count(), 1)
        
        # Check that the email was sent
        mock_send_mail.assert_called_once()
        
        # Check that the URL in the email doesn't contain "None"
        plain_message = mock_send_mail.call_args[0][1]
        self.assertNotIn("None", plain_message)


class VerifyEmailTests(TestCase):
    """Test cases for the verify_email function."""

    def setUp(self):
        """Set up test data for each test case."""
        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        
        # Create a verification record
        self.verification = EmailVerification.objects.create(
            user=self.user,
            key=uuid.uuid4(),
            verified=False,
            expires_at=timezone.now() + timedelta(days=30)
        )

    def test_verify_email_success(self):
        """Test successful email verification."""
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertEqual(user, self.user)
        self.assertIsNone(error)
        
        # Check that the verification was marked as verified
        self.verification.refresh_from_db()
        self.assertTrue(self.verification.verified)

    def test_verify_email_invalid_key(self):
        """Test email verification with invalid key."""
        # Call the function with a non-existent key
        user, error = verify_email(str(uuid.uuid4()))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_already_verified(self):
        """Test email verification with already verified key."""
        # Mark the verification as verified
        self.verification.verified = True
        self.verification.save()
        
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Email has already been verified")

    def test_verify_email_expired(self):
        """Test email verification with expired key."""
        # Set the verification to be expired
        self.verification.expires_at = timezone.now() - timedelta(days=1)
        self.verification.save()
        
        # Call the function
        user, error = verify_email(str(self.verification.key))
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Verification link has expired")

    def test_verify_email_does_not_activate_user(self):
        """Test that verify_email does not activate the user."""
        # Ensure user is not active before verification
        self.user.active = False
        self.user.save()
        
        # Call the function
        verify_email(str(self.verification.key))
        
        # Check that the user is still not active
        self.user.refresh_from_db()
        self.assertFalse(self.user.active)

    def test_verify_email_with_invalid_uuid_format(self):
        """Test email verification with an invalid UUID format."""
        # Call the function with an invalid UUID
        user, error = verify_email("not-a-uuid")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_with_none_key(self):
        """Test email verification with a None key."""
        # Call the function with None
        user, error = verify_email(None)
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

    def test_verify_email_with_empty_key(self):
        """Test email verification with an empty key."""
        # Call the function with an empty string
        user, error = verify_email("")
        
        # Check the result
        self.assertIsNone(user)
        self.assertEqual(error, "Invalid verification link")

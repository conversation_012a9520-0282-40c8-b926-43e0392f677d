"""
Debug views for the core app.
"""
from django.urls import get_resolver
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny


class UrlsDebugView(APIView):
    """
    View for debugging URL patterns.
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """
        Get all URL patterns in the application.
        """
        resolver = get_resolver()
        url_patterns = self._get_url_patterns(resolver.url_patterns)
        return Response(url_patterns, status=status.HTTP_200_OK)

    def _get_url_patterns(self, patterns, prefix=''):
        """
        Recursively get all URL patterns.
        """
        result = []
        for pattern in patterns:
            if hasattr(pattern, 'url_patterns'):
                # This is an include, recurse into it
                result.extend(self._get_url_patterns(pattern.url_patterns, prefix + str(pattern.pattern)))
            else:
                # This is a URL pattern
                name = getattr(pattern, 'name', None)
                pattern_str = prefix + str(pattern.pattern)
                result.append({
                    'pattern': pattern_str,
                    'name': name,
                    'callback': str(pattern.callback.__name__) if hasattr(pattern.callback, '__name__') else str(pattern.callback),
                })
        return result

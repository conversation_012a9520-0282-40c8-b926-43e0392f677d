"""
Test cases for authentication serializers.
"""
import json
from unittest.mock import patch, Mock

from django.test import TestCase, RequestFactory
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.conf import settings
from datetime import timed<PERSON><PERSON>
from rest_framework.test import APITestCase
from rest_framework.exceptions import ValidationError
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken

from common.models import EmailVerification
from core.models import Member, Department
from core.serializers.auth.auth_serializers import (
    LoginSerializer,
    TokenRefreshSerializer,
    ChangePasswordSerializer,
    AdminChangePasswordSerializer,
    UserDetailsSerializer,
    UnverifiedUserListSerializer
)

Member = get_user_model()


class LoginSerializerTests(APITestCase):
    """Test cases for the LoginSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a department
        self.department = Department.objects.create(
            name='Test Department',
            department_city='Test City',
            department_state='MS'
        )

        # Create a staff user with verified email
        self.staff_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Staff User',
            department=self.department,
            is_staff=True,
            active=True
        )

        # Create email verification for staff user
        self.staff_verification = EmailVerification.objects.create(
            user=self.staff_user,
            verified=True
        )

        # Create a regular user with verified email
        self.regular_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Regular User',
            department=self.department,
            is_staff=False,
            active=True
        )

        # Create email verification for regular user
        self.regular_verification = EmailVerification.objects.create(
            user=self.regular_user,
            verified=True
        )

        # Create an inactive user with verified email
        self.inactive_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Inactive User',
            department=self.department,
            is_staff=True,
            active=False
        )

        # Create email verification for inactive user
        self.inactive_verification = EmailVerification.objects.create(
            user=self.inactive_user,
            verified=True
        )

        # Create a user with unverified email
        self.unverified_user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Unverified User',
            department=self.department,
            is_staff=True,
            active=True
        )

        # Create unverified email verification
        self.unverified_verification = EmailVerification.objects.create(
            user=self.unverified_user,
            verified=False
        )

        # Create request factory
        self.factory = RequestFactory()
        self.request = self.factory.get('/')

        # Valid login data for staff user
        self.valid_staff_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        # Valid login data for regular user
        self.valid_regular_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        # Login data for user with unverified email
        self.unverified_email_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        # Login data for inactive user
        self.inactive_user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        # Login data with incorrect password
        self.incorrect_password_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }

        # Login data for non-existent user
        self.nonexistent_user_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

    def test_valid_staff_login(self):
        """Test login with valid credentials for a staff user."""
        serializer = LoginSerializer(
            data=self.valid_staff_data,
            context={'request': self.request}
        )

        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data

        # Check that user is returned
        self.assertEqual(validated_data['user'], self.staff_user)

        # Check that tokens are generated
        self.assertIn('access', validated_data)
        self.assertIn('refresh', validated_data)

        # Verify token belongs to the user
        refresh = RefreshToken(validated_data['refresh'])
        self.assertEqual(refresh['user_id'], self.staff_user.id)

    def test_regular_user_login_rejected(self):
        """Test login with regular user is rejected (staff only)."""
        serializer = LoginSerializer(
            data=self.valid_regular_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Only staff members are allowed to login', str(context.exception))

    def test_unverified_email_login_rejected(self):
        """Test login with unverified email is rejected."""
        serializer = LoginSerializer(
            data=self.unverified_email_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Email has not been verified', str(context.exception))

    def test_inactive_user_login_rejected(self):
        """Test login with inactive user is rejected."""
        serializer = LoginSerializer(
            data=self.inactive_user_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('awaiting approval', str(context.exception))

    def test_incorrect_password_rejected(self):
        """Test login with incorrect password is rejected."""
        serializer = LoginSerializer(
            data=self.incorrect_password_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as cm:
            serializer.is_valid(raise_exception=True)
        
        exception_detail = cm.exception.detail
        self.assertIn('non_field_errors', exception_detail)
        self.assertEqual(len(exception_detail['non_field_errors']), 1)
        error_detail_obj = exception_detail['non_field_errors'][0]
        self.assertEqual(str(error_detail_obj), 'Invalid credentials.')
        self.assertEqual(error_detail_obj.code, 'invalid')

    def test_nonexistent_user_rejected(self):
        """Test login with non-existent user is rejected."""
        serializer = LoginSerializer(
            data=self.nonexistent_user_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as cm:
            serializer.is_valid(raise_exception=True)

        exception_detail = cm.exception.detail
        self.assertIn('non_field_errors', exception_detail)
        self.assertEqual(len(exception_detail['non_field_errors']), 1)
        error_detail_obj = exception_detail['non_field_errors'][0]
        self.assertEqual(str(error_detail_obj), 'Invalid credentials.')
        self.assertEqual(error_detail_obj.code, 'invalid')

    def test_missing_email_field(self):
        """Test validation fails when email is missing."""
        data = {'password': 'securepassword123'}

        serializer = LoginSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('email', str(context.exception))

    def test_missing_password_field(self):
        """Test validation fails when password is missing."""
        data = {'email': '<EMAIL>'}

        serializer = LoginSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('password', str(context.exception))

    def test_empty_email_field(self):
        """Test validation fails when email is empty."""
        data = {
            'email': '',
            'password': 'securepassword123'
        }

        serializer = LoginSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('email', str(context.exception))

    def test_empty_password_field(self):
        """Test validation fails when password is empty."""
        data = {
            'email': '<EMAIL>',
            'password': ''
        }

        serializer = LoginSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('password', str(context.exception))

    def test_invalid_email_format(self):
        """Test validation fails with invalid email format."""
        data = {
            'email': 'not_an_email',
            'password': 'securepassword123'
        }

        serializer = LoginSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('email', str(context.exception))

    @patch('core.serializers.auth.auth_serializers.authenticate')
    def test_authentication_failure(self, mock_authenticate):
        """Test serializer validation when authenticate returns None."""
        mock_authenticate.return_value = None # Simulate authentication failure

        serializer = LoginSerializer(
            data=self.valid_staff_data, # Use valid data, but auth will fail
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as cm:
            serializer.is_valid(raise_exception=True)

        exception_detail = cm.exception.detail
        self.assertIn('non_field_errors', exception_detail)
        self.assertEqual(len(exception_detail['non_field_errors']), 1)
        error_detail_obj = exception_detail['non_field_errors'][0]
        self.assertEqual(str(error_detail_obj), 'Invalid credentials.')
        self.assertEqual(error_detail_obj.code, 'invalid')

    def test_case_insensitive_email(self):
        """Test that email is case-insensitive for login."""
        # Use uppercase email
        uppercase_email_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }

        serializer = LoginSerializer(
            data=uppercase_email_data,
            context={'request': self.request}
        )

        # For debugging
        is_valid = serializer.is_valid()
        if not is_valid:
            print(f"Validation errors: {serializer.errors}")

            # Check if the user exists with case-insensitive lookup
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user = User.objects.filter(email__iexact=uppercase_email_data['email']).first()
            print(f"User found with case-insensitive lookup: {user}")

            # Check all users in the database
            all_users = User.objects.all()
            print(f"All users: {[u.email for u in all_users]}")

        self.assertTrue(is_valid)
        self.assertEqual(serializer.validated_data['user'], self.staff_user)


class TokenRefreshSerializerTests(APITestCase):
    """Test cases for the TokenRefreshSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            name='Token Test User',
            is_staff=True,
            active=True
        )

        # Generate a refresh token for the user
        self.refresh = RefreshToken.for_user(self.user)
        self.refresh_token = str(self.refresh)

        # Valid refresh data
        self.valid_refresh_data = {
            'refresh': self.refresh_token
        }

        # Invalid refresh token data
        self.invalid_token_data = {
            'refresh': 'invalid_token'
        }

        # Create an expired token
        self.expired_refresh = RefreshToken.for_user(self.user)
        self.expired_refresh.set_exp(lifetime=-timedelta(days=1))
        self.expired_token = str(self.expired_refresh)

        # Expired token data
        self.expired_token_data = {
            'refresh': self.expired_token
        }

    def test_valid_token_refresh(self):
        """Test refreshing token with valid refresh token."""
        serializer = TokenRefreshSerializer(data=self.valid_refresh_data)

        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data

        # Check that new tokens are generated
        self.assertIn('access', validated_data)
        self.assertIn('refresh', validated_data)

        # In test environment, the token might be the same due to fixed keys
        # Just verify that a refresh token is returned
        self.assertIsNotNone(validated_data['refresh'])

    def test_invalid_token_rejected(self):
        """Test refreshing token with invalid token is rejected."""
        serializer = TokenRefreshSerializer(data=self.invalid_token_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Invalid or expired refresh token', str(context.exception))

    def test_expired_token_rejected(self):
        """Test refreshing token with expired token is rejected."""
        serializer = TokenRefreshSerializer(data=self.expired_token_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Invalid or expired refresh token', str(context.exception))

    def test_missing_refresh_field(self):
        """Test validation fails when refresh field is missing."""
        serializer = TokenRefreshSerializer(data={})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('refresh', str(context.exception))

    def test_empty_refresh_field(self):
        """Test validation fails when refresh field is empty."""
        data = {'refresh': ''}

        serializer = TokenRefreshSerializer(data=data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('refresh', str(context.exception))

    def test_blacklisted_token_rejected(self):
        """Test refreshing token with blacklisted token is rejected."""
        # Blacklist the token
        self.refresh.blacklist()

        serializer = TokenRefreshSerializer(data=self.valid_refresh_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Invalid or expired refresh token', str(context.exception))


class ChangePasswordSerializerTests(APITestCase):
    """Test cases for the ChangePasswordSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='currentpassword123',
            name='Password Test User',
            is_staff=True,
            active=True
        )

        # Create request factory and mock request
        self.factory = RequestFactory()
        self.request = self.factory.get('/')
        self.request.user = self.user

        # Valid change password data
        self.valid_data = {
            'old_password': 'currentpassword123',
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

        # Data with incorrect old password
        self.incorrect_old_password_data = {
            'old_password': 'wrongpassword',
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

        # Data with mismatched passwords
        self.mismatched_passwords_data = {
            'old_password': 'currentpassword123',
            'new_password': 'newpassword123!@#',
            'confirm_password': 'differentpassword123!@#'
        }

        # Data with weak new password
        self.weak_password_data = {
            'old_password': 'currentpassword123',
            'new_password': 'weak',
            'confirm_password': 'weak'
        }

    def test_valid_password_change(self):
        """Test changing password with valid data."""
        serializer = ChangePasswordSerializer(
            data=self.valid_data,
            context={'request': self.request}
        )

        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data

        # Check that all fields are present and correct
        self.assertEqual(validated_data['old_password'], self.valid_data['old_password'])
        self.assertEqual(validated_data['new_password'], self.valid_data['new_password'])
        self.assertEqual(validated_data['confirm_password'], self.valid_data['confirm_password'])

    def test_incorrect_old_password_rejected(self):
        """Test changing password with incorrect old password is rejected."""
        serializer = ChangePasswordSerializer(
            data=self.incorrect_old_password_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Current password is incorrect', str(context.exception))

    def test_mismatched_passwords_rejected(self):
        """Test changing password with mismatched new passwords is rejected."""
        serializer = ChangePasswordSerializer(
            data=self.mismatched_passwords_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Password fields didn\'t match', str(context.exception))

    def test_weak_password_rejected(self):
        """Test changing password with weak new password is rejected."""
        serializer = ChangePasswordSerializer(
            data=self.weak_password_data,
            context={'request': self.request}
        )

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        # Django's password validation error messages can vary
        # Just check that validation error was raised
        self.assertFalse(serializer.is_valid())

    def test_missing_old_password_field(self):
        """Test validation fails when old_password field is missing."""
        data = {
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

        serializer = ChangePasswordSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('old_password', str(context.exception))

    def test_missing_new_password_field(self):
        """Test validation fails when new_password field is missing."""
        data = {
            'old_password': 'currentpassword123',
            'confirm_password': 'newpassword123!@#'
        }

        serializer = ChangePasswordSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('new_password', str(context.exception))

    def test_missing_confirm_password_field(self):
        """Test validation fails when confirm_password field is missing."""
        data = {
            'old_password': 'currentpassword123',
            'new_password': 'newpassword123!@#'
        }

        serializer = ChangePasswordSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('confirm_password', str(context.exception))

    def test_empty_fields_rejected(self):
        """Test validation fails when fields are empty."""
        data = {
            'old_password': '',
            'new_password': '',
            'confirm_password': ''
        }

        serializer = ChangePasswordSerializer(data=data, context={'request': self.request})

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        # Check that all fields are in errors
        errors = str(context.exception)
        self.assertIn('old_password', errors)
        self.assertIn('new_password', errors)

    def test_same_old_and_new_password(self):
        """Test changing password where new password is the same as old password."""
        data = {
            'old_password': 'currentpassword123',
            'new_password': 'currentpassword123',
            'confirm_password': 'currentpassword123'
        }

        serializer = ChangePasswordSerializer(data=data, context={'request': self.request})

        # Django's password validators usually catch this, but it's not a guaranteed error
        # Just check if validation passes or fails
        if serializer.is_valid():
            # If it's valid, that's okay
            pass
        else:
            # If it's invalid, check that the error is about the new password
            self.assertIn('new_password', serializer.errors)


class AdminChangePasswordSerializerTests(APITestCase):
    """Test cases for the AdminChangePasswordSerializer."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = Member.objects.create_user(
            email='<EMAIL>',
            password='currentpassword123',
            name='Admin Password Test User',
            is_staff=True,
            active=True
        )

        # Valid admin change password data
        self.valid_data = {
            'user_id': self.user.id,
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

        # Data with mismatched passwords
        self.mismatched_passwords_data = {
            'user_id': self.user.id,
            'new_password': 'newpassword123!@#',
            'confirm_password': 'differentpassword123!@#'
        }

        # Data with weak new password
        self.weak_password_data = {
            'user_id': self.user.id,
            'new_password': 'weak',
            'confirm_password': 'weak'
        }

        # Data with non-existent user ID
        self.nonexistent_user_data = {
            'user_id': 99999,  # Assuming this ID doesn't exist
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

    def test_valid_admin_password_change(self):
        """Test admin changing password with valid data."""
        serializer = AdminChangePasswordSerializer(data=self.valid_data)

        self.assertTrue(serializer.is_valid())
        validated_data = serializer.validated_data

        # Check that all fields are present and correct
        self.assertEqual(validated_data['user_id'], self.valid_data['user_id'])
        self.assertEqual(validated_data['new_password'], self.valid_data['new_password'])
        self.assertEqual(validated_data['confirm_password'], self.valid_data['confirm_password'])

        # Verify that the user object is included in validated data
        self.assertEqual(validated_data['user'], self.user)

    def test_mismatched_passwords_rejected(self):
        """Test admin changing password with mismatched new passwords is rejected."""
        serializer = AdminChangePasswordSerializer(data=self.mismatched_passwords_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('Password fields didn\'t match', str(context.exception))

    def test_weak_password_rejected(self):
        """Test admin changing password with weak new password is rejected."""
        serializer = AdminChangePasswordSerializer(data=self.weak_password_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        # Django's password validation error messages can vary
        # Just check that validation error was raised
        self.assertFalse(serializer.is_valid())

    def test_nonexistent_user_rejected(self):
        """Test admin changing password for non-existent user is rejected."""
        serializer = AdminChangePasswordSerializer(data=self.nonexistent_user_data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('User with this ID does not exist', str(context.exception))

    def test_missing_user_id_field(self):
        """Test validation fails when user_id field is missing."""
        data = {
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

        serializer = AdminChangePasswordSerializer(data=data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('user_id', str(context.exception))

    def test_missing_new_password_field(self):
        """Test validation fails when new_password field is missing."""
        data = {
            'user_id': self.user.id,
            'confirm_password': 'newpassword123!@#'
        }

        serializer = AdminChangePasswordSerializer(data=data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('new_password', str(context.exception))

    def test_missing_confirm_password_field(self):
        """Test validation fails when confirm_password field is missing."""
        data = {
            'user_id': self.user.id,
            'new_password': 'newpassword123!@#'
        }

        serializer = AdminChangePasswordSerializer(data=data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('confirm_password', str(context.exception))

    def test_invalid_user_id_format(self):
        """Test validation fails when user_id is not an integer."""
        data = {
            'user_id': 'not_an_integer',
            'new_password': 'newpassword123!@#',
            'confirm_password': 'newpassword123!@#'
        }

        serializer = AdminChangePasswordSerializer(data=data)

        with self.assertRaises(ValidationError) as context:
            serializer.is_valid(raise_exception=True)

        self.assertIn('user_id', str(context.exception))
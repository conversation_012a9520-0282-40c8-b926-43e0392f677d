"""
Tests for the common app activity tracking utilities.
"""
from unittest.mock import patch, <PERSON><PERSON>ock, call
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response

from common.utils.activity_tracking import track_activity
from common.models import UserActivity

User = get_user_model()


class TrackActivityDecoratorTests(TestCase):
    """Test cases for the track_activity decorator."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            name='Test User'
        )
        # Clear any existing activities before each test
        UserActivity.objects.all().delete()

    def tearDown(self):
        """Clean up after each test."""
        # Clean up all user activities
        UserActivity.objects.all().delete()

    def test_track_activity_function_based_view(self):
        """Test track_activity decorator on a function-based view."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(self, request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view(self, request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_class_based_view(self):
        """Test track_activity decorator on a class-based view method."""
        # Define a class-based view with the decorator
        class TestView:
            @track_activity("User viewed test page")
            def get(self, request):
                return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Create an instance of the view and call the method
        view = TestView()
        response = view.get(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed test page")

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_api_view(self):
        """Test track_activity decorator on an APIView method."""
        # Define an APIView with the decorator
        class TestAPIView(APIView):
            @track_activity("User viewed API test page")
            def get(self, request):
                return Response({"message": "Test response"})

        # Create a request with an authenticated user
        request = self.factory.get('/api/test/')
        request.user = self.user

        # Create an instance of the view and call the method
        view = TestAPIView()
        response = view.get(request)

        # Check that the activity was recorded
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "User viewed API test page")

        # Check that the response was returned correctly
        self.assertEqual(response.data, {"message": "Test response"})

    def test_track_activity_without_description(self):
        """Test track_activity decorator without a description (uses function name)."""
        # Define a function-based view with the decorator without description
        @track_activity()
        def test_view_no_description(self, request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call the view
        response = test_view_no_description(self, request)

        # Check that the activity was recorded with function name as description
        self.assertEqual(UserActivity.objects.count(), 1)
        activity = UserActivity.objects.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.description, "test_view_no_description")

    def test_track_activity_unauthenticated_user(self):
        """Test track_activity decorator with an unauthenticated user."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(self, request):
            return HttpResponse("Test response")

        # Create a fresh request with an unauthenticated user
        request = self.factory.get('/test/')
        # Set user to None explicitly
        request.user = None

        # Call the view
        response = test_view(self, request)

        # Check that no activity was recorded
        self.assertEqual(UserActivity.objects.count(), 0)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_anonymous_user(self):
        """Test track_activity decorator with an anonymous user."""
        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(self, request):
            return HttpResponse("Test response")

        # Create a fresh request with an anonymous user
        request = self.factory.get('/test/')
        
        # Create a proper mock with is_authenticated=False
        mock_user = MagicMock()
        mock_user.is_authenticated = False
        request.user = mock_user

        # Call the view
        response = test_view(self, request)

        # Check that no activity was recorded
        self.assertEqual(UserActivity.objects.count(), 0)

        # Check that the response was returned correctly
        self.assertEqual(response.content, b"Test response")

    @patch('common.utils.activity_tracking.UserActivity.objects.create')
    def test_track_activity_exception_handling(self, mock_create):
        """Test that exceptions in activity tracking don't affect the response."""
        # Mock UserActivity.objects.create to raise an exception
        mock_create.side_effect = Exception("Test exception")

        # Define a function-based view with the decorator
        @track_activity("User viewed test page")
        def test_view(self, request):
            return HttpResponse("Test response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Capture print output to check error logging
        with patch('builtins.print') as mock_print:
            # Call the view
            response = test_view(self, request)

            # Check that the error was logged
            mock_print.assert_called()
            # Instead of checking the specific message format, just verify it was called
            self.assertTrue(mock_print.called)

        # Check that the response was returned correctly despite the exception
        self.assertEqual(response.content, b"Test response")

    def test_track_activity_multiple_calls(self):
        """Test multiple calls to views with track_activity decorator."""
        # Define two function-based views with the decorator
        @track_activity("User viewed first page")
        def first_view(self, request):
            return HttpResponse("First response")

        @track_activity("User viewed second page")
        def second_view(self, request):
            return HttpResponse("Second response")

        # Create a request with an authenticated user
        request = self.factory.get('/test/')
        request.user = self.user

        # Call both views
        first_view(self, request)
        second_view(self, request)

        # Check that both activities were recorded
        self.assertEqual(UserActivity.objects.count(), 2)
        
        activities = UserActivity.objects.order_by('description')
        self.assertEqual(activities[0].description, "User viewed first page")
        self.assertEqual(activities[1].description, "User viewed second page")

    def test_track_activity_preserves_function_metadata(self):
        """Test that track_activity preserves function metadata."""
        # Define a function with docstring and attributes
        @track_activity("User viewed test page")
        def test_view(self, request):
            """Test view docstring."""
            return HttpResponse("Test response")
        
        test_view.custom_attr = "custom value"

        # Check that the docstring and attributes are preserved
        self.assertEqual(test_view.__doc__, "Test view docstring.")
        self.assertEqual(test_view.custom_attr, "custom value")
        self.assertEqual(test_view.__name__, "test_view")

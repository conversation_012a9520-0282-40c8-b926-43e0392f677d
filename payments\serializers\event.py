"""
Serializers for event registration models.
"""
from rest_framework import serializers
from core.models import EventRegistration
from payments.serializers.payment import MemberSerializer, PaymentSerializer


class EventRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for EventRegistration model"""
    member_details = MemberSerializer(source='member', read_only=True)
    group_members_details = MemberSerializer(source='group_members', many=True, read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)

    # These fields can be calculated or provided
    base_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    guest_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)

    class Meta:
        model = EventRegistration
        fields = [
            'id', 'member', 'member_details', 'first_name', 'last_name', 'title',
            'fire_department', 'address', 'city', 'state', 'zipcode', 'phone',
            'email', 'invoice_number', 'registration_type', 'number_of_participants',
            'number_of_guests', 'registration_date', 'notes', 'base_amount',
            'guest_amount', 'total_amount', 'payment_status', 'extra_participants',
            'group_registration', 'group_members', 'group_members_details', 'payments',
            'event'
        ]
        read_only_fields = ['invoice_number', 'registration_date']

    def validate(self, data):
        """Ensure base_amount, guest_amount, and total_amount are provided"""
        # If base_amount is not provided, calculate it based on registration type
        if 'base_amount' not in data:
            registration_type = data.get('registration_type')
            if data.get('group_registration', False):
                # Group registration rate
                num_participants = data.get('number_of_participants', 1)
                data['base_amount'] = 125.00 * num_participants
            elif registration_type == 'LATE':
                # Late registration rate
                data['base_amount'] = 115.00
            elif registration_type == 'NORMAL':
                # Normal registration rate
                data['base_amount'] = 100.00
            else:
                data['base_amount'] = 0.00

        # If guest_amount is not provided, calculate it based on number of guests
        if 'guest_amount' not in data:
            num_guests = data.get('number_of_guests', 0)
            data['guest_amount'] = 75.00 * num_guests  # $75 per guest

        # If total_amount is not provided, calculate it from base_amount and guest_amount
        if 'total_amount' not in data:
            data['total_amount'] = data['base_amount'] + data['guest_amount']

        return data

    def create(self, validated_data):
        """Create event registration with group members if provided"""
        group_members = validated_data.pop('group_members', [])

        # Create the event registration
        event_registration = EventRegistration.objects.create(**validated_data)

        # Add group members if this is a group registration
        if validated_data.get('group_registration', False) and group_members:
            event_registration.group_members.set(group_members)

        return event_registration

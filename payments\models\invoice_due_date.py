"""
Invoice due date model for handling payment due dates.
"""
from django.db import models
from simple_history.models import HistoricalRecords


class InvoiceDueDate(models.Model):
    """
    Model to store invoice due dates
    """
    due_date = models.DateField()
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    history = HistoricalRecords()

    def __str__(self):
        """Return string representation of InvoiceDueDate"""
        return f"Invoice Due Date: {self.due_date}" 